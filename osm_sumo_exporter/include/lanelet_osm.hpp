#include <lanelet2_core/LaneletMap.h>
#include <lanelet2_routing/RoutingGraph.h>
#include "lanelet2_core/geometry/LineString.h"
#include "lanelet2_traffic_rules/TrafficRulesFactory.h"
#include <set>

const double kJunctionDistanceThrehold = 25.0;
struct SingleDirectionLaneletGroup {
    std::vector<int> lanelet_ids;
    int left_lane_id;
    int left_linestring_id;
    double width;
};
struct LaneletGroup {
    std::vector<int> forward_lanelet_ids;
    std::vector<int> backward_lanelet_ids;
    int forward_lane_id;
    int backward_lane_id;
    double width;
    int center_linestring_id;

    std::pair<double, double> start_point;
    std::pair<double, double> end_point;

    std::string start_junction_name;
    std::string end_junction_name;
    std::string road_name;
};

struct JunctionGroup {
    // static int junction_id;
    std::pair<double, double> point;
    std::vector<std::pair<std::string, std::pair<double, double>>> road_ids;
    std::string junction_name;
};

class LaneletOSM
{
public:
    LaneletOSM(const lanelet::LaneletMapPtr &map)
    {
        map_ = map;
        traffic_rules_ptr_ = lanelet::traffic_rules::TrafficRulesFactory::create(lanelet::Locations::Germany,
                                                                                 lanelet::Participants::Vehicle);
        routing_graph_ = lanelet::routing::RoutingGraph::build(*map_, *traffic_rules_ptr_);
    }
    ~LaneletOSM() {}
    bool process()
    {
        // 遍历所有的lanelet
        std::unordered_map<std::string, LaneletGroup> single_direction_lanelets;
        for (auto &lanelet : map_->laneletLayer) {
            if (proceesd_lanelets_.find(lanelet.id()) != proceesd_lanelets_.end()) {
                continue;
            }

            LaneletGroup lanelet_group;
            auto left_relations = routing_graph_->leftRelations(lanelet);
            auto right_relations = routing_graph_->rightRelations(lanelet);
            lanelet_group.forward_lanelet_ids.push_back(lanelet.id());
            proceesd_lanelets_.insert(lanelet.id());

            for (auto &relation : left_relations) {
                lanelet_group.forward_lanelet_ids.push_back(relation.lanelet.id());
                proceesd_lanelets_.insert(relation.lanelet.id());
            }
            for (auto &relation : right_relations) {
                lanelet_group.backward_lanelet_ids.push_back(relation.lanelet.id());
                proceesd_lanelets_.insert(relation.lanelet.id());
            }

            for (const auto &id : lanelet_group.forward_lanelet_ids) {
                auto ll = map_->laneletLayer.get(id);
                auto left = routing_graph_->left(ll);
                auto adjacent_left = routing_graph_->adjacentLeft(ll);
                if (!left && !adjacent_left) {
                    lanelet_group.forward_lane_id = id;
                    lanelet_group.center_linestring_id = ll.leftBound().id();
                    lanelet_group.width = lanelet::geometry::distance2d(ll.leftBound2d(), ll.rightBound2d());
                    lanelet_group.start_point = std::make_pair(ll.leftBound2d().front().basicPoint2d().x(),
                                                               ll.leftBound2d().front().basicPoint2d().y());
                    lanelet_group.end_point = std::make_pair(ll.leftBound2d().back().basicPoint2d().x(),
                                                             ll.leftBound2d().back().basicPoint2d().y());
                    lanelet_group.road_name = std::to_string(lanelet_group.center_linestring_id) + "_" +
                                              std::to_string(lanelet_group.forward_lane_id);
                }
            }
            single_direction_lanelets.insert({lanelet_group.road_name, lanelet_group});
        }
        for (const auto &ll : single_direction_lanelets) {
            std::cout << "single direction lanelet: " << ll.second.road_name << std::endl;
        }

        std::vector<std::string> single_lanelet_group_to_remove;
        for (auto it = single_direction_lanelets.begin(); it != single_direction_lanelets.end(); ++it) {
            if (std::find(single_lanelet_group_to_remove.begin(), single_lanelet_group_to_remove.end(),
                          it->second.road_name) != single_lanelet_group_to_remove.end()) {
                continue;
            }
            auto &cur_lanelet_group = (*it).second;
            auto it_find = std::find_if(
                single_direction_lanelets.begin(), single_direction_lanelets.end(), [cur_lanelet_group](const auto &group) {
                    return group.second.forward_lane_id != cur_lanelet_group.forward_lane_id &&
                           group.second.center_linestring_id == cur_lanelet_group.center_linestring_id;
                });
            if (it_find != single_direction_lanelets.end()) {
                LaneletGroup double_direction_lanelet_group;
                double_direction_lanelet_group.forward_lanelet_ids = cur_lanelet_group.forward_lanelet_ids;
                double_direction_lanelet_group.backward_lanelet_ids = it_find->second.forward_lanelet_ids;
                double_direction_lanelet_group.forward_lane_id = cur_lanelet_group.forward_lane_id;
                double_direction_lanelet_group.backward_lane_id = it_find->second.forward_lane_id;
                double_direction_lanelet_group.center_linestring_id = cur_lanelet_group.center_linestring_id;
                double_direction_lanelet_group.width = cur_lanelet_group.width;
                double_direction_lanelet_group.start_point = cur_lanelet_group.start_point;
                double_direction_lanelet_group.end_point = cur_lanelet_group.end_point;
                double_direction_lanelet_group.road_name =
                    std::to_string(cur_lanelet_group.center_linestring_id) + "_" +
                    std::to_string(double_direction_lanelet_group.forward_lane_id) + "_" +
                    std::to_string(double_direction_lanelet_group.backward_lane_id);
                lanelets_.push_back(double_direction_lanelet_group);

                single_lanelet_group_to_remove.push_back(cur_lanelet_group.road_name);
                single_lanelet_group_to_remove.push_back(it_find->second.road_name);
            }
        }

        for (auto &it : single_lanelet_group_to_remove) {
            single_direction_lanelets.erase(it);
        }
        for (auto &ll : single_direction_lanelets) {
            lanelets_.push_back(ll.second);
        }

        for (size_t i = 0; i < lanelets_.size(); ++i) {
            auto &cur_ll = lanelets_[i];

            if (cur_ll.start_junction_name.empty()) {
                JunctionGroup junction_group;
                junction_group.junction_name = "junction_" + std::to_string(junctions_.size());
                junction_group.road_ids.push_back(std::make_pair(cur_ll.road_name, cur_ll.start_point));
                cur_ll.start_junction_name = junction_group.junction_name;

                for (size_t j = i + 1; j < lanelets_.size(); ++j) {
                    auto &other_ll = lanelets_[j];
                    if (std::hypot(other_ll.start_point.first - cur_ll.start_point.first,
                                   other_ll.start_point.second - cur_ll.start_point.second) < kJunctionDistanceThrehold) {
                        junction_group.road_ids.push_back(std::make_pair(other_ll.road_name, other_ll.start_point));
                        other_ll.start_junction_name = junction_group.junction_name;
                        std::cout << "Junction:" << junction_group.junction_name << " cur road start: " << cur_ll.road_name
                                  << " other road start: " << other_ll.road_name << std::endl;
                    }
                    if (std::hypot(other_ll.end_point.first - cur_ll.start_point.first,
                                   other_ll.end_point.second - cur_ll.start_point.second) < kJunctionDistanceThrehold) {
                        junction_group.road_ids.push_back(std::make_pair(other_ll.road_name, other_ll.end_point));
                        other_ll.end_junction_name = junction_group.junction_name;
                        std::cout << "Junction:" << junction_group.junction_name << " cur road start: " << cur_ll.road_name
                                  << " other road end: " << other_ll.road_name << std::endl;
                    }
                }
                std::pair<double, double> point(0, 0);
                for (const auto &pt : junction_group.road_ids) {
                    point.first += pt.second.first;
                    point.second += pt.second.second;
                }
                point.first /= junction_group.road_ids.size();
                point.second /= junction_group.road_ids.size();
                junction_group.point = point;
                junctions_.push_back(junction_group);
            }
            if (cur_ll.end_junction_name.empty()) {
                JunctionGroup junction_group;
                junction_group.junction_name = "junction_" + std::to_string(junctions_.size());
                junction_group.road_ids.push_back(std::make_pair(cur_ll.road_name, cur_ll.end_point));
                cur_ll.end_junction_name = junction_group.junction_name;

                for (size_t j = i + 1; j < lanelets_.size(); ++j) {
                    auto &other_ll = lanelets_[j];
                    if (std::hypot(other_ll.start_point.first - cur_ll.end_point.first,
                                   other_ll.start_point.second - cur_ll.end_point.second) < kJunctionDistanceThrehold) {
                        junction_group.road_ids.push_back(std::make_pair(other_ll.road_name, other_ll.start_point));
                        other_ll.start_junction_name = junction_group.junction_name;
                        std::cout << "Junction:" << junction_group.junction_name
                                  << " cur road end: " << cur_ll.road_name << " other road start: " << other_ll.road_name

                                  << std::endl;
                    }
                    if (std::hypot(other_ll.end_point.first - cur_ll.end_point.first,
                                   other_ll.end_point.second - cur_ll.end_point.second) < kJunctionDistanceThrehold) {
                        junction_group.road_ids.push_back(std::make_pair(other_ll.road_name, other_ll.end_point));
                        other_ll.end_junction_name = junction_group.junction_name;
                        std::cout << "Junction:" << junction_group.junction_name << " cur road end: " << cur_ll.road_name
                                  << " other road end: " << other_ll.road_name << std::endl;
                    }
                }
                std::pair<double, double> point(0, 0);
                for (const auto &pt : junction_group.road_ids) {
                    point.first += pt.second.first;
                    point.second += pt.second.second;
                }
                point.first /= junction_group.road_ids.size();
                point.second /= junction_group.road_ids.size();
                junction_group.point = point;
                junctions_.push_back(junction_group);
            }
        }
        return true;
    }
    void print()
    {
        for (const auto &junction : junctions_) {
            std::cout << "junction: " << junction.junction_name << " point: " << junction.point.first << " "
                      << junction.point.second << " road_ids: ";
            for (const auto &road_id : junction.road_ids) {
                std::cout << road_id.first << " ";
            }
            std::cout << std::endl;
        }
        for (const auto &lanelet : lanelets_) {
            std::cout << "lanelet: " << lanelet.road_name << " start_junction: " << lanelet.start_junction_name
                      << " end_junction: " << lanelet.end_junction_name << std::endl;
        }
    }

    void exportToSUMO(const std::string &file_dir)
    {
        
    }

private:
    lanelet::LaneletMapPtr map_;
    lanelet::traffic_rules::TrafficRulesPtr traffic_rules_ptr_;
    lanelet::routing::RoutingGraphPtr routing_graph_;
    std::vector<LaneletGroup> lanelets_;
    std::vector<JunctionGroup> junctions_;
    std::set<int> proceesd_lanelets_;
};