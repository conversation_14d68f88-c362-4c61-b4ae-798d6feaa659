#include <lanelet2_core/LaneletMap.h>
#include <lanelet2_io/Io.h>
#include "had_map_utils/local_osm_parser.hpp"
#include "had_map_utils/had_map_parse.hpp"
#include "lanelet_osm.hpp"

int main(int argc, char **argv)
{
    std::string map_filename = argv[1];
    std::shared_ptr<lanelet::LaneletMap> map_;
    bool ret = uslam::common::had_map_utils::osmMapParse(map_filename, true, map_);

    if (!ret) {
        std::cerr << "Failed to parse map file: " << map_filename << std::endl;
        return 1;
    }
    LaneletOSM lanelet_osm(map_);
    lanelet_osm.process();
    lanelet_osm.print();

    return 0;
}