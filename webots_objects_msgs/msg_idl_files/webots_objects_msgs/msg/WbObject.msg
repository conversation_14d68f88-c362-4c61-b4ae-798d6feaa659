# action 
uint8 ADD=0
uint8 MODIFY=0
uint8 DELETE=2
uint8 DELETEALL=3

# type
uint8 STATIC = 1
uint8 DYNAMIC_CIRCLE = 2
uint8 DYNAMIC_POLYGON = 3
uint8 DYNAMIC_LINES = 4
uint8 DYNAMIC_VEHICLE = 5
uint8 CAR = 6
uint8 PEDESTRIAN = 7
uint8 TRAILER = 8

uint8 action
uint8 type
uint8 specific_type
int32 id
string prefix
geometry_msgs/Vector3 size
geometry_msgs/Pose pose
float32 velocity
float32 radius
geometry_msgs/Point32[] points