---
include:
  - project: "pub/ci_templates"
    file: "/cpp.lint.gitlab-ci.yml"
  - project: "pub/ci_templates"
    file: "/docker.build/define.gitlab-ci.yml"


stages:
  - lint
  - build

build:ubuntu20_merge_requests:
  variables:
    PLATFORM: linux/amd64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/image/uslam4_base:ubuntu20.04
    CROSS_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/crosscompile:ubuntu20.04
    ENV_File: env/20.env
    TAG: ubuntu20.04

  extends:
    - .merge_requests_job

build:ubuntu20_main:
  variables:
    PLATFORM: linux/amd64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/image/uslam4_base:ubuntu20.04
    CROSS_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/crosscompile:ubuntu20.04
    TAG: ubuntu20.04
  extends:
    - .main_job
  # rules:
  #   - if: $CI_COMMIT_BRANCH == "main"


build:orin_devel_merge_requests:
  variables:
    PLATFORM: linux/arm64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/image/uslam4_base:orin-devel
    CROSS_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/crosscompile:ubuntu20.04
    ENV_File: env/orin.env
    TAG: orin-devel

  extends:
    - .merge_requests_job

build:orin_devel_main:
  variables:
    PLATFORM: linux/arm64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/image/uslam4_base:orin-devel
    CROSS_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/crosscompile:ubuntu20.04
    ENV_File: env/orin.env 
    TAG: orin-devel
  extends:
    - .main_job
  # rules:
  #   - if: $CI_COMMIT_BRANCH == "main"


build:latest_main:
  variables:
    SRC_TAG: ubuntu20.04
    DES_TAG: latest
  extends:
    - .tag_job
  needs:
    - build:ubuntu20_main
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

