#!/bin/bash
set -e

if [ "$TARGETPLATFORM" = "linux/amd64" ]; then
    export TARGET_ARCH=x86_64
elif [ "$TARGETPLATFORM" = "linux/arm64" ]; then
    export TARGET_ARCH=aarch64
else
    exit -1
fi

export TARGET_TRIPLE=$TARGET_ARCH-linux-gnu
export CC=/usr/bin/$TARGET_TRIPLE-gcc
export CXX=/usr/bin/$TARGET_TRIPLE-g++
export CROSS_COMPILE=/usr/bin/$TARGET_TRIPLE-
export SYSROOT=/sysroot
export SRC_PATH=/opt/src
export INSTALL_PATH=/opt/uads/uads_common
export PKG_CONFIG_DIR=""
export PKG_CONFIG_PATH="${SYSROOT}/usr/lib/pkgconfig:${SYSROOT}/usr/share/pkgconfig:${SYSROOT}/usr/lib/${TARGET_TRIPLE}/pkgconfig"
export PKG_CONFIG_SYSROOT_DIR=$SYSROOT
export UAUTO_PATH=/opt/uauto
export UBT_3RD_PATH=/opt/ubt_3rdparty

sudo mkdir -p $INSTALL_PATH
sudo chown -R 1000:1000 $INSTALL_PATH
. /opt/ubt_3rdparty/local_setup.bash
. /opt/uauto/local_setup.bash

mkdir -p src_common && vcs import src_common < repos.yml

colcon build --merge-install \
    --install-base $INSTALL_PATH \
    --cmake-force-configure \
    --cmake-args \
        -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON \
        -DCMAKE_TOOLCHAIN_FILE="$(pwd)/toolchain.cmake"
