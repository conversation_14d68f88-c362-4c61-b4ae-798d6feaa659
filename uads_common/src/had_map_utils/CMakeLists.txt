cmake_minimum_required(VERSION 3.10 FATAL_ERROR)
project(had_map_utils)

# 关闭Eigen 部分编译警告
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-deprecated-copy")

find_package(ament_cmake REQUIRED)
find_package(lanelet2_core REQUIRED)
find_package(lanelet2_io REQUIRED)
find_package(lanelet2_projection REQUIRED)
find_package(lanelet2_routing REQUIRED)
find_package(uautopilot_msgs REQUIRED)
find_package(CGAL REQUIRED)
find_package(uauto REQUIRED)

add_library(${PROJECT_NAME} SHARED
    src/had_map_computation.cpp 
    src/had_map_conversion.cpp 
    src/had_map_query.cpp
    src/had_map_utils.cpp 
    src/had_map_visualization.cpp
    src/had_map_parse.cpp
    src/local_osm_parser.cpp
    src/custom_regulatory_elements.cpp
)
target_include_directories(${PROJECT_NAME} PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include/>
)
target_link_libraries(${PROJECT_NAME} 
    CGAL::CGAL
    lanelet2_core::lanelet2_core
    lanelet2_io::lanelet2_io
    lanelet2_projection::lanelet2_projection
    lanelet2_routing::lanelet2_routing
    uautopilot_msgs::uautopilot_msgs
    ulog::ulog
)
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE ${EIGEN3_INCLUDE_DIRS} ${Boost_INCLUDE_DIRS})

install(DIRECTORY include/ DESTINATION include)

install(
  TARGETS ${PROJECT_NAME}
  EXPORT export_${PROJECT_NAME}
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES
  DESTINATION include)

ament_export_targets(export_${PROJECT_NAME} HAS_LIBRARY_TARGET)
ament_export_dependencies(uautopilot_msgs lanelet2_core lanelet2_io lanelet2_projection lanelet2_routing CGAL)

ament_package()
