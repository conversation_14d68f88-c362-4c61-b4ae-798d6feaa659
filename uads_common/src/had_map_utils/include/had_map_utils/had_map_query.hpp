// Copyright 2020 Tier IV, Inc
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef HAD_MAP_UTILS__HAD_MAP_QUERY_HPP_
#define HAD_MAP_UTILS__HAD_MAP_QUERY_HPP_

#include <lanelet2_core/LaneletMap.h>
#include <lanelet2_core/primitives/LineString.h>
#include <lanelet2_core/primitives/Point.h>
#include <lanelet2_core/primitives/Polygon.h>
#include <lanelet2_core/utility/Units.h>
#include <lanelet2_routing/RoutingGraph.h>

#include <cmath>
#include <memory>
// #include "had_map_utils/visibility_control.hpp"

namespace uslam {
namespace common {
namespace had_map_utils {

lanelet::Areas getAreaLayer(const lanelet::LaneletMapPtr ll_map);

lanelet::Areas subtypeAreas(const lanelet::Areas areas, const char subtype[]);

lanelet::Polygons3d typePolygons(const lanelet::Polygons3d polygons, const char type[]);

lanelet::Polygons3d getPolygonLayer(const lanelet::LaneletMapPtr ll_map);

lanelet::Polygons3d subtypePolygons(const lanelet::Polygons3d polygons, const char subtype[]);

lanelet::LineStrings3d getLineStringLayer(const lanelet::LaneletMapPtr ll_map);

lanelet::LineStrings3d subtypeLineStrings(const lanelet::LineStrings3d linestrings, const char subtype[]);

lanelet::ConstLanelets getConstLaneletLayer(const std::shared_ptr<lanelet::LaneletMap> &ll_map);

lanelet::Lanelets getLaneletLayer(const std::shared_ptr<lanelet::LaneletMap> &ll_map);

lanelet::Lanelets subtypeLaneletLayer(const lanelet::Lanelets lls, const char subtype[]);
lanelet::ConstLanelets subtypeLaneletLayer(const lanelet::ConstLanelets lls, const char subtype[]);

lanelet::ConstLanelets getAllNeighbors(const lanelet::routing::RoutingGraphPtr &graph, const lanelet::ConstLanelet &lanelet);

lanelet::ConstLanelets getAllNeighborsLeft(const lanelet::routing::RoutingGraphPtr &graph,
                                           const lanelet::ConstLanelet &lanelet);
lanelet::ConstLanelets getAllNeighborsRight(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet);
lanelet::ConstLanelets getAllChangeableLane(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet);
lanelet::ConstLanelets getAllChangeableLeft(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet);
lanelet::ConstLanelets getAllChangeableRight(const lanelet::routing::RoutingGraphPtr &graph,
                                             const lanelet::ConstLanelet &lanelet);
}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam

#endif  // HAD_MAP_UTILS__HAD_MAP_QUERY_HPP_
