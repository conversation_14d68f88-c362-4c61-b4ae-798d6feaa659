// Copyright 2020 Tier IV, Inc
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef HAD_MAP_UTILS__HAD_MAP_VISUALIZATION_HPP_
#define HAD_MAP_UTILS__HAD_MAP_VISUALIZATION_HPP_

#include <lanelet2_core/LaneletMap.h>

#include <chrono>
#include <cmath>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "builtin_interfaces/msg/Time.h"
#include "geometry_msgs/msg/Point.h"
#include "geometry_msgs/msg/Point32.h"
#include "geometry_msgs/msg/Polygon.h"
#include "unav/time.h"
#include "visualization_msgs/msg/Marker.h"
#include "visualization_msgs/msg/MarkerArray.h"

// #include "had_map_utils/visibility_control.hpp"

namespace uslam {
namespace common {
namespace had_map_utils {
/**
 * \brief Set set rgba information to a Color Object
 * \param[out] cl color object to be set
 * \param r red value
 * \param g green value
 * \param b blue value
 * \param a alpha value
 */
void setColor(std_msgs::msg::ColorRGBA *cl, float r, float g, float b, float a);

/**
 * \brief Set the header information to a marker object
 * \param m input marker
 * \param id id of the marker
 * \param t timestamp of the marker
 * \param frame_id frame of the marker
 * \param ns namespace of the marker
 * \param c color of the marker
 * \param action action used to visualize the marker
 * \param type type of the marker
 * \param scale scale of the marker
 * \return visualization_msgs::msg::Marker
 */
void setMarkerHeader(visualization_msgs::msg::Marker *m, int32_t id, const builtin_interfaces::msg::Time &t,
                     const std::string &frame_id, const std::string &ns, const std_msgs::msg::ColorRGBA &c,
                     int32_t action, int32_t type, float scale);

/**
 * \brief creates marker with type LINE_STRIP from a lanelet::LineString3d object
 * \param t timestamp set to the marker
 * \param ls input linestring
 * \param frame_id frame id set to the marker
 * \param ns namespace set to the marker
 * \param c color of the marker
 * \param lss linestrip scale (i.e. width)
 * \return created visualization_msgs::msg::Marker
 */
visualization_msgs::msg::Marker lineString2Marker(const builtin_interfaces::msg::Time &t, const lanelet::LineString3d &ls,
                                                  const std::string &frame_id, const std::string &ns,
                                                  const std_msgs::msg::ColorRGBA &c, float lss, int extern_id = -1);

/**
 * \brief creates marker with type LINE_STRIP from a lanelet::ConstLineString3d object
 * \param t timestamp set to the marker
 * \param ls input linestring
 * \param frame_id frame id set to the marker
 * \param ns namespace set to the marker
 * \param c color of the marker
 * \param lss linestrip scale (i.e. width)
 * \return created visualization_msgs::msg::Marker
 */
visualization_msgs::msg::Marker lineString2Marker(const builtin_interfaces::msg::Time &t,
                                                  const lanelet::ConstLineString3d &ls, const std::string &frame_id,
                                                  const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss,
                                                  int extern_id = -1);

/** 车道线方向转化为三角形 */
visualization_msgs::msg::Marker laneletsDirection2TriangleMarker(const builtin_interfaces::msg::Time &t,
                                                                 const lanelet::ConstLanelet &lanelet,
                                                                 const std::string &frame_id, const std::string &ns,
                                                                 const std_msgs::msg::ColorRGBA &c, float lss,
                                                                 int extern_id = -1);

visualization_msgs::msg::Marker string2Marker(const builtin_interfaces::msg::Time &t, const std::string string,
                                              const std::string &frame_id, const std::string &ns, const int id,
                                              const std_msgs::msg::ColorRGBA &c, const double size,
                                              const geometry_msgs::msg::Pose &pose);

/**
 * \brief converts lanelet::LineString into markers with type LINE_STRIP
 * \param t time set to returned marker message
 * \param ns namespace set to the marker
 * \param linestrings input linestring objects
 * \param c color of the marker
 * \return created visualization_msgs::msg::MarkerArray
 */
visualization_msgs::msg::MarkerArray lineStringsAsMarkerArray(const builtin_interfaces::msg::Time &t, const std::string &ns,
                                                              const lanelet::LineStrings3d &linestrings,
                                                              const std_msgs::msg::ColorRGBA &c);

/**
 * \brief converts outer bound of lanelet::Lanelet into markers with type LINE_STRIP
 * \param t time set to returned marker message
 * \param lanelets input lanelet objects
 * \param c color of the marker
 * \param viz_centerline option to add centerline to the marker array
 * \return created visualization_msgs::msg::MarkerArray
 */
visualization_msgs::msg::MarkerArray laneletsBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const lanelet::ConstLanelets &lanelets,
                                                                   const std_msgs::msg::ColorRGBA &c,
                                                                   const bool &viz_centerline);

visualization_msgs::msg::MarkerArray laneletsRegulatoryElementAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                            const lanelet::ConstLanelets &lanelets);

/**
 * \brief creates marker with type LINE_STRIP from a lanelet::BasicPolygon object
 * \param t timestamp set to the marker
 * \param line_id id set to the marker
 * \param pg input polygon
 * \param frame_id frame id set to the marker
 * \param ns namespace set to the marker
 * \param c color of the marker
 * \param lss linestrip scale (i.e. width)
 * \return created visualization_msgs::msg::Marker
 */
visualization_msgs::msg::Marker basicPolygon2Marker(const builtin_interfaces::msg::Time &t, int32_t line_id,
                                                    const lanelet::BasicPolygon3d &pg, const std::string &frame_id,
                                                    const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss);

/**
 * \brief converts outer bound of lanelet::Area into markers with type LINE_STRIP
 * \param t time set to returned marker message
 * \param ns namespace set to the marker
 * \param areas input area objects
 * \param c color of the marker
 * \return created visualization_msgs::msg::MarkerArray
 */
visualization_msgs::msg::MarkerArray areasBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                const std::string &ns, const lanelet::Areas &areas,
                                                                const std_msgs::msg::ColorRGBA &c);

/**
 * \brief converts outer bound of lanelet::Polygon into markers with type LINE_STRIP
 * \param t Time set to returned marker message
 * \param ns namespace set to the marker
 * \param polygons input polygons
 * \param c color of the marker
 * \return created visualization_msgs::msg::MarkerArray
 */
visualization_msgs::msg::MarkerArray polygonsBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const std::string &ns,
                                                                   const lanelet::Polygons3d &polygons,
                                                                   const std_msgs::msg::ColorRGBA &c, float lss = 0.1f);

/**
 * \brief creates marker with type LINE_STRIP from a bounding box
 * \param t Time set to returned marker message
 * \param line_id id set to marker
 * \param lower lower bound of the bounding box with length 3(x,y,z)
 * \param upper upper bound of the bounding box with length 3(x,y,z)
 * \param frame_id frame id set to the marker
 * \param ns namespace set to the marker
 * \param c color of the marker
 * \param lss linestrip scale (i.e. width)
 * \return created visualization_msgs::msg::Marker
 */

visualization_msgs::msg::MarkerArray polygonsTextAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                               const std::string &ns, const lanelet::Polygons3d &polygons,
                                                               const std_msgs::msg::ColorRGBA &c);

visualization_msgs::msg::Marker bbox2Marker(const builtin_interfaces::msg::Time &t, int32_t &line_id,
                                            const double lower[], const double upper[], const std::string &frame_id,
                                            const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss);

/**
 * \brief creates marker array from bounding box
 * \param t Time set to returned marker message
 * \param ns Namespace set to returned marker message
 * \param upper upper bound of the bounding box with length 3(x,y,z)
 * \param lower lower bound of the bounding box with length 3(x,y,z)
 * \param c Color of the marker array
 * \return created visualization_msgs::msg::MarkerArray
 */
visualization_msgs::msg::MarkerArray boundingBoxAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                              const std::string &ns, const double upper[],
                                                              const double lower[], const std_msgs::msg::ColorRGBA &c);

/**
 * \brief converts area enclosed by lanelet::Lanelet into list of triangles.
 * \param ll input lanelet
 * \return result of triangulation
 */
std::vector<geometry_msgs::msg::Polygon> lanelet2Triangle(const lanelet::ConstLanelet &ll);

/**
 * \brief converts area enclosed by geometry_msg::msg::Polygon into list of triangles.
 * \param polygon input polygon
 * \return result of triangulation
 */
std::vector<geometry_msgs::msg::Polygon> polygon2Triangle(const geometry_msgs::msg::Polygon &polygon);

/**
 * \brief converts lanelet::Area into geometry_msgs::msg::Polygon type
 * \param area input area
 * \return converted geometry_msgs::msg::Polygon
 */
geometry_msgs::msg::Polygon area2Polygon(const lanelet::ConstArea &area);

/**
 * \brief converts lanelet::Lanelet into geometry_msgs::msg::Polygon type
 * \param ll input lanelet
 * \return converted geometry_msgs::msg::Polygon
 */
geometry_msgs::msg::Polygon lanelet2Polygon(const lanelet::ConstLanelet &ll);

/**
 * \brief converts bounded area by lanelet::Lanelet into triangle markers
 * \param t Time set to returned marker message
 * \param ns Namespace set to returned marker message
 * \param lanelets input lanelet::Lanelet
 * \param c Color of the marker array
 * \return Converted triangle markers enclosed by the Lanelet
 */
visualization_msgs::msg::MarkerArray laneletsAsTriangleMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const std::string &ns,
                                                                   const lanelet::ConstLanelets &lanelets,
                                                                   const std_msgs::msg::ColorRGBA &c, bool show_id = false);

/**
 * \brief converts bounded area by lanelet::Lanelet into triangle markers
 * \param t Time set to returned marker message
 * \param ns Namespace set to returned marker message
 * \param lanelets input lanelet::Lanelet
 * \param indoor_c Indoor color of the marker array
 * \param outdoor_c Outdoor color of the marker array
 * \return Converted triangle markers enclosed by the Lanelet
 */
visualization_msgs::msg::MarkerArray laneletsAsTriangleMarkerArray(
    const builtin_interfaces::msg::Time &t, const std::string &ns, const lanelet::ConstLanelets &lanelets,
    const std_msgs::msg::ColorRGBA &indoor_c, const std_msgs::msg::ColorRGBA &outdoor_c, bool show_id = false);

/**
 * \brief converts bounded area by lanelet::Area into triangle markers
 * \param t Time set to returned marker message
 * \param ns Namespace set to returned marker message
 * \param areas input lanelet::Area objects
 * \param c Color of the marker array
 * \return Converted triangle markers enclosed by the area
 */
visualization_msgs::msg::MarkerArray areasAsTriangleMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                const std::string &ns, const lanelet::Areas &areas,
                                                                const std_msgs::msg::ColorRGBA &c);

// bzj add
void insertMarkerArray(visualization_msgs::msg::MarkerArray &a1, const visualization_msgs::msg::MarkerArray &a2);

// bzj add
visualization_msgs::msg::MarkerArray getLanelet2MapMarkerArray(const lanelet::LaneletMapPtr &map);

geometry_msgs::msg::Polygon basicPolygon3dToMsgPolygon(const lanelet::BasicPolygon3d &basic_polygon);

}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam

#endif  // HAD_MAP_UTILS__HAD_MAP_VISUALIZATION_HPP_
