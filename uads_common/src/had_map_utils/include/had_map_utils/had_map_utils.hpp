// Copyright 2020 Tier IV, Inc
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef HAD_MAP_UTILS__HAD_MAP_UTILS_HPP_
#define HAD_MAP_UTILS__HAD_MAP_UTILS_HPP_

#include <lanelet2_core/LaneletMap.h>
#include <lanelet2_core/primitives/Point.h>
#include <lanelet2_core/utility/Units.h>

#include <cmath>
#include "geometry_msgs/msg/Pose.h"

namespace uslam {
namespace common {
namespace had_map_utils {

std::vector<double> calculateSegmentDistances(const lanelet::ConstLineString3d &line_string);
std::vector<double> calculateAccumulatedLengths(const lanelet::ConstLineString3d &line_string);
std::pair<size_t, size_t> findNearestIndexPair(const std::vector<double> &accumulated_lengths, const double target_length);
std::vector<lanelet::BasicPoint3d> resamplePoints(const lanelet::ConstLineString3d &line_string, const int32_t num_segments);
void overwriteLaneletsCenterline(lanelet::LaneletMapPtr lanelet_map, const bool force_overwrite);
lanelet::LineString3d generateFineCenterline(const lanelet::ConstLanelet &lanelet_obj, const double resolution);
bool isInLanelet(const double pose_x, const double pose_y, const lanelet::ConstLanelet &lanelet, const double radius);

// 获取某线段上离目标点最近的一截
lanelet::ConstLineString3d getClosestSegment(const lanelet::BasicPoint2d &search_pt,
                                             const lanelet::ConstLineString3d &linestring);

// 获取车道上离目标点最近的位置的角度
double getLaneletAngle(const lanelet::ConstLanelet &lanelet, const lanelet::Point2d &search_point,
                       lanelet::Point2d *nearst_point = nullptr);

bool getClosestLanelet(const lanelet::ConstLanelets &lanelets, const geometry_msgs::msg::Pose &search_pose,
                       lanelet::ConstLanelet *closest_lanelet_ptr);

lanelet::ArcCoordinates getArcCoordinates(const lanelet::ConstLanelets &lanelet_sequence,
                                          const geometry_msgs::msg::Pose &pose);
}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam

#endif  // HAD_MAP_UTILS__HAD_MAP_UTILS_HPP_
