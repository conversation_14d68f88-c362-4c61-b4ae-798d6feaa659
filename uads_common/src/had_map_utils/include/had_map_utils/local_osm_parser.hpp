// Copyright 2015-2019 Autoware Foundation. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Authors: <AUTHORS>

#ifndef LANELET2_EXTENSION__IO__AUTOWARE_OSM_PARSER_HPP_
#define LANELET2_EXTENSION__IO__AUTOWARE_OSM_PARSER_HPP_

// NOLINTBEGIN(readability-identifier-naming)

#include <lanelet2_io/io_handlers/OsmHandler.h>
#include <lanelet2_io/io_handlers/OsmFile.h>
#include <lanelet2_io/Projection.h>
#include <memory>
#include <string>

namespace lanelet::projection {

class LocalProjector : public Projector
{
public:
    LocalProjector() : Projector(Origin(GPSPoint{})) {}

    BasicPoint3d forward(const GPSPoint &gps) const override  // NOLINT
    {
        return BasicPoint3d{gps.lat, gps.lon, gps.ele};
    }

    GPSPoint reverse(const BasicPoint3d &point) const override { return GPSPoint{point.x(), point.y(), point.z()}; }
};

}  // namespace lanelet::projection

namespace lanelet::io_handlers {
class LocalOsmParser : public OsmParser
{
public:
    using OsmParser::OsmParser;

    /**
     * [parse parse osm file to laneletMap. It is generally same as default
     * OsmParser, but it will overwrite x and y value with local_x and local_y
     * tags if present]
     * @param  filename [path to osm file]
     * @param  errors   [any errors caught during parsing]
     * @return          [returns LaneletMap]
     */
    std::unique_ptr<LaneletMap> parse(const std::string &filename, ErrorMessages &errors) const override;

    /**
     * [parseVersions parses MetaInfo tags from osm file]
     * @param filename       [path to osm file]
     * @param format_version [parsed information about map format version]
     * @param map_version    [parsed information about map version]
     */
    static void parseVersions(const std::string &filename, std::string *format_version, std::string *map_version);

    static constexpr const char *extension() { return ".osm"; }

    static constexpr const char *name() { return "local_osm_handler"; }
};

class LocalOsmWriter : public OsmWriter
{
public:
    using OsmWriter::OsmWriter;

    /**
     * [write writes laneletMap to osm file. It is generally same as default
     * OsmWriter, but it will write x and y value with local_x and local_y
     * tags if present]
     * @param  filename [path to osm file]
     * @param  map      [laneletMap to write]
     * @param  errors   [any errors caught during writing]
     * @return          [returns true if writing was successful]
     */
    void write(const std::string &filename, const LaneletMap &map, ErrorMessages &errors,
               const io::Configuration &params = io::Configuration()) const override;

    static constexpr const char *extension() { return ".osm"; }

    static constexpr const char *name() { return "local_osm_handler"; }
};

}  // namespace lanelet::io_handlers

// NOLINTEND(readability-identifier-naming)

#endif  // LANELET2_EXTENSION__IO__AUTOWARE_OSM_PARSER_HPP_
