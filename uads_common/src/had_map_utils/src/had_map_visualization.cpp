// Copyright 2020 TierIV, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Co-developed by Tier IV, Inc. and Apex.AI, Inc.

// lint -e537 pclint vs cpplint NOLINT

#include "had_map_utils/had_map_visualization.hpp"

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#include <lanelet2_core/primitives/BasicRegulatoryElements.h>
#include <lanelet2_io/io_handlers/Serialize.h>
#include <lanelet2_core/geometry/LineString.h>
#pragma GCC diagnostic pop

#include <algorithm>
#include <boost/archive/binary_iarchive.hpp>
#include <boost/archive/binary_oarchive.hpp>
#include <limits>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "had_map_utils/custom_regulatory_elements.hpp"
#include "had_map_utils/had_map_query.hpp"
#include "ulog/ulog.h"
#include "utf/tf2/transform_datatypes.h"

namespace uslam {
namespace common {
namespace had_map_utils {

template <typename T>
bool exists(const std::unordered_set<T> &set, const T &element)
{
    return std::find(set.begin(), set.end(), element) != set.end();
}

// 求解两点之间中点沿垂直和水平方向平移一定距离的点
geometry_msgs::msg::Point translateMidpointPerpendicular(const double &p1x, const double &p1y, const double &p2x,
                                                         const double &p2y, const double &distance_ver,
                                                         const double &distance_hor)
{
    geometry_msgs::msg::Point output;
    // 计算中点
    std::pair<double, double> mid = {(p1x + p2x) / 2, (p1y + p2y) / 2};

    // 计算垂直单位向量
    double dx = p2y - p1y;
    double dy = p1x - p2x;
    double length = sqrt(dx * dx + dy * dy);
    std::pair<double, double> directionUnitVector = {dx / length, dy / length};
    std::pair<double, double> perpUnitVector = {directionUnitVector.second, -directionUnitVector.first};

    // 平移中点
    std::pair<double, double> newPoint = {
        mid.first + perpUnitVector.first * distance_hor + directionUnitVector.first * distance_ver,
        mid.second + perpUnitVector.second * distance_hor + directionUnitVector.second * distance_ver};
    output.x = newPoint.first;
    output.y = newPoint.second;
    output.z = 0.0;
    return output;
}

void setColor(std_msgs::msg::ColorRGBA *cl, float r, float g, float b, float a)
{
    cl->r = r;
    cl->g = g;
    cl->b = b;
    cl->a = a;
}

void setMarkerHeader(visualization_msgs::msg::Marker *m, int32_t id, const builtin_interfaces::msg::Time &t,
                     const std::string &frame_id, const std::string &ns, const std_msgs::msg::ColorRGBA &c,
                     int32_t action, int32_t type, float scale)
{
    m->header.frame_id = frame_id;
    m->header.stamp = t;
    m->ns = ns;
    m->action = action;
    m->type = type;

    m->id = id;
    m->pose.position.x = 0.0;
    m->pose.position.y = 0.0;
    m->pose.position.z = 0.0;
    m->pose.orientation.x = 0.0;
    m->pose.orientation.y = 0.0;
    m->pose.orientation.z = 0.0;
    m->pose.orientation.w = 1.0;
    m->scale.x = scale;
    m->scale.y = scale;
    m->scale.z = scale;
    m->color = c;
}

visualization_msgs::msg::Marker lineString2Marker(const builtin_interfaces::msg::Time &t, const lanelet::LineString3d &ls,
                                                  const std::string &frame_id, const std::string &ns,
                                                  const std_msgs::msg::ColorRGBA &c, float lss, int extern_id)
{
    visualization_msgs::msg::Marker line_strip;
    if (extern_id != -1) {
        setMarkerHeader(&line_strip, extern_id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                        visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);
    } else
        setMarkerHeader(&line_strip, static_cast<int32_t>(ls.id()), t, frame_id, ns, c,
                        visualization_msgs::msg::Marker_Constants::ADD,
                        visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);

    for (auto i = ls.begin(); i != ls.end(); i++) {
        geometry_msgs::msg::Point p;
        p.x = (*i).x();
        p.y = (*i).y();
        p.z = (*i).z();
        line_strip.points.push_back(p);
    }
    return line_strip;
}

visualization_msgs::msg::Marker lineString2Marker(const builtin_interfaces::msg::Time &t,
                                                  const lanelet::ConstLineString3d &ls, const std::string &frame_id,
                                                  const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss,
                                                  int extern_id)
{
    if (ls.hasAttribute("subtype") && ls.attribute("subtype").value() == "dashed") {
        visualization_msgs::msg::Marker line_list_marker;
        if (extern_id != -1) {
            setMarkerHeader(&line_list_marker, extern_id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                            visualization_msgs::msg::Marker_Constants::LINE_LIST, lss);
        } else {
            setMarkerHeader(&line_list_marker, static_cast<int32_t>(ls.id()), t, frame_id, ns, c,
                            visualization_msgs::msg::Marker_Constants::ADD,
                            visualization_msgs::msg::Marker_Constants::LINE_LIST, lss);
        }

        double length = lanelet::geometry::rangedLength(ls.begin(), ls.end());
        double step = 0.5;
        int step_count = length / step;
        if (step_count % 2 == 0) step_count++;
        lanelet::LineString3d new_line;
        for (int i = 0; i <= step_count; i++) {
            auto pt = lanelet::geometry::interpolatedPointAtDistance(ls, length * i / step_count);
            geometry_msgs::msg::Point p;
            p.x = pt.x();
            p.y = pt.y();
            p.z = pt.z();
            line_list_marker.points.push_back(p);
        }
        return line_list_marker;
    }

    visualization_msgs::msg::Marker line_strip;
    if (extern_id != -1) {
        setMarkerHeader(&line_strip, extern_id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                        visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);
    } else
        setMarkerHeader(&line_strip, static_cast<int32_t>(ls.id()), t, frame_id, ns, c,
                        visualization_msgs::msg::Marker_Constants::ADD,
                        visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);

    for (auto i = ls.begin(); i != ls.end(); i++) {
        geometry_msgs::msg::Point p;
        p.x = (*i).x();
        p.y = (*i).y();
        p.z = (*i).z();
        line_strip.points.push_back(p);
    }
    return line_strip;

    // if (ls.hasAttribute("subtype")) {
    //     if (ls.attribute("subtype").value() == "solid")
    //         marker_line_type = visualization_msgs::msg::Marker_Constants::LINE_STRIP;
    //     else
    //         marker_line_type = visualization_msgs::msg::Marker_Constants::LINE_LIST;
    // }
}

visualization_msgs::msg::Marker laneletsDirection2TriangleMarker(const builtin_interfaces::msg::Time &t,
                                                                 const lanelet::ConstLanelet &lanelet,
                                                                 const std::string &frame_id, const std::string &ns,
                                                                 const std_msgs::msg::ColorRGBA &c, float lss, int extern_id)
{
    visualization_msgs::msg::Marker line_strip;

    setMarkerHeader(&line_strip, static_cast<int32_t>(lanelet.id()), t, frame_id, ns, c,
                    visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::TRIANGLE_LIST, 1.0);

    lanelet::ConstLineString3d left_ls = lanelet.leftBound();
    lanelet::ConstLineString3d right_ls = lanelet.rightBound();
    lanelet::ConstLineString3d center_ls = lanelet.centerline();

    geometry_msgs::msg::Point point;
    point.x = left_ls.front().x();
    point.y = left_ls.front().y();
    point.z = left_ls.front().z();
    line_strip.points.push_back(point);
    line_strip.colors.push_back(c);

    point.x = right_ls.front().x();
    point.y = right_ls.front().y();
    point.z = right_ls.front().z();
    line_strip.points.push_back(point);
    line_strip.colors.push_back(c);

    const double forward_dis = std::min(1.0, static_cast<double>(lanelet::geometry::length(lanelet.centerline())));
    auto tri_pt = lanelet::geometry::interpolatedPointAtDistance(lanelet.centerline(), forward_dis);
    point.x = tri_pt.x();
    point.y = tri_pt.y();
    point.z = right_ls.front().z();
    line_strip.points.push_back(point);
    line_strip.colors.push_back(c);

    return line_strip;
}

visualization_msgs::msg::Marker string2Marker(const builtin_interfaces::msg::Time &t, const std::string string,
                                              const std::string &frame_id, const std::string &ns, const int id,
                                              const std_msgs::msg::ColorRGBA &c, const double size,
                                              const geometry_msgs::msg::Pose &pose)
{
    visualization_msgs::msg::Marker marker;
    setMarkerHeader(&marker, id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::TEXT_VIEW_FACING, size);
    marker.pose = pose;
    marker.text = string;
    return marker;
}

visualization_msgs::msg::MarkerArray lineStringsAsMarkerArray(const builtin_interfaces::msg::Time &t, const std::string &ns,
                                                              const lanelet::LineStrings3d &linestrings,
                                                              const std_msgs::msg::ColorRGBA &c)
{
    float lss = 0.1f;
    visualization_msgs::msg::MarkerArray marker_array;

    for (auto lsi = linestrings.begin(); lsi != linestrings.end(); lsi++) {
        lanelet::LineString3d ls = *lsi;
        visualization_msgs::msg::Marker line_strip = lineString2Marker(t, ls, "map", ns, c, lss);
        marker_array.markers.push_back(line_strip);
    }
    return marker_array;
}

visualization_msgs::msg::MarkerArray laneletsBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const lanelet::ConstLanelets &lanelets,
                                                                   const std_msgs::msg::ColorRGBA &c,
                                                                   const bool &viz_centerline)
{
    float lss = 0.1f;
    std::unordered_set<lanelet::Id> added;
    visualization_msgs::msg::MarkerArray marker_array;

    for (auto li = lanelets.begin(); li != lanelets.end(); li++) {
        lanelet::ConstLanelet lll = *li;
        lanelet::ConstLineString3d left_ls = lll.leftBound();
        lanelet::ConstLineString3d right_ls = lll.rightBound();
        lanelet::ConstLineString3d center_ls = lll.centerline();

        visualization_msgs::msg::Marker left_line_strip, right_line_strip, center_line_strip, direction_strip;
        if (!exists(added, left_ls.id())) {
            left_line_strip = lineString2Marker(t, left_ls, "map", "left_lane_bound", c, lss);
            marker_array.markers.push_back(left_line_strip);
            added.insert(left_ls.id());
        }
        if (!exists(added, right_ls.id())) {
            right_line_strip = lineString2Marker(t, right_ls, "map", "right_lane_bound", c, lss);
            marker_array.markers.push_back(right_line_strip);
            added.insert(right_ls.id());
        }
        if (viz_centerline && !exists(added, center_ls.id())) {
            if (center_ls.id() == 0) {
                center_line_strip = lineString2Marker(t, center_ls, "map", "center_lane_line", c,
                                                      std::max(lss * 0.1f, 0.01f), lll.id() * 10e4);
                marker_array.markers.push_back(center_line_strip);
            } else {
                center_line_strip =
                    lineString2Marker(t, center_ls, "map", "center_lane_line", c, std::max(lss * 0.1f, 0.01f));
                marker_array.markers.push_back(center_line_strip);
                added.insert(center_ls.id());
            }
        }
        direction_strip =
            laneletsDirection2TriangleMarker(t, lll, "map", "lanelet_direction", c, std::max(lss * 0.1f, 0.01f));
        marker_array.markers.push_back(direction_strip);
    }
    return marker_array;
}

visualization_msgs::msg::MarkerArray laneletsRegulatoryElementAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                            const lanelet::ConstLanelets &lanelets)
{
    std_msgs::msg::ColorRGBA color_speed_limit_line, color_traffic_line, color_traffic_string, color_detection_area;
    uslam::common::had_map_utils::setColor(&color_speed_limit_line, 1.0f, 1.0f, 0.1f, 1.0f);  // yellow
    uslam::common::had_map_utils::setColor(&color_traffic_line, 0.2f, 1.0f, 0.2f, 1.0f);      // light green
    uslam::common::had_map_utils::setColor(&color_traffic_string, 0.0f, 1.0f, 0.0f, 1.0f);    // green
    uslam::common::had_map_utils::setColor(&color_detection_area, 0.4f, 0.4f, 1.0f, 1.0f);    // blue

    float lss = 0.15f;
    std::unordered_set<lanelet::Id> added;
    visualization_msgs::msg::MarkerArray marker_array;

    for (auto li = lanelets.begin(); li != lanelets.end(); li++) {
        lanelet::ConstLanelet lll = *li;
        visualization_msgs::msg::Marker stop_line_strip;
        // 交通标志
        auto reg_elems = lll.regulatoryElementsAs<lanelet::TrafficSign>();
        for (auto &reg_elem : reg_elems) {
            // 减速带
            if (!reg_elem->refLines().empty() && reg_elem->type() == "speed_limit_sign" &&
                reg_elem->hasAttribute("speed_limit")) {
                ULOGI("Find speed_limit_sign id:%d speed:%f sign_id:%d", reg_elem->id(),
                      reg_elem->attribute("speed_limit").asDouble().get(),
                      reg_elem->trafficSigns().front().lineString()->id());
                for (auto &ref_line : reg_elem->refLines())
                    if (!exists(added, ref_line.id())) {
                        stop_line_strip =
                            lineString2Marker(t, ref_line, "map", "speed_limit_line", color_speed_limit_line, lss);
                        marker_array.markers.push_back(stop_line_strip);

                        if (reg_elem->trafficSigns().empty() || reg_elem->trafficSigns().front().isPolygon()) continue;

                        auto sign_line = reg_elem->trafficSigns().front().lineString().get();
                        geometry_msgs::msg::Pose line_pose;
                        line_pose.position =
                            translateMidpointPerpendicular(sign_line.front().x(), sign_line.front().y(),
                                                           sign_line.back().x(), sign_line.back().y(), 0.0, 0.0);
                        std::string str = reg_elem->attribute("speed_limit").value() + "m/s";
                        auto text_marker = string2Marker(t, str, "map", "speed_limit_line", sign_line.id(),
                                                         color_speed_limit_line, 3.0, line_pose);
                        marker_array.markers.push_back(text_marker);
                        added.insert(ref_line.id());
                    }
            }
        }

        // 交通灯
        auto reg_elems_light = lll.regulatoryElementsAs<lanelet::TrafficLight>();
        for (auto &reg_elem : reg_elems_light) {
            if (reg_elem->stopLine() && reg_elem->hasAttribute("device_id") && reg_elem->hasAttribute("location")) {
                ULOGI("Find traffic light id:%d device:%d location:%d", reg_elem->id(),
                      reg_elem->attribute("device_id").asInt().get(), reg_elem->attribute("location").asInt().get());
                auto stop_line = reg_elem->stopLine().get();
                if (!exists(added, stop_line.id())) {
                    stop_line_strip = lineString2Marker(t, stop_line, "map", "traffic_light", color_traffic_line, lss);
                    marker_array.markers.push_back(stop_line_strip);

                    if (reg_elem->trafficLights().empty() || reg_elem->trafficLights().front().isPolygon()) continue;

                    geometry_msgs::msg::Pose line_pose;
                    line_pose.position = translateMidpointPerpendicular(
                        stop_line.front().x(), stop_line.front().y(), stop_line.back().x(), stop_line.back().y(), 0.0, 3.0);

                    std::string location_str = "Unknown";
                    if (reg_elem->attribute("location").asInt().get() == 1)
                        location_str = "E";
                    else if (reg_elem->attribute("location").asInt().get() == 2)
                        location_str = "S";
                    else if (reg_elem->attribute("location").asInt().get() == 3)
                        location_str = "W";
                    else if (reg_elem->attribute("location").asInt().get() == 4)
                        location_str = "N";
                    std::string str = "TL-" + reg_elem->attribute("device_id").value() + "-" + location_str;
                    auto text_marker = string2Marker(t, str, "map", "traffic_light",
                                                     reg_elem->trafficLights().front().lineString()->id(),
                                                     color_traffic_string, 3.0, line_pose);
                    marker_array.markers.push_back(text_marker);
                    added.insert(reg_elem->stopLine()->id());
                }
            }
        }

        // 检测区域
        auto reg_elems_detection_area = lll.regulatoryElementsAs<lanelet::DetectionArea>();
        for (auto &reg_elem : reg_elems_detection_area) {
            for (const auto stop_line : reg_elem->stopLines()) {
                ULOGI("Find detection_area id:%d line_id:%d", reg_elem->id(), stop_line.id());
                // auto stop_line = reg_elem->stopLine();
                if (!exists(added, stop_line.id())) {
                    stop_line_strip = lineString2Marker(t, stop_line, "map", "detection_area", color_detection_area, lss);
                    marker_array.markers.push_back(stop_line_strip);
                }
            }
            for (auto &polygon : reg_elem->detectionAreas()) {
                if (!exists(added, polygon.id())) {
                    visualization_msgs::msg::Marker marker = basicPolygon2Marker(
                        t, polygon.id(), polygon.basicPolygon(), "map", "detection_area", color_detection_area, lss);
                    marker_array.markers.push_back(marker);
                }
            }
        }
    }
    return marker_array;
}

visualization_msgs::msg::Marker basicPolygon2Marker(const builtin_interfaces::msg::Time &t, int32_t line_id,
                                                    const lanelet::BasicPolygon3d &pg, const std::string &frame_id,
                                                    const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss)
{
    visualization_msgs::msg::Marker line_strip;
    setMarkerHeader(&line_strip, line_id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);

    for (auto i = pg.begin(); i != pg.end(); i++) {
        geometry_msgs::msg::Point p;
        p.x = (*i).x();
        p.y = (*i).y();
        p.z = (*i).z();
        line_strip.points.push_back(p);
    }
    geometry_msgs::msg::Point pb;
    auto i = pg.begin();
    pb.x = (*i).x();
    pb.y = (*i).y();
    pb.z = (*i).z();
    line_strip.points.push_back(pb);
    return line_strip;
}

visualization_msgs::msg::MarkerArray areasBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                const std::string &ns, const lanelet::Areas &areas,
                                                                const std_msgs::msg::ColorRGBA &c)
{
    int pg_count = 0;
    float lss = 0.1f;
    visualization_msgs::msg::MarkerArray marker_array;
    for (auto area : areas) {
        lanelet::CompoundPolygon3d cpg = area.outerBoundPolygon();
        lanelet::BasicPolygon3d bpg = cpg.basicPolygon();

        visualization_msgs::msg::Marker line_strip = basicPolygon2Marker(t, pg_count, bpg, "map", ns, c, lss);
        marker_array.markers.push_back(line_strip);
        pg_count++;
    }
    return marker_array;
}

visualization_msgs::msg::MarkerArray polygonsBoundaryAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const std::string &ns,
                                                                   const lanelet::Polygons3d &polygons,
                                                                   const std_msgs::msg::ColorRGBA &c, float lss)
{
    int32_t pg_count = 0;
    visualization_msgs::msg::MarkerArray marker_array;
    for (auto poly : polygons) {
        lanelet::BasicPolygon3d bpg = poly.basicPolygon();
        visualization_msgs::msg::Marker line_strip = basicPolygon2Marker(t, pg_count, bpg, "map", ns, c, lss);
        marker_array.markers.push_back(line_strip);
        pg_count++;
    }
    return marker_array;
}

visualization_msgs::msg::MarkerArray polygonsTextAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                               const std::string &ns, const lanelet::Polygons3d &polygons,
                                                               const std_msgs::msg::ColorRGBA &c)
{
    visualization_msgs::msg::MarkerArray marker_array;
    for (auto poly : polygons) {
        visualization_msgs::msg::Marker text_marker;
        if (poly.basicPolygon().size() > 1) {
            setMarkerHeader(&text_marker, poly.id(), t, "map", ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                            visualization_msgs::msg::Marker_Constants::TEXT_VIEW_FACING, 1.0);
            double pose_x = 0.0, pose_y = 0.0;
            for (int i = 0; i < poly.basicPolygon().size(); ++i) {
                pose_x += poly.basicPolygon()[i].x();
                pose_y += poly.basicPolygon()[i].y();
            }
            text_marker.pose.position.x = pose_x / (poly.basicPolygon().size());
            text_marker.pose.position.y = pose_y / (poly.basicPolygon().size());
            text_marker.text = ns + "_" + std::to_string(poly.id());
            marker_array.markers.push_back(text_marker);
        }
    }
    return marker_array;
}

visualization_msgs::msg::Marker bbox2Marker(const builtin_interfaces::msg::Time &t, int32_t line_id,
                                            const double lower[], const double upper[], const std::string &frame_id,
                                            const std::string &ns, const std_msgs::msg::ColorRGBA &c, float lss)
{
    visualization_msgs::msg::Marker line_strip;
    setMarkerHeader(&line_strip, line_id, t, frame_id, ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::LINE_STRIP, lss);

    geometry_msgs::msg::Point bl, br, tl, tr;
    bl.x = lower[0];
    bl.y = lower[0];
    bl.z = 0.0;
    br.x = upper[0];
    br.y = lower[0];
    br.z = 0.0;
    tl.x = lower[0];
    tl.y = upper[0];
    tl.z = 0.0;
    tr.x = upper[0];
    tr.y = upper[0];
    tr.z = 0.0;

    line_strip.points.push_back(bl);
    line_strip.points.push_back(br);
    line_strip.points.push_back(tr);
    line_strip.points.push_back(tl);
    line_strip.points.push_back(bl);
    return line_strip;
}

visualization_msgs::msg::MarkerArray boundingBoxAsMarkerArray(const builtin_interfaces::msg::Time &t,
                                                              const std::string &ns, const double upper[],
                                                              const double lower[], const std_msgs::msg::ColorRGBA &c)
{
    float lss = 0.2f;
    visualization_msgs::msg::MarkerArray marker_array;
    visualization_msgs::msg::Marker line_strip = bbox2Marker(t, 0, upper, lower, "map", ns, c, lss);
    marker_array.markers.push_back(line_strip);
    return marker_array;
}

geometry_msgs::msg::Point toGeomMsgPt(const geometry_msgs::msg::Point32 &src)
{
    geometry_msgs::msg::Point dst;
    dst.x = static_cast<float>(src.x);
    dst.y = static_cast<float>(src.y);
    dst.z = static_cast<float>(src.z);
    return dst;
}

geometry_msgs::msg::Point32 toGeomMsgPt32(const lanelet::BasicPoint3d &src)
{
    geometry_msgs::msg::Point32 dst;
    dst.x = static_cast<float>(src.x());
    dst.y = static_cast<float>(src.y());
    dst.z = static_cast<float>(src.z());
    return dst;
}
void adjacentPoints(const size_t i, const size_t N, const geometry_msgs::msg::Polygon poly,
                    geometry_msgs::msg::Point32 *p0, geometry_msgs::msg::Point32 *p1, geometry_msgs::msg::Point32 *p2)
{
    *p1 = poly.points[i];
    if (i == 0) {
        *p0 = poly.points[N - 1];
    } else {
        *p0 = poly.points[i - 1];
    }
    if (i < N - 1) {
        *p2 = poly.points[i + 1];
    } else {
        *p2 = poly.points[0];
    }
}

std::vector<geometry_msgs::msg::Polygon> lanelet2Triangle(const lanelet::ConstLanelet &ll)
{
    geometry_msgs::msg::Polygon ls_poly = lanelet2Polygon(ll);
    return polygon2Triangle(ls_poly);
}

std::vector<geometry_msgs::msg::Polygon> area2Triangle(const lanelet::Area &area)
{
    geometry_msgs::msg::Polygon ls_poly = area2Polygon(area);
    return polygon2Triangle(ls_poly);
}

// Is angle AOB less than 180?
// https://qiita.com/fujii-kotaro/items/a411f2a45627ed2f156e
bool isAcuteAngle(const geometry_msgs::msg::Point32 &vertex_a, const geometry_msgs::msg::Point32 &vertex_o,
                  const geometry_msgs::msg::Point32 &vertex_b)
{
    return (vertex_a.x - vertex_o.x) * (vertex_b.y - vertex_o.y) - (vertex_a.y - vertex_o.y) * (vertex_b.x - vertex_o.x) >= 0;
}

// https://qiita.com/fujii-kotaro/items/a411f2a45627ed2f156e
bool isWithinTriangle(const geometry_msgs::msg::Point32 &vertex_a, const geometry_msgs::msg::Point32 &vertex_b,
                      const geometry_msgs::msg::Point32 &vertex_c, const geometry_msgs::msg::Point32 &pt)
{
    double c1 = (vertex_b.x - vertex_a.x) * (pt.y - vertex_b.y) - (vertex_b.y - vertex_a.y) * (pt.x - vertex_b.x);
    double c2 = (vertex_c.x - vertex_b.x) * (pt.y - vertex_c.y) - (vertex_c.y - vertex_b.y) * (pt.x - vertex_c.x);
    double c3 = (vertex_a.x - vertex_c.x) * (pt.y - vertex_a.y) - (vertex_a.y - vertex_c.y) * (pt.x - vertex_a.x);

    return (c1 > 0.0 && c2 > 0.0 && c3 > 0.0) || (c1 < 0.0 && c2 < 0.0 && c3 < 0.0);
}

std::vector<geometry_msgs::msg::Polygon> polygon2Triangle(const geometry_msgs::msg::Polygon &polygon)
{
    std::vector<geometry_msgs::msg::Polygon> triangles;
    geometry_msgs::msg::Polygon poly = polygon;
    size_t num_vertices = poly.points.size();

    std::vector<bool> is_acute_angle;
    is_acute_angle.assign(num_vertices, false);
    for (size_t i = 0; i < num_vertices; i++) {
        geometry_msgs::msg::Point32 p0, p1, p2;

        adjacentPoints(i, num_vertices, poly, &p0, &p1, &p2);
        is_acute_angle.at(i) = isAcuteAngle(p0, p1, p2);
    }
    while (num_vertices >= 3) {
        size_t clipped_vertex = 0;

        for (size_t i = 0; i < num_vertices; i++) {
            bool theta = is_acute_angle.at(i);
            if (theta == true) {
                geometry_msgs::msg::Point32 p0, p1, p2;
                adjacentPoints(i, num_vertices, poly, &p0, &p1, &p2);

                size_t j_begin = (i + 2) % num_vertices;
                size_t j_end = (i - 1 + num_vertices) % num_vertices;
                bool is_ear = true;
                for (size_t j = j_begin; j != j_end; j = (j + 1) % num_vertices) {
                    if (isWithinTriangle(p0, p1, p2, poly.points.at(j))) {
                        is_ear = false;
                        break;
                    }
                }

                if (is_ear) {
                    clipped_vertex = i;
                    break;
                }
            }
        }
        geometry_msgs::msg::Point32 p0, p1, p2;
        adjacentPoints(clipped_vertex, num_vertices, poly, &p0, &p1, &p2);
        geometry_msgs::msg::Polygon triangle;
        triangle.points.push_back(p0);
        triangle.points.push_back(p1);
        triangle.points.push_back(p2);
        triangles.push_back(triangle);
        auto it = poly.points.begin();
        std::advance(it, clipped_vertex);
        poly.points.erase(it);

        auto it_angle = is_acute_angle.begin();
        std::advance(it_angle, clipped_vertex);
        is_acute_angle.erase(it_angle);

        num_vertices = poly.points.size();
        if (clipped_vertex == num_vertices) {
            clipped_vertex = 0;
        }
        adjacentPoints(clipped_vertex, num_vertices, poly, &p0, &p1, &p2);
        is_acute_angle.at(clipped_vertex) = isAcuteAngle(p0, p1, p2);

        size_t i_prev = (clipped_vertex == 0) ? num_vertices - 1 : clipped_vertex - 1;
        adjacentPoints(i_prev, num_vertices, poly, &p0, &p1, &p2);
        is_acute_angle.at(i_prev) = isAcuteAngle(p0, p1, p2);
    }
    return triangles;
}

geometry_msgs::msg::Polygon area2Polygon(const lanelet::ConstArea &area)
{
    geometry_msgs::msg::Polygon polygon;
    polygon.points.clear();
    polygon.points.reserve(area.outerBoundPolygon().size());

    std::transform(area.outerBoundPolygon().begin(), area.outerBoundPolygon().end(), std::back_inserter(polygon.points),
                   [](lanelet::ConstPoint3d pt) { return toGeomMsgPt32(pt.basicPoint()); });
    return polygon;
}

geometry_msgs::msg::Polygon lanelet2Polygon(const lanelet::ConstLanelet &ll)
{
    geometry_msgs::msg::Polygon polygon;

    const lanelet::CompoundPolygon3d &ll_poly = ll.polygon3d();
    polygon.points.clear();
    polygon.points.reserve(ll_poly.size());

    std::transform(ll_poly.begin(), ll_poly.end(), std::back_inserter(polygon.points),
                   [](lanelet::ConstPoint3d pt) { return toGeomMsgPt32(pt.basicPoint()); });
    return polygon;
}

visualization_msgs::msg::MarkerArray laneletsAsTriangleMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const std::string &ns,
                                                                   const lanelet::ConstLanelets &lanelets,
                                                                   const std_msgs::msg::ColorRGBA &c, bool show_id)
{
    visualization_msgs::msg::MarkerArray marker_array;
    visualization_msgs::msg::Marker marker;

    if (lanelets.empty()) {
        return marker_array;
    }
    static int id = 0;
    setMarkerHeader(&marker, id++, t, "map", ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::TRIANGLE_LIST, 1.0);

    for (auto ll : lanelets) {
        std::vector<geometry_msgs::msg::Polygon> triangles = lanelet2Triangle(ll);

        for (auto tri : triangles) {
            geometry_msgs::msg::Point tri0[3];

            for (size_t i = 0; i < 3; i++) {
                tri0[i] = toGeomMsgPt(tri.points[i]);

                marker.points.push_back(tri0[i]);
                marker.colors.push_back(c);
            }
        }
    }
    if (!marker.points.empty()) {
        marker_array.markers.push_back(marker);
    }

    if (show_id) {
        for (auto ll : lanelets) {
            visualization_msgs::msg::Marker marker_id;
            std_msgs::msg::ColorRGBA c_id;
            setColor(&c_id, 1.0, 1.0, 1.0, 1.0);
            setMarkerHeader(&marker_id, ll.id(), t, "map", ns + "_id", c_id, visualization_msgs::msg::Marker_Constants::ADD,
                            visualization_msgs::msg::Marker_Constants::TEXT_VIEW_FACING, 1.0);

            auto ceneter_point = lanelet::geometry::interpolatedPointAtDistance(
                ll.centerline2d(), lanelet::geometry::length(ll.centerline2d()) / 2.0);
            marker_id.pose.position.x = ceneter_point.x();
            marker_id.pose.position.y = ceneter_point.y();
            marker_id.text = std::to_string(ll.id());
            marker_array.markers.push_back(marker_id);
        }
    }

    return marker_array;
}

visualization_msgs::msg::MarkerArray laneletsAsTriangleMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                   const std::string &ns,
                                                                   const lanelet::ConstLanelets &lanelets,
                                                                   const std_msgs::msg::ColorRGBA &indoor_c,
                                                                   const std_msgs::msg::ColorRGBA &outdoor_c, bool show_id)
{
    visualization_msgs::msg::MarkerArray marker_array;
    visualization_msgs::msg::Marker marker;

    if (lanelets.empty()) {
        return marker_array;
    }
    static int id = 0;
    setMarkerHeader(&marker, id++, t, "map", ns, outdoor_c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::TRIANGLE_LIST, 1.0);

    for (auto ll : lanelets) {
        std::vector<geometry_msgs::msg::Polygon> triangles = lanelet2Triangle(ll);

        for (auto tri : triangles) {
            geometry_msgs::msg::Point tri0[3];

            for (size_t i = 0; i < 3; i++) {
                tri0[i] = toGeomMsgPt(tri.points[i]);
                if (ll.hasAttribute("no_obstacle_avoid") && ll.attribute("no_obstacle_avoid") == "true") {
                    marker.points.push_back(tri0[i]);
                    marker.colors.push_back(indoor_c);
                    continue;
                }
                marker.points.push_back(tri0[i]);
                marker.colors.push_back(outdoor_c);
            }
        }
    }
    if (!marker.points.empty()) {
        marker_array.markers.push_back(marker);
    }

    if (show_id) {
        for (auto ll : lanelets) {
            visualization_msgs::msg::Marker marker_id;
            std_msgs::msg::ColorRGBA c_id;
            setColor(&c_id, 1.0, 1.0, 1.0, 1.0);
            setMarkerHeader(&marker_id, ll.id(), t, "map", ns + "_id", c_id, visualization_msgs::msg::Marker_Constants::ADD,
                            visualization_msgs::msg::Marker_Constants::TEXT_VIEW_FACING, 1.0);

            // 标记
            std::string attribute_str = "";
            if (ll.hasAttribute("no_obstacle_stop") && ll.attribute("no_obstacle_stop").value() == "true") {
                attribute_str += " no_obstacle_stop";
            }
            if (ll.hasAttribute("no_obstacle_avoid") && ll.attribute("no_obstacle_avoid").value() == "true") {
                attribute_str += " no_obstacle_avoid";
            }
            if (ll.hasAttribute("no_lane_change") && ll.attribute("no_lane_change").value() == "true") {
                attribute_str += " no_lane_change";
            }
            if (ll.hasAttribute("no_dangerous_turn") && ll.attribute("no_dangerous_turn").value() == "true") {
                attribute_str += " no_dangerous_turn";
            }
            if (ll.hasAttribute("priority")) {
                int priority = ll.attribute("priority").asInt().get();
                attribute_str += " priority:" + std::to_string(priority);
            }

            auto ceneter_point = lanelet::geometry::interpolatedPointAtDistance(
                ll.centerline2d(), lanelet::geometry::length(ll.centerline2d()) / 2.0);
            marker_id.pose.position.x = ceneter_point.x();
            marker_id.pose.position.y = ceneter_point.y();
            marker_id.text = std::to_string(ll.id()) + attribute_str;
            marker_array.markers.push_back(marker_id);
        }
    }

    return marker_array;
}

visualization_msgs::msg::MarkerArray areasAsTriangleMarkerArray(const builtin_interfaces::msg::Time &t,
                                                                const std::string &ns, const lanelet::Areas &areas,
                                                                const std_msgs::msg::ColorRGBA &c)
{
    visualization_msgs::msg::MarkerArray marker_array;
    visualization_msgs::msg::Marker marker;

    if (areas.empty()) {
        return marker_array;
    }
    static int id = 0;
    setMarkerHeader(&marker, id++, t, "map", ns, c, visualization_msgs::msg::Marker_Constants::ADD,
                    visualization_msgs::msg::Marker_Constants::TRIANGLE_LIST, 1.0);

    for (auto area : areas) {
        std::vector<geometry_msgs::msg::Polygon> triangles = area2Triangle(area);
        for (auto tri : triangles) {
            for (size_t i = 0; i < 3; i++) {
                marker.points.push_back(toGeomMsgPt(tri.points[i]));
                marker.colors.push_back(c);
            }
        }
    }

    if (!marker.points.empty()) {
        marker_array.markers.push_back(marker);
    }
    return marker_array;
}

// bzj add
void insertMarkerArray(visualization_msgs::msg::MarkerArray &a1, const visualization_msgs::msg::MarkerArray &a2)
{
    if (a2.markers.size() > 0) {
        a1.markers.insert(a1.markers.end(), a2.markers.begin(), a2.markers.end());
    }
}

geometry_msgs::msg::Polygon basicPolygon3dToMsgPolygon(const lanelet::BasicPolygon3d &basic_polygon)
{
    geometry_msgs::msg::Polygon polygon;
    polygon.points.clear();
    polygon.points.reserve(basic_polygon.size());

    std::transform(basic_polygon.begin(), basic_polygon.end(), std::back_inserter(polygon.points),
                   [](const lanelet::BasicPoint3d &pt) {
                       geometry_msgs::msg::Point32 msg_pt;
                       msg_pt.x = static_cast<float>(pt.x());
                       msg_pt.y = static_cast<float>(pt.y());
                       msg_pt.z = static_cast<float>(pt.z());
                       return msg_pt;
                   });
    return polygon;
}

// bzj add
visualization_msgs::msg::MarkerArray getLanelet2MapMarkerArray(const lanelet::LaneletMapPtr &map)
{
    if (!map) {
        return visualization_msgs::msg::MarkerArray();
    }

    auto lls = uslam::common::had_map_utils::getConstLaneletLayer(map);

    std_msgs::msg::ColorRGBA color_lane_bounds, color_parking_area_bounds, color_parking_spot_bounds, color_geom_bounds,
        color_lanelets, color_lanelets_indoor, color_parking, color_parking_access, color_pickup_dropoff,
        color_virtual_obstacle, color_industrial_park, color_special_area;
    uslam::common::had_map_utils::setColor(&color_lane_bounds, 1.0f, 1.0f, 1.0f, 1.0f);  // white
    uslam::common::had_map_utils::setColor(&color_parking_area_bounds, 1.0f, 1.0f, 1.0f, 1.0f);
    uslam::common::had_map_utils::setColor(&color_parking_spot_bounds, 1.0f, 0.8f, 0.0f, 1.0f);  // 深黄色
    uslam::common::had_map_utils::setColor(&color_geom_bounds, 0.0f, 0.0f, 1.0f, 1.0f);
    uslam::common::had_map_utils::setColor(&color_lanelets, 0.2f, 0.5f, 0.6f, 0.6f);           // 湖蓝
    uslam::common::had_map_utils::setColor(&color_lanelets_indoor, 0.93f, 0.9f, 0.52f, 1.0f);  // 黄
    uslam::common::had_map_utils::setColor(&color_parking, 0.3f, 0.3f, 0.7f, 0.5f);
    uslam::common::had_map_utils::setColor(&color_parking_access, 0.3f, 0.7f, 0.3f, 0.5f);
    uslam::common::had_map_utils::setColor(&color_pickup_dropoff, 0.9f, 0.2f, 0.1f, 0.7f);
    uslam::common::had_map_utils::setColor(&color_virtual_obstacle, 1.0f, 0.0f, 0.0f, 1.0f);
    uslam::common::had_map_utils::setColor(&color_industrial_park, 1.0f, 0.75f, 0.796f, 0.8f);  // 浅粉
    uslam::common::had_map_utils::setColor(&color_special_area, 0.9f, 0.2f, 0.1f, 0.7f);        // 橙

    visualization_msgs::msg::MarkerArray map_marker_array;

    // 删除历史地图的Marker
    visualization_msgs::msg::Marker marker_tmp;
    marker_tmp.action = visualization_msgs::msg::Marker_Constants::DELETEALL;
    map_marker_array.markers.push_back(marker_tmp);

    builtin_interfaces::msg::Time marker_t;
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::laneletsBoundaryAsMarkerArray(
                                            marker_t, lls, color_lane_bounds, true));
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::laneletsAsTriangleMarkerArray(
                          marker_t, "lanelet_triangles", lls, color_lanelets_indoor, color_lanelets, true));
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::laneletsRegulatoryElementAsMarkerArray(marker_t, lls));

    // 特殊区域（厂区门等场景）special area
    auto ll_polygons = uslam::common::had_map_utils::getPolygonLayer(map);
    auto ll_special_polygons = uslam::common::had_map_utils::typePolygons(ll_polygons, "special_area");
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::polygonsBoundaryAsMarkerArray(
                                            marker_t, "special_area", ll_special_polygons, color_lanelets));
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::polygonsTextAsMarkerArray(
                                            marker_t, "special_area", ll_special_polygons, color_lane_bounds));

    // parking area  parking spot
    auto ll_parking_area_polygons = uslam::common::had_map_utils::typePolygons(ll_polygons, "parking_area");
    auto ll_parking_spot_polygons = uslam::common::had_map_utils::typePolygons(ll_polygons, "parking_spot");
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsBoundaryAsMarkerArray(
                          marker_t, "parking_area", ll_parking_area_polygons, color_parking_area_bounds, 0.1));
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsBoundaryAsMarkerArray(
                          marker_t, "parking_spot", ll_parking_spot_polygons, color_parking_spot_bounds, 0.2));
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::polygonsTextAsMarkerArray(
                                            marker_t, "parking_area", ll_parking_area_polygons, color_parking_area_bounds));
    insertMarkerArray(map_marker_array, uslam::common::had_map_utils::polygonsTextAsMarkerArray(
                                            marker_t, "parking_spot", ll_parking_spot_polygons, color_parking_spot_bounds));

    // vritual_obstacle
    auto ll_virtual_obstacle_polygons = uslam::common::had_map_utils::typePolygons(ll_polygons, "virtual_obstacle");
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsBoundaryAsMarkerArray(
                          marker_t, "virtual_obstacle", ll_virtual_obstacle_polygons, color_virtual_obstacle, 0.2));
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsTextAsMarkerArray(
                          marker_t, "virtual_obstacle", ll_virtual_obstacle_polygons, color_virtual_obstacle));

    // industrial_park 园区场景
    auto ll_industrial_park_polygons = uslam::common::had_map_utils::typePolygons(ll_polygons, "industrial_park");
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsBoundaryAsMarkerArray(
                          marker_t, "industrial_park", ll_industrial_park_polygons, color_industrial_park, 0.2));
    insertMarkerArray(map_marker_array,
                      uslam::common::had_map_utils::polygonsTextAsMarkerArray(
                          marker_t, "industrial_park", ll_industrial_park_polygons, color_industrial_park));

    return map_marker_array;
}

}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam
