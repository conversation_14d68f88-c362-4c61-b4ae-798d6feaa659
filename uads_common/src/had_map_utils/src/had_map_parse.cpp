// Copyright 2020 TierIV, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Co-developed by Tier IV, Inc. and Apex.AI, Inc.

// lint -e537 pclint vs cpplint NOLINT

#include "had_map_utils/local_osm_parser.hpp"
#include "ulog/ulog.h"

#include "had_map_utils/had_map_parse.hpp"

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#include <lanelet2_io/io_handlers/OsmFile.h>
#include <lanelet2_io/io_handlers/OsmHandler.h>
#include <lanelet2_io/Io.h>
#include <lanelet2_projection/UTM.h>
#include <lanelet2_io/Projection.h>
#include <lanelet2_core/geometry/LineString.h>
#pragma GCC diagnostic pop

#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/MGRS.hpp>

namespace uslam {
namespace common {
namespace had_map_utils {
bool osmMapParse(const std::string map_filename, bool local_mdoe, std::shared_ptr<lanelet::LaneletMap> &map)
{
    lanelet::GPSPoint origin_gps;

    // 解析第一个点的mgrs code 作为projector初值
    pugi::xml_document doc;
    auto result = doc.load_file(map_filename.c_str());
    if (!result) {
        ULOGE("osmMapParse: map file is not exist. map_filename:%s", map_filename.c_str());
        return false;
    }

    lanelet::osm::Errors osmReadErrors;
    auto fileNode = lanelet::osm::read(doc, &osmReadErrors);
    if (fileNode.nodes.size()) {
        auto &first_node = fileNode.nodes.begin()->second;
        if (first_node.attributes.find("mgrs_code") != first_node.attributes.end() &&
            first_node.attributes["mgrs_code"].size() > 5) {
            std::string mgrs_code = first_node.attributes["mgrs_code"].substr(0, 5);
            ULOGI("osmMapParse: map file first point: id:%d lat:%.9f lon:%.9f mgrs:%s", fileNode.nodes.begin()->first,
                  first_node.point.lat, first_node.point.lon, mgrs_code.c_str());

            if (mgrs_code != "99XXX") {
                int zone, prec;
                bool northp;
                double x, y;
                GeographicLib::MGRS::Reverse(mgrs_code, zone, northp, x, y, prec, false);
                double lat, lon;
                GeographicLib::UTMUPS::Reverse(zone, northp, x, y, lat, lon);
                origin_gps = lanelet::GPSPoint{lat, lon, 0.0};
                ULOGI("osmMapParse: transform gps point: lat:%.9f lon:%.9f", lat, lon);
            } else {
                ULOGI("osmMapParse: invalid mgrs code: %s. use local mode.", mgrs_code.c_str());
                local_mdoe = true;
            }
        }
    }

    if (local_mdoe) {
        lanelet::ErrorMessages errors;
        const lanelet::projection::LocalProjector projector;
        map = lanelet::load(map_filename, "local_osm_handler", projector, &errors);
    } else {
        lanelet::Origin origin{origin_gps};
        lanelet::ErrorMessages errors;
        lanelet::projection::UtmProjector projector(origin);
        map = lanelet::load(map_filename, "osm_handler", projector, &errors);
    }
    return true;
}

bool osmMapWrite(const std::string map_filename, const lanelet::LaneletMap &map)
{
    try {
        lanelet::ErrorMessages errors;
        const lanelet::projection::LocalProjector projector;
        lanelet::write(map_filename, map, "local_osm_handler", projector);
    } catch (std::exception &e) {
        ULOGE("osmMapWrite: write map file failed. map_filename:%s, error:%s", map_filename.c_str(), e.what());
        return false;
    }
    return true;
}
}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam
