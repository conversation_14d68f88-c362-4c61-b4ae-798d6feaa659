// Copyright 2015-2019 Autoware Foundation. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Authors: <AUTHORS>

// NOLINTBEGIN(readability-identifier-naming)

#include "had_map_utils/local_osm_parser.hpp"

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#include <lanelet2_core/geometry/LineString.h>
#include <lanelet2_io/io_handlers/Factory.h>
#include <lanelet2_io/io_handlers/OsmFile.h>
#include <lanelet2_io/io_handlers/OsmHandler.h>
#pragma GCC diagnostic pop

#include <memory>
#include <string>

#include "local_osm_file_parser.hpp"

namespace lanelet::io_handlers {

template <typename MapT>
void registerIds(const MapT &map)
{
    if (!map.empty()) {
        utils::registerId(map.rbegin()->first);
    }
}

using Errors = std::vector<std::string>;
Errors buildErrorMessage(const std::string &errorIntro, const Errors &errors)
{
    if (errors.empty()) {
        return {};
    }
    Errors message{errorIntro};
    message.reserve(errors.size() + 1);
    for (const auto &error : errors) {
        message.push_back("\t- " + error);
    }
    return message;
}

std::unique_ptr<LaneletMap> LocalOsmParser::parse(const std::string &filename, ErrorMessages &errors) const
{
    // read xml
    pugi::xml_document doc;
    auto result = doc.load_file(filename.c_str());
    if (!result) {
        throw lanelet::ParseError(std::string("Errors occured while parsing osm file: ") + result.description());
    }
    osm::Errors osmReadErrors;
    // testAndPrintLocaleWarning(osmReadErrors);
    auto file = lanelet::local_osm::LocalOsmFileParser::read(doc, &osmReadErrors);
    auto map = fromOsmFile(file, errors);
    // make sure ids in the file are known to Lanelet2 id management.
    registerIds(file.nodes);
    registerIds(file.ways);
    registerIds(file.relations);
    errors = buildErrorMessage("Errors ocurred while parsing Lanelet Map:", utils::concatenate({osmReadErrors, errors}));

    return map;
}

namespace {
RegisterParser<LocalOsmParser> regParser;
}  // namespace

void LocalOsmParser::parseVersions(const std::string &filename, std::string *format_version, std::string *map_version)
{
    if (format_version == nullptr || map_version == nullptr) {
        std::cerr << __FUNCTION__ << ": either format_version or map_version is null pointer!";
        return;
    }

    pugi::xml_document doc;
    auto result = doc.load_file(filename.c_str());
    if (!result) {
        throw lanelet::ParseError(std::string("Errors occurred while parsing osm file: ") + result.description());
    }

    auto osmNode = doc.child("osm");
    auto metainfo = osmNode.child("MetaInfo");
    if (metainfo.attribute("format_version")) {
        *format_version = metainfo.attribute("format_version").value();
    }
    if (metainfo.attribute("map_version")) {
        *map_version = metainfo.attribute("map_version").value();
    }
}

namespace {
// register with factories
RegisterWriter<LocalOsmWriter> regWriter;
}  // namespace

void LocalOsmWriter::write(const std::string &filename, const LaneletMap &laneletMap, ErrorMessages &errors,
                           const io::Configuration &params) const
{
    auto file = toOsmFile(laneletMap, errors, params);
    auto doc = lanelet::local_osm::LocalOsmFileWriter::write(*file, params);
    auto res = doc->save_file(filename.c_str(), "  ");
    if (!res) {
        throw ParseError("Pugixml failed to write the map (unable to create file?)");
    }
}

}  // namespace lanelet::io_handlers

// NOLINTEND(readability-identifier-naming)
