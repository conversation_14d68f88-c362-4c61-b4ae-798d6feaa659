// Copyright 2020 TierIV, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Co-developed by Tier IV, Inc. and Apex.AI, Inc.

// lint -e537 pclint vs cpplint NOLINT

#include "had_map_utils/had_map_utils.hpp"

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#include <lanelet2_core/geometry/Lanelet.h>
#pragma GCC diagnostic pop

#include <algorithm>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "utf/tf2/utils.h"

namespace uslam {
namespace common {
namespace had_map_utils {

std::vector<double> calculateSegmentDistances(const lanelet::ConstLineString3d &line_string)
{
    std::vector<double> segment_distances;
    segment_distances.reserve(line_string.size() - 1);

    for (size_t i = 1; i < line_string.size(); ++i) {
        const auto distance = lanelet::geometry::distance(line_string[i], line_string[i - 1]);
        segment_distances.push_back(distance);
    }

    return segment_distances;
}

std::vector<double> calculateAccumulatedLengths(const lanelet::ConstLineString3d &line_string)
{
    const auto segment_distances = calculateSegmentDistances(line_string);

    std::vector<double> accumulated_lengths{0};
    accumulated_lengths.reserve(segment_distances.size() + 1);
    std::partial_sum(std::begin(segment_distances), std::end(segment_distances), std::back_inserter(accumulated_lengths));

    return accumulated_lengths;
}

std::pair<size_t, size_t> findNearestIndexPair(const std::vector<double> &accumulated_lengths, const double target_length)
{
    // List size
    const auto N = accumulated_lengths.size();

    // Front
    if (target_length < accumulated_lengths.at(1)) {
        return std::make_pair(0, 1);
    }

    // Back
    if (target_length > accumulated_lengths.at(N - 2)) {
        return std::make_pair(N - 2, N - 1);
    }

    // Middle
    for (size_t i = 1; i < N; ++i) {
        if (accumulated_lengths.at(i - 1) <= target_length && target_length <= accumulated_lengths.at(i)) {
            return std::make_pair(i - 1, i);
        }
    }

    // Throw an exception because this never happens
    throw std::runtime_error("findNearestIndexPair(): No nearest point found.");
}

std::vector<lanelet::BasicPoint3d> resamplePoints(const lanelet::ConstLineString3d &line_string, const int32_t num_segments)
{
    // Calculate length
    const auto line_length = lanelet::geometry::length(line_string);

    // Calculate accumulated lengths
    const auto accumulated_lengths = calculateAccumulatedLengths(line_string);

    // Create each segment
    std::vector<lanelet::BasicPoint3d> resampled_points;
    for (auto i = 0; i <= num_segments; ++i) {
        // Find two nearest points
        const double target_length = (static_cast<double>(i) / num_segments) * static_cast<double>(line_length);
        auto inter_point = lanelet::geometry::interpolatedPointAtDistance(line_string, target_length);

        // Add to list
        resampled_points.push_back(inter_point);
    }

    return resampled_points;
}

lanelet::LineString3d generateFineCenterline(const lanelet::ConstLanelet &lanelet_obj, const double resolution)
{
    lanelet::ConstLineString3d centerline_raw = lanelet_obj.centerline();
    if (centerline_raw.id() == 0) {
        // 使用中线
        const double left_length = static_cast<double>(lanelet::geometry::length(lanelet_obj.leftBound()));
        const double right_length = static_cast<double>(lanelet::geometry::length(lanelet_obj.rightBound()));
        const double longer_distance = (left_length > right_length) ? left_length : right_length;
        const int32_t num_segments = std::max(static_cast<int32_t>(ceil(longer_distance / resolution)), 1);

        // Resample points
        const auto left_points = resamplePoints(lanelet_obj.leftBound(), num_segments);
        const auto right_points = resamplePoints(lanelet_obj.rightBound(), num_segments);

        // Create centerline
        lanelet::LineString3d centerline(lanelet::utils::getId());
        for (size_t i = 0; i < static_cast<size_t>(num_segments + 1); i++) {
            // Add ID for the average point of left and right
            const auto center_basic_point = (right_points.at(i) + left_points.at(i)) / 2.0;
            const lanelet::Point3d center_point(lanelet::utils::getId(), center_basic_point.x(), center_basic_point.y(),
                                                center_basic_point.z());
            centerline.push_back(center_point);
        }
        return centerline;
    } else {
        lanelet::LineString3d centerline(lanelet::utils::getId());
        const int32_t num_segments = std::max(
            static_cast<int32_t>(ceil(static_cast<double>(lanelet::geometry::length(centerline_raw)) / resolution)), 1);
        const auto centerline_raw_resample = resamplePoints(centerline_raw, num_segments);

        for (size_t i = 0; i < static_cast<size_t>(num_segments + 1); i++) {
            // Add ID for the average point of left and right
            const auto center_basic_point = centerline_raw_resample.at(i);
            const lanelet::Point3d center_point(lanelet::utils::getId(), center_basic_point.x(), center_basic_point.y(),
                                                center_basic_point.z());
            centerline.push_back(center_point);
        }
        return centerline;
    }
}

void overwriteLaneletsCenterline(lanelet::LaneletMapPtr lanelet_map, const bool force_overwrite)
{
    for (auto &lanelet_obj : lanelet_map->laneletLayer) {
        if (force_overwrite || !lanelet_obj.hasCustomCenterline()) {
            const auto fine_center_line = generateFineCenterline(lanelet_obj, 2.0);
            lanelet_obj.setCenterline(fine_center_line);
        }
    }
}

bool isInLanelet(const double pose_x, const double pose_y, const lanelet::ConstLanelet &lanelet, const double radius)
{
    constexpr double eps = 1.0e-9;
    const lanelet::BasicPoint2d p(pose_x, pose_y);
    return boost::geometry::distance(p, lanelet.polygon2d().basicPolygon()) < radius + eps;
}

lanelet::ConstLineString3d getClosestSegment(const lanelet::BasicPoint2d &search_pt,
                                             const lanelet::ConstLineString3d &linestring)
{
    if (linestring.size() < 2) {
        return lanelet::LineString3d();
    }

    lanelet::ConstLineString3d closest_segment;
    double min_distance = std::numeric_limits<double>::max();

    for (size_t i = 1; i < linestring.size(); i++) {
        lanelet::BasicPoint3d prev_basic_pt = linestring[i - 1].basicPoint();
        lanelet::BasicPoint3d current_basic_pt = linestring[i].basicPoint();

        lanelet::Point3d prev_pt(lanelet::InvalId, prev_basic_pt.x(), prev_basic_pt.y(), prev_basic_pt.z());
        lanelet::Point3d current_pt(lanelet::InvalId, current_basic_pt.x(), current_basic_pt.y(), current_basic_pt.z());

        lanelet::LineString3d current_segment(lanelet::InvalId, {prev_pt, current_pt});
        double distance = lanelet::geometry::distance2d(lanelet::utils::to2D(current_segment).basicLineString(), search_pt);
        if (distance < min_distance) {
            closest_segment = current_segment;
            min_distance = distance;
        }
    }
    return closest_segment;
}

double getLaneletAngle(const lanelet::ConstLanelet &lanelet, const lanelet::Point2d &search_point,
                       lanelet::Point2d *nearst_point)
{
    const auto &centerline = uslam::common::had_map_utils::generateFineCenterline(lanelet, 0.5);

    double min_dis = std::numeric_limits<double>::max();
    auto point_min_it = lanelet::utils::to2D(centerline).begin();
    double angle = 0.0;
    for (auto first = lanelet::utils::to2D(centerline).begin(),
              second = std::next(lanelet::utils::to2D(centerline).begin());
         second != lanelet::utils::to2D(centerline).end(); ++first, ++second) {
        const auto &p1 = *first;
        const auto &p2 = *second;
        double currentLength = boost::geometry::distance(search_point, p1);
        if (currentLength < min_dis) {
            point_min_it = first;
            angle = std::atan2(p2.y() - p1.y(), p2.x() - p1.x());
            min_dis = currentLength;
        }
    }
    if (nearst_point != nullptr) {
        *nearst_point = *point_min_it;
    }
    return angle;
}

inline double normalizeRadian(const double rad, const double min_rad = -M_PI)
{
    const auto max_rad = min_rad + 2 * M_PI;

    const auto value = std::fmod(rad, 2 * M_PI);
    if (min_rad <= value && value < max_rad) {
        return value;
    }

    return value - std::copysign(2 * M_PI, value);
}

bool getClosestLanelet(const lanelet::ConstLanelets &lanelets, const geometry_msgs::msg::Pose &search_pose,
                       lanelet::ConstLanelet *closest_lanelet_ptr)
{
    if (closest_lanelet_ptr == nullptr) {
        std::cerr << "argument closest_lanelet_ptr is null! Failed to find closest lanelet" << std::endl;
        return false;
    }

    if (lanelets.empty()) {
        return false;
    }

    bool found = false;

    lanelet::BasicPoint2d search_point(search_pose.position.x, search_pose.position.y);

    // find by distance
    lanelet::ConstLanelets candidate_lanelets;
    {
        double min_distance = std::numeric_limits<double>::max();
        for (const auto &llt : lanelets) {
            double distance = boost::geometry::comparable_distance(llt.polygon2d().basicPolygon(), search_point);

            if (std::abs(distance - min_distance) <= std::numeric_limits<double>::epsilon()) {
                candidate_lanelets.push_back(llt);
            } else if (distance < min_distance) {
                found = true;
                candidate_lanelets.clear();
                candidate_lanelets.push_back(llt);
                min_distance = distance;
            }
        }
    }

    if (candidate_lanelets.size() == 1) {
        *closest_lanelet_ptr = candidate_lanelets.at(0);
        return found;
    }

    // find by angle
    {
        double min_angle = std::numeric_limits<double>::max();
        double pose_yaw = tf2::getYaw(search_pose.orientation);
        for (const auto &llt : candidate_lanelets) {
            lanelet::ConstLineString3d segment =
                uslam::common::had_map_utils::getClosestSegment(search_point, llt.centerline());
            double angle_diff = M_PI;
            if (!segment.empty()) {
                double segment_angle =
                    std::atan2(segment.back().y() - segment.front().y(), segment.back().x() - segment.front().x());
                angle_diff = std::abs(normalizeRadian(segment_angle - pose_yaw));
            }
            if (angle_diff < min_angle) {
                min_angle = angle_diff;
                *closest_lanelet_ptr = llt;
            }
        }
    }

    return found;
}

lanelet::ArcCoordinates getArcCoordinates(const lanelet::ConstLanelets &lanelet_sequence,
                                          const geometry_msgs::msg::Pose &pose)
{
    lanelet::ConstLanelet closest_lanelet;
    getClosestLanelet(lanelet_sequence, pose, &closest_lanelet);

    double length = 0;
    lanelet::ArcCoordinates arc_coordinates;
    for (const auto &llt : lanelet_sequence) {
        const auto &centerline_2d = lanelet::utils::to2D(llt.centerline());
        if (llt == closest_lanelet) {
            const auto lanelet_point =
                lanelet::Point3d(lanelet::InvalId, pose.position.x, pose.position.y, pose.position.z);
            arc_coordinates =
                lanelet::geometry::toArcCoordinates(centerline_2d, lanelet::utils::to2D(lanelet_point).basicPoint());
            arc_coordinates.length += length;
            break;
        }
        length += static_cast<double>(lanelet::geometry::length(centerline_2d));
    }
    return arc_coordinates;
}

}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam
