// Copyright 2020 TierIV, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Co-developed by Tier IV, Inc. and Apex.AI, Inc.

// lint -e537 pclint vs cpplint NOLINT

#include "had_map_utils/had_map_query.hpp"

#include <algorithm>
#include <limits>
#include <memory>
#include <string>
#include <vector>

namespace uslam {
namespace common {
namespace had_map_utils {

lanelet::Areas getAreaLayer(const lanelet::LaneletMapPtr ll_map)
{
    lanelet::Areas areas;
    for (auto ai = ll_map->areaLayer.begin(); ai != ll_map->areaLayer.end(); ai++) {
        areas.push_back(*ai);
    }
    return areas;
}

lanelet::Areas subtypeAreas(const lanelet::Areas areas, const char subtype[])
{
    lanelet::Areas subtype_areas;
    for (auto ai = areas.begin(); ai != areas.end(); ai++) {
        lanelet::Area a = *ai;
        if (a.hasAttribute(lanelet::AttributeName::Subtype)) {
            lanelet::Attribute attr = a.attribute(lanelet::AttributeName::Subtype);
            if (attr.value() == subtype) {
                subtype_areas.push_back(a);
            }
        }
    }
    return subtype_areas;
}

lanelet::Polygons3d getPolygonLayer(const lanelet::LaneletMapPtr ll_map)
{
    lanelet::Polygons3d polygons;
    for (auto ai = ll_map->polygonLayer.begin(); ai != ll_map->polygonLayer.end(); ai++) {
        polygons.push_back(*ai);
    }
    return polygons;
}

lanelet::Polygons3d subtypePolygons(const lanelet::Polygons3d polygons, const char subtype[])
{
    lanelet::Polygons3d subtype_polygons;
    for (auto pi = polygons.begin(); pi != polygons.end(); pi++) {
        lanelet::Polygon3d p = *pi;
        if (p.hasAttribute(lanelet::AttributeName::Subtype)) {
            lanelet::Attribute attr = p.attribute(lanelet::AttributeName::Subtype);
            if (attr.value() == subtype) {
                subtype_polygons.push_back(p);
            }
        }
    }
    return subtype_polygons;
}

lanelet::Polygons3d typePolygons(const lanelet::Polygons3d polygons, const char type[])
{
    lanelet::Polygons3d type_polygons;
    for (auto pi = polygons.begin(); pi != polygons.end(); pi++) {
        lanelet::Polygon3d p = *pi;
        if (p.hasAttribute(lanelet::AttributeName::Type)) {
            lanelet::Attribute attr = p.attribute(lanelet::AttributeName::Type);
            if (attr.value() == type) {
                type_polygons.push_back(p);
            }
        }
    }
    return type_polygons;
}

lanelet::LineStrings3d getLineStringLayer(const lanelet::LaneletMapPtr ll_map)
{
    lanelet::LineStrings3d linestrings;
    for (auto lsi = ll_map->lineStringLayer.begin(); lsi != ll_map->lineStringLayer.end(); lsi++) {
        linestrings.push_back(*lsi);
    }
    return linestrings;
}

lanelet::LineStrings3d subtypeLineStrings(const lanelet::LineStrings3d linestrings, const char subtype[])
{
    lanelet::LineStrings3d subtype_linestrings;
    for (auto lsi = linestrings.begin(); lsi != linestrings.end(); lsi++) {
        lanelet::LineString3d ls = *lsi;
        if (ls.hasAttribute(lanelet::AttributeName::Subtype)) {
            lanelet::Attribute attr = ls.attribute(lanelet::AttributeName::Subtype);
            if (attr.value() == subtype) {
                subtype_linestrings.push_back(ls);
            }
        }
    }
    return subtype_linestrings;
}

lanelet::ConstLanelets getConstLaneletLayer(const std::shared_ptr<lanelet::LaneletMap> &ll_map)
{
    lanelet::ConstLanelets lanelets;
    for (auto li = ll_map->laneletLayer.begin(); li != ll_map->laneletLayer.end(); li++) {
        lanelets.push_back(*li);
    }

    return lanelets;
}
lanelet::Lanelets getLaneletLayer(const std::shared_ptr<lanelet::LaneletMap> &ll_map)
{
    lanelet::Lanelets lanelets;
    for (auto li = ll_map->laneletLayer.begin(); li != ll_map->laneletLayer.end(); li++) {
        lanelets.push_back(*li);
    }

    return lanelets;
}
lanelet::Lanelets subtypeLaneletLayer(const lanelet::Lanelets lls, const char subtype[])
{
    lanelet::Lanelets subtype_lanelets;
    for (auto li = lls.begin(); li != lls.end(); li++) {
        lanelet::Lanelet ll = *li;
        if (ll.hasAttribute(lanelet::AttributeName::Subtype)) {
            lanelet::Attribute attr = ll.attribute(lanelet::AttributeName::Subtype);
            if (attr.value() == subtype) {
                subtype_lanelets.push_back(ll);
            }
        }
    }
    return subtype_lanelets;
}
lanelet::ConstLanelets subtypeLaneletLayer(const lanelet::ConstLanelets lls, const char subtype[])
{
    lanelet::ConstLanelets subtype_lanelets;
    for (auto li = lls.begin(); li != lls.end(); li++) {
        lanelet::ConstLanelet ll = *li;
        if (ll.hasAttribute(lanelet::AttributeName::Subtype)) {
            lanelet::Attribute attr = ll.attribute(lanelet::AttributeName::Subtype);
            if (attr.value() == subtype) {
                subtype_lanelets.push_back(ll);
            }
        }
    }
    return subtype_lanelets;
}

lanelet::ConstLanelets getAllNeighbors(const lanelet::routing::RoutingGraphPtr &graph, const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;

    lanelet::ConstLanelets left_lanelets = getAllNeighborsLeft(graph, lanelet);
    lanelet::ConstLanelets right_lanelets = getAllNeighborsRight(graph, lanelet);

    std::reverse(left_lanelets.begin(), left_lanelets.end());
    lanelets.insert(lanelets.end(), left_lanelets.begin(), left_lanelets.end());
    lanelets.push_back(lanelet);
    lanelets.insert(lanelets.end(), right_lanelets.begin(), right_lanelets.end());

    return lanelets;
}

lanelet::ConstLanelets getAllNeighborsRight(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;
    auto right_lane = (!!graph->right(lanelet)) ? graph->right(lanelet) : graph->adjacentRight(lanelet);
    while (!!right_lane) {
        lanelets.push_back(right_lane.get());
        right_lane = (!!graph->right(right_lane.get())) ? graph->right(right_lane.get())
                                                        : graph->adjacentRight(right_lane.get());
    }
    return lanelets;
}

lanelet::ConstLanelets getAllNeighborsLeft(const lanelet::routing::RoutingGraphPtr &graph,
                                           const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;
    auto left_lane = (!!graph->left(lanelet)) ? graph->left(lanelet) : graph->adjacentLeft(lanelet);
    while (!!left_lane) {
        lanelets.push_back(left_lane.get());
        left_lane = (!!graph->left(left_lane.get())) ? graph->left(left_lane.get()) : graph->adjacentLeft(left_lane.get());
    }
    return lanelets;
}

lanelet::ConstLanelets getAllChangeableLane(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;

    lanelet::ConstLanelets left_lanelets = getAllChangeableLeft(graph, lanelet);
    lanelet::ConstLanelets right_lanelets = getAllChangeableRight(graph, lanelet);

    std::reverse(left_lanelets.begin(), left_lanelets.end());
    lanelets.insert(lanelets.end(), left_lanelets.begin(), left_lanelets.end());
    lanelets.push_back(lanelet);
    lanelets.insert(lanelets.end(), right_lanelets.begin(), right_lanelets.end());

    return lanelets;
}

lanelet::ConstLanelets getAllChangeableRight(const lanelet::routing::RoutingGraphPtr &graph,
                                             const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;
    auto right_lane = graph->right(lanelet);
    while (!!right_lane) {
        lanelets.push_back(right_lane.get());
        right_lane = graph->right(right_lane.get());
    }
    return lanelets;
}

lanelet::ConstLanelets getAllChangeableLeft(const lanelet::routing::RoutingGraphPtr &graph,
                                            const lanelet::ConstLanelet &lanelet)
{
    lanelet::ConstLanelets lanelets;
    auto left_lane = graph->left(lanelet);
    while (!!left_lane) {
        lanelets.push_back(left_lane.get());
        left_lane = graph->left(left_lane.get());
    }
    return lanelets;
}
}  // namespace had_map_utils
}  // namespace common
}  // namespace uslam
