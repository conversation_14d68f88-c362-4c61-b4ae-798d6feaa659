ARG BASE_IMAGE
ARG CROSS_IMAGE
ARG UATO_IMAGE
ARG UBT_3RD_IMAGE

FROM $BASE_IMAGE as core
FROM $UATO_IMAGE as uauto
FROM $UBT_3RD_IMAGE as ubt_3rd

FROM --platform=$BUILDPLATFORM $CROSS_IMAGE as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM

COPY --chown=1000 . /opt/src/
WORKDIR /opt/src

RUN --mount=type=bind,from=core,source=/,target=/sysroot \
    --mount=type=bind,from=ubt_3rd,source=/opt/ubt_3rdparty,target=/opt/ubt_3rdparty \
    --mount=type=bind,from=uauto,source=/opt/uauto,target=/opt/uauto \
    --mount=type=ssh,uid=1000 \
    ./build.sh

FROM glcr.rd.ubtrobot.com/pub/hub/busybox:musl
COPY --from=builder /opt/uads/uads_common /opt/uads/uads_common
