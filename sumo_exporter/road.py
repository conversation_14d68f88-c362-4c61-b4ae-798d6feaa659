"""Road class container."""

import math
import re

from re_definitions import floatRE, intRE
from data_structures import grouper
from math_utils import apply_spline_subdivision_to_path
from shapely.geometry import LineString, MultiLineString
from lxml import etree as ET

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available. Road visualization will be disabled.")


class Road(object):
    """Class matching with a Webots Road, containing facilities to export to SUMO edges."""

    roads = dict()

    def __init__(self, wbtString, roadType):
        """Constructor: Extract info from the wbtString matching the node."""
        self.startJunction = None
        self.endJunction = None
        self.roadType = roadType

        name = re.findall(r'name\s*"([^"]*)"', wbtString)
        self.name = name[0] if name else "UnknownRoad"
        self.name = self.name.replace(" ", "")

        width = re.findall(r"width\s*(%s)" % floatRE, wbtString)
        self.width = float(width[0]) if width else 7

        speedLimit = re.findall(r"speedLimit\s*(%s)" % floatRE, wbtString)
        self.speedLimit = float(speedLimit[0]) if speedLimit else 50.0 / 3.6  # 50 km/h

        translation = re.findall(
            r"translation\s*(%s\s*%s\s*%s)" % (floatRE, floatRE, floatRE), wbtString
        )
        self.translation = (
            [float(x) for x in translation[0].split()]
            if translation
            else [0.0, 0.0, 0.0]
        )

        rotation = re.findall(
            r"rotation\s*(%s\s*%s\s*%s\s*%s)" % (floatRE, floatRE, floatRE, floatRE),
            wbtString,
        )
        self.rotation = (
            [float(x) for x in rotation[0].split()] if rotation else [0.0, 0.0, 1.0, 0]
        )

        # startJunctionID = re.findall(r'startJunction\s*"([^"]*)"', wbtString)
        # self.startJunctionID = startJunctionID[0] if startJunctionID else ""

        # endJunctionID = re.findall(r'endJunction\s*"([^"]*)"', wbtString)
        # self.endJunctionID = endJunctionID[0] if endJunctionID else ""

        if self.roadType == "Road":
            try:
                self.wayPoints = grouper(
                    3,
                    [
                        float(x)
                        for x in re.findall(r"wayPoints\s*\[([^\]]*)\]", wbtString)[
                            0
                        ].split()
                    ],
                )
                correction_angle = math.pi * 0.5
                for i in range(len(self.wayPoints)):
                    wayPoint = self.wayPoints[i]
                    x = (
                        -math.cos(correction_angle) * wayPoint[0]
                        + math.sin(correction_angle) * wayPoint[1]
                    )
                    y = (
                        math.cos(correction_angle) * wayPoint[1]
                        + math.sin(correction_angle) * wayPoint[0]
                    )
                    z = wayPoint[2]
                    self.wayPoints[i] = [x, y, z]
            except Exception:
                self.wayPoints = []

            splineSubdivision = re.findall(
                r"splineSubdivision\s*(%s)" % intRE, wbtString
            )
            splineSubdivision = int(splineSubdivision[0]) if splineSubdivision else 4
            if splineSubdivision > 0:
                self.wayPoints = apply_spline_subdivision_to_path(
                    self.wayPoints, splineSubdivision
                )
        elif self.roadType == "StraightRoadSegment":
            length = re.findall(r"length\s*(%s)" % floatRE, wbtString)
            length = float(length[0]) if length else 10.0
            self.wayPoints = [[0, 0, 0], [0, length, 0]]
        elif self.roadType == "CurvedRoadSegment":
            self.wayPoints = []

            subdivision = re.findall(r"subdivision\s*(%s)" % intRE, wbtString)
            subdivision = int(subdivision[0]) if subdivision else 16

            curvatureRadius = re.findall(r"curvatureRadius\s*(%s)" % floatRE, wbtString)
            curvatureRadius = float(curvatureRadius[0]) if curvatureRadius else 10.0

            totalAngle = re.findall(r"totalAngle\s*(%s)" % floatRE, wbtString)
            totalAngle = float(totalAngle[0]) if totalAngle else 1.5708

            for i in range(subdivision + 1):
                x1 = curvatureRadius * math.cos(
                    float(i) * totalAngle / float(subdivision)
                )
                y1 = curvatureRadius * math.sin(
                    float(i) * totalAngle / float(subdivision)
                )
                self.wayPoints.append([x1, y1, 0])
        else:
            self.wayPoints = []

        lanes = re.findall(r"numberOfLanes\s*(%s)" % intRE, wbtString)
        self.lanes = int(lanes[0]) if lanes else 2

        forwardLanes = re.findall(r"numberOfForwardLanes\s*(%s)" % intRE, wbtString)
        self.forwardLanes = int(forwardLanes[0]) if forwardLanes else 1

        self.backwardLanes = self.lanes - self.forwardLanes
        self.oneWay = self.backwardLanes == 0

        if self.rotation[0] < 0.01 and self.rotation[1] < 0.01:
            angle = self.rotation[3]
            if self.rotation[2] < 0:
                angle = -angle
            for i in range(len(self.wayPoints)):
                wayPoint = self.wayPoints[i]
                x = math.cos(angle) * wayPoint[1] - math.sin(angle) * wayPoint[0]
                y = math.cos(angle) * wayPoint[0] + math.sin(angle) * wayPoint[1]
                z = wayPoint[2]
                self.wayPoints[i] = [x, y, z]
        else:
            print(
                'Warning: cannot export edge "%s" because the road is rotated not only along axis Z.'
                % self.name
            )

        self.laneWidth = self.width / self.lanes

        # The original path should be slightly shifted if the case where the
        # forwardLanes and backwardLanes are not matching.
        originalCoords = [
            [x + self.translation[0], y + self.translation[1], z + self.translation[2]]
            for [x, y, z] in self.wayPoints
        ]
        originalLineString = LineString(originalCoords)
        if 0:
            originalLineString = originalLineString.parallel_offset(
                0.5 * self.laneWidth * self.forwardLanes, "left"
            )
        else:
            offset = (self.forwardLanes - self.backwardLanes) * self.laneWidth * 0.5
            if offset > 0.0:
                originalLineString = originalLineString.parallel_offset(offset, "left")
            elif offset < 0.0:
                originalLineString = originalLineString.parallel_offset(offset, "left")
                # originalLineString = LineString(list(originalLineString.coords[::-1]))

        if isinstance(originalLineString, MultiLineString):
            self.originalPath = originalCoords
        else:
            self.originalPath = list(originalLineString.coords)

    # def create_edge(self, edges):
    #     """Create the SUMO edge XML node(s) matching with the Webots road."""
    #     if self.startJunctionID == self.endJunctionID:
    #         print(
    #             'Warning: cannot export edge "%s" because start and end junctions are identical.'
    #             % self.id
    #         )
    #         return

    #     if len(self.wayPoints) < 2:
    #         print(
    #             'Warning: cannot export edge "%s" because it has less than 2 way-points.'
    #             % self.id
    #         )
    #         return

    #     # Create the forward edge
    #     if self.forwardLanes > 0:
    #         edge = ET.SubElement(edges, "edge")
    #         edge.attrib["id"] = self.id
    #         edge.attrib["from"] = self.startJunctionID
    #         edge.attrib["to"] = self.endJunctionID
    #         edge.attrib["numLanes"] = str(self.forwardLanes)
    #         edge.attrib["width"] = str(self.laneWidth)
    #         edge.attrib["shape"] = Road._pathToString(self.originalPath)
    #     # Create the backward edge
    #     if self.backwardLanes > 0:
    #         edge = ET.SubElement(edges, "edge")
    #         edge.attrib["id"] = "-" + self.id
    #         edge.attrib["to"] = self.startJunctionID
    #         edge.attrib["from"] = self.endJunctionID
    #         edge.attrib["numLanes"] = str(self.backwardLanes)
    #         edge.attrib["width"] = str(self.laneWidth)
    #         edge.attrib["shape"] = Road._pathToString(self.originalPath[::-1])

    @classmethod
    def _pathToString(cls, path):
        s = ""
        for coord in path:
            s += "%f,%f " % (coord[0], coord[1])
        return s

    @classmethod
    def print(cls):
        print("Roads Num:", len(cls.roads))
        for road in cls.roads.values():
            print(
                "name:",
                road.name,
                "startJunction:",
                road.startJunction.name if road.startJunction else "None",
                "endJunction:",
                road.endJunction.name if road.endJunction else "None",
            )

    def visualize(
        self,
        show=True,
        save_path=None,
        ax=None,
        color="blue",
        alpha=0.7,
        show_waypoints=True,
        show_info=True,
        show_name=True,
    ):
        """
        可视化Road对象，根据坐标、类型、长度和角度信息绘制Road图像

        Args:
            show (bool): 是否显示图像
            save_path (str): 保存图像的路径，如果为None则不保存
            ax (matplotlib.axes.Axes): 现有的坐标轴对象，如果为None则创建新的
            color (str): 道路颜色
            alpha (float): 透明度
            show_waypoints (bool): 是否显示路径点
            show_info (bool): 是否显示道路详细信息
            show_name (bool): 是否在道路中央显示名称

        Returns:
            matplotlib.axes.Axes: 绘图坐标轴对象
        """
        if not MATPLOTLIB_AVAILABLE:
            print("Error: matplotlib not available. Cannot visualize road.")
            return None

        if len(self.wayPoints) < 2:
            print(
                f"Warning: Road {self.name} has less than 2 waypoints, cannot visualize."
            )
            return ax

        # 创建新的图形或使用现有的坐标轴
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 8))
            new_figure = True
        else:
            new_figure = False

        # 绘制道路宽度（在底层）
        if len(self.originalPath) >= 2:
            self._draw_road_width(ax, self.originalPath, color, alpha * 0.3)

        # 绘制道路中心线（在中层）
        if len(self.originalPath) >= 2:
            coords_array = np.array(self.originalPath)
            ax.plot(
                coords_array[:, 0],
                coords_array[:, 1],
                color=color,
                linewidth=3,
                alpha=alpha,
                zorder=3,
            )

        # 显示路径点（在最下层，避免遮挡）
        if len(self.originalPath) >= 2:
            coords_array = np.array(self.originalPath)
            ax.scatter(
                coords_array[:, 0],
                coords_array[:, 1],
                color="red",
                s=30,
                zorder=1,
                alpha=0.8,
            )

            # 标注起点和终点 - 沿线段方向偏移，避免重合
            start_offset = self._calculate_label_offset(coords_array, 0, is_start=True)
            end_offset = self._calculate_label_offset(coords_array, -1, is_start=False)

            # 构建Start和End标注文本，包含junction信息
            start_text = "Start"
            end_text = "End"

            # 加深标注颜色
            darker_color = self._darken_color(color)

            ax.annotate(
                start_text,
                coords_array[0][:2],  # 只取前两个坐标 (x, y)
                xytext=start_offset,
                textcoords="offset points",
                fontsize=6,
                color=darker_color,
                ha="center",
                va="center",
                zorder=10,
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8),
            )
            ax.annotate(
                end_text,
                coords_array[-1][:2],  # 只取前两个坐标 (x, y)
                xytext=end_offset,
                textcoords="offset points",
                fontsize=6,
                color=darker_color,
                ha="center",
                va="center",
                zorder=10,
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8),
            )

        # 在道路中央显示道路名称
        road_coords = [
            [x + self.translation[0], y + self.translation[1]]
            for [x, y, z] in self.wayPoints
        ]
        if len(road_coords) >= 2:
            # 计算道路的几何中心点
            mid_point = self._calculate_road_center(road_coords)

            # 显示道路名称（优先使用name，如果没有则使用id）
            display_name = self.name
            if display_name:
                ax.annotate(
                    display_name,
                    mid_point,
                    ha="center",
                    va="center",
                    fontsize=10,
                    fontweight="bold",
                    color="black",
                    bbox=dict(
                        boxstyle="round,pad=0.3",
                        facecolor="white",
                        alpha=0.9,
                        edgecolor="gray",
                    ),
                    zorder=15,
                )

        # 显示详细道路信息（如果启用）
        if show_info and len(road_coords) >= 2:
            # 在道路中点偏移位置显示详细信息
            mid_point = self._calculate_road_center(road_coords)

            info_text = f"Lanes: {self.lanes}\nForward Lanes: {self.forwardLanes}"

            ax.annotate(
                info_text,
                mid_point,
                xytext=(15, 25),
                textcoords="offset points",
                fontsize=6,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
                zorder=12,
            )

        # 设置图形属性
        if new_figure:
            ax.set_title(f"Road Visualization: {self.name}")
            ax.set_xlabel("X Coordinate (m)")
            ax.set_ylabel("Y Coordinate (m)")
            ax.grid(True, alpha=0.3)
            ax.axis("equal")

            # 保存图像
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches="tight")

            # 显示图像
            if show:
                plt.show()
            elif new_figure:
                plt.close()

        return ax

    def _draw_road_width(self, ax, road_coords, color, alpha):
        """
        绘制道路宽度区域

        Args:
            ax: matplotlib坐标轴
            road_coords: 道路坐标点列表
            color: 颜色
            alpha: 透明度
        """
        if len(road_coords) < 2:
            return

        try:
            # 使用shapely计算道路边界
            line = LineString(road_coords)

            # 计算道路两侧边界
            left_boundary = line.parallel_offset(
                self.backwardLanes * self.laneWidth, "left"
            )
            right_boundary = line.parallel_offset(
                self.forwardLanes * self.laneWidth, "right"
            )

            # 绘制道路区域
            if hasattr(left_boundary, "coords") and hasattr(right_boundary, "coords"):
                left_coords = list(left_boundary.coords)
                right_coords = list(right_boundary.coords)

                # 创建闭合的多边形（在最底层）
                polygon_coords = left_coords + right_coords[::-1]
                polygon = patches.Polygon(
                    polygon_coords,
                    closed=True,
                    facecolor=color,
                    alpha=alpha,
                    edgecolor="none",
                    zorder=2,
                )
                ax.add_patch(polygon)

        except Exception as e:
            # 如果shapely计算失败，使用简化的矩形绘制
            print(f"Warning: Failed to draw road width for {self.name}: {e}")
            self._draw_simple_road_width(ax, road_coords, color, alpha)

    def _draw_simple_road_width(self, ax, road_coords, color, alpha):
        """
        使用简化方法绘制道路宽度（当shapely计算失败时的备选方案）

        Args:
            ax: matplotlib坐标轴
            road_coords: 道路坐标点列表
            color: 颜色
            alpha: 透明度
        """
        if len(road_coords) < 2:
            return

        half_width = self.width / 2.0

        for i in range(len(road_coords) - 1):
            p1 = np.array(road_coords[i])
            p2 = np.array(road_coords[i + 1])

            # 计算垂直方向
            direction = p2 - p1
            length = np.linalg.norm(direction)
            if length > 0:
                direction = direction / length
                perpendicular = np.array([-direction[1], direction[0]])

                # 计算矩形的四个顶点
                corners = [
                    p1 + perpendicular * half_width,
                    p1 - perpendicular * half_width,
                    p2 - perpendicular * half_width,
                    p2 + perpendicular * half_width,
                ]

                # 绘制矩形（在最底层）
                polygon = patches.Polygon(
                    corners,
                    closed=True,
                    facecolor=color,
                    alpha=alpha,
                    edgecolor="none",
                    zorder=2,
                )
                ax.add_patch(polygon)

    @classmethod
    def visualize_all_roads(
        cls,
        roads=None,
        show=True,
        save_path=None,
        figsize=(15, 10),
        show_waypoints=True,
        show_info=False,
        show_names=True,
    ):
        """
        可视化所有Road对象

        Args:
            roads (list): Road对象列表，如果为None则使用cls.roads
            show (bool): 是否显示图像
            save_path (str): 保存图像的路径
            figsize (tuple): 图像大小
            show_waypoints (bool): 是否显示路径点
            show_info (bool): 是否显示道路信息
            show_names (bool): 是否在道路中央显示名称

        Returns:
            matplotlib.axes.Axes: 绘图坐标轴对象
        """
        if not MATPLOTLIB_AVAILABLE:
            print("Error: matplotlib not available. Cannot visualize roads.")
            return None

        if roads is None:
            roads = cls.roads.values()

        if not roads:
            print("No roads to visualize.")
            return None

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 绘制所有道路
        for i, road in enumerate(roads):
            if len(road.wayPoints) < 2:
                continue

            # 使用循环颜色
            colors = [
                "blue",
                "green",
                "red",
                "orange",
                "purple",
                "brown",
                "pink",
                "gray",
            ]
            color = colors[i % len(colors)]

            # 绘制道路
            road.visualize(
                show=False,
                ax=ax,
                color=color,
                show_waypoints=show_waypoints,
                show_info=show_info,
                show_name=show_names,
            )

        # 设置图形属性
        ax.set_title("All Roads Visualization")
        ax.set_xlabel("X Coordinate (m)")
        ax.set_ylabel("Y Coordinate (m)")
        ax.grid(True, alpha=0.3)
        ax.axis("equal")

        # 添加统计信息
        stats_text = f"Total Roads: {len(roads)}\n"
        type_counts = {}
        for road in roads:
            type_counts[road.roadType] = type_counts.get(road.roadType, 0) + 1

        for road_type, count in type_counts.items():
            stats_text += f"{road_type}: {count}\n"

        ax.text(
            0.02,
            0.98,
            stats_text,
            transform=ax.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
        )

        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        # 显示图像
        if show:
            plt.show()
        else:
            plt.close()

        return ax

    def get_road_info(self):
        """
        获取道路的详细信息

        Returns:
            dict: 包含道路信息的字典
        """
        info = {
            "name": self.name,
            "type": self.roadType,
            "width": self.width,
            "lanes": self.lanes,
            "forward_lanes": self.forwardLanes,
            "backward_lanes": self.backwardLanes,
            "one_way": self.oneWay,
            "speed_limit_kmh": (
                self.speedLimit * 3.6 if hasattr(self, "speedLimit") else None
            ),
            "translation": self.translation,
            "rotation": self.rotation,
            "waypoints_count": len(self.wayPoints),
            "road_length": self._calculate_road_length(),
        }
        return info

    def _calculate_road_length(self):
        """
        计算道路长度

        Returns:
            float: 道路长度（米）
        """
        if len(self.wayPoints) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(self.wayPoints) - 1):
            p1 = np.array(self.wayPoints[i][:2])  # 只使用x, y坐标
            p2 = np.array(self.wayPoints[i + 1][:2])
            total_length += np.linalg.norm(p2 - p1)

        return total_length

    def _calculate_road_center(self, road_coords):
        """
        计算道路的几何中心点

        Args:
            road_coords: 道路坐标点列表 [[x, y], [x, y], ...]

        Returns:
            list: 中心点坐标 [x, y]
        """
        if len(road_coords) < 2:
            return road_coords[0] if road_coords else [0, 0]

        if len(road_coords) == 2:
            # 对于只有两个点的直线道路，返回中点
            p1 = np.array(road_coords[0])
            p2 = np.array(road_coords[1])
            return ((p1 + p2) / 2).tolist()

        # 返回中间索引的点
        mid_idx = len(road_coords) // 2
        return road_coords[mid_idx]

    def _calculate_label_offset(self, coords_array, point_index, is_start=True):
        """
        计算Start/End标注的偏移位置，向线段内部偏移避免重合

        Args:
            coords_array: 坐标数组
            point_index: 点的索引（0为起点，-1为终点）
            is_start: 是否为起点

        Returns:
            tuple: (x_offset, y_offset) 偏移量（像素单位）

        逻辑说明:
        - Start标注：从起点向第二个点的方向偏移（向线段内部）
        - End标注：从终点向倒数第二个点的方向偏移（向线段内部）
        """
        if len(coords_array) < 2:
            return (0, 15) if is_start else (0, -15)

        # 获取当前点和相邻点
        if is_start:
            current_point = coords_array[0]
            next_point = coords_array[1]
            # 起点：沿着从起点到下一点的方向偏移
            direction = next_point - current_point
        else:
            current_point = coords_array[-1]
            prev_point = coords_array[-2]
            # 终点：沿着从前一点到终点的方向偏移
            direction = current_point - prev_point

        # 计算方向向量的长度
        length = np.linalg.norm(direction)
        if length == 0:
            return (0, 15) if is_start else (0, -15)

        # 归一化方向向量
        direction = direction / length

        # 计算偏移距离（像素）
        offset_distance = 15  # 偏移距离，缩小一点

        # 沿线段方向偏移
        offset_x = direction[0] * offset_distance
        offset_y = direction[1] * offset_distance

        # 起点向线段内部偏移（正方向），终点向线段内部偏移（反方向）
        if is_start:
            return (offset_x, offset_y)  # 起点向线段内部偏移
        else:
            return (-offset_x, -offset_y)  # 终点向线段内部偏移

    def _darken_color(self, color, factor=0.7):
        """
        加深颜色

        Args:
            color: 原始颜色（字符串或RGB）
            factor: 加深因子，0-1之间，越小越深

        Returns:
            str: 加深后的颜色
        """
        try:
            import matplotlib.colors as mcolors

            # 如果是颜色名称，先转换为RGB
            if isinstance(color, str):
                if color in mcolors.CSS4_COLORS:
                    rgb = mcolors.to_rgb(color)
                elif color.startswith("#"):
                    rgb = mcolors.to_rgb(color)
                else:
                    # 对于基本颜色名称
                    rgb = mcolors.to_rgb(color)
            else:
                rgb = color

            # 加深颜色（乘以因子）
            darker_rgb = tuple(c * factor for c in rgb)

            # 转换回十六进制颜色
            return mcolors.to_hex(darker_rgb)

        except Exception:
            # 如果转换失败，返回一些预定义的深色
            color_mapping = {
                "blue": "#000080",  # 深蓝
                "green": "#006400",  # 深绿
                "red": "#8B0000",  # 深红
                "orange": "#FF4500",  # 深橙
                "purple": "#4B0082",  # 深紫
                "brown": "#654321",  # 深棕
                "pink": "#C71585",  # 深粉
                "gray": "#2F4F4F",  # 深灰
                "cyan": "#008B8B",  # 深青
                "yellow": "#B8860B",  # 深黄
            }
            return color_mapping.get(color.lower(), "#000000")  # 默认黑色
