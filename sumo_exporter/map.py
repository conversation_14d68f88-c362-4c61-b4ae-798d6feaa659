import optparse
import os
import sys

from lxml import etree as ET

from crossroad import Crossroad
from node_extractor import NodeExtractor
from road import Road


class Map(object):
    def __init__(self, wbt_file, output_dir):
        self.wbt_file = wbt_file
        self.output_dir = output_dir
        self.crossroads = dict()
        self.junctions = dict()

    def load(self):
        """Load the map from the Webots file."""
        nodeExtractor = NodeExtractor(self.wbt_file)
        # 提取Road
        self.__load_road(nodeExtractor)

        # 提取Crossroad
        self.__load_crossroad(nodeExtractor)

        # Crossroad 和 Road关联
        self.__combine_crossroad_road()

        # 创建 Road 之间连接的junction
        self.__create_crossroad_between_roads()

        # 创建 Road 未连接的起始和结束点junction
        self.__create_single_crossroad()

        Road.print()
        Crossroad.print()

    def __load_road(self, nodeExtractor):
        # 提取Road
        roadTypes = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]
        for roadType in roadTypes:
            for roadString in nodeExtractor.extractRootNodes(roadType):
                road = Road(roadString, roadType)
                if road.name in Road.roads:
                    print(f"Warning: Road {road.name} already exists. Skipping.")
                    continue
                Road.roads[road.name] = road

    def __load_crossroad(self, nodeExtractor):
        # 提取Crossroad
        crossroadTypes = ["Crossroad", "RoadIntersection", "LaneSeparation"]
        for crossroadType in crossroadTypes:
            for crossroadString in nodeExtractor.extractRootNodes(crossroadType):
                crossroad = Crossroad(crossroadType)
                crossroad.init_from_wbt_string(crossroadString)
                if crossroad.name in Crossroad.crossroads:
                    print(
                        f"Warning: Crossroad {crossroad.name} already exists. Skipping."
                    )
                    continue
                Crossroad.crossroads[crossroad.name] = crossroad

    def __combine_crossroad_road(self):
        # Crossroad 和 Road关联
        for crossroad in Crossroad.crossroads.values():
            crossroad.roads = []
            for roadID in crossroad.connectedRoadIDs:
                if roadID not in Road.roads:
                    print(
                        f"Warning: crossroad {crossroad.name} connectedRoadIDs {roadID} not found. Skipping."
                    )
                    continue

                road = Road.roads[roadID]
                crossroad.roads.append(road)
                distance_start = (
                    (road.originalPath[0][0] - crossroad.translation[0]) ** 2
                    + (road.originalPath[0][1] - crossroad.translation[1]) ** 2
                ) ** 0.5
                distance_end = (
                    (road.originalPath[-1][0] - crossroad.translation[0]) ** 2
                    + (road.originalPath[-1][1] - crossroad.translation[1]) ** 2
                ) ** 0.5
                if distance_start < distance_end:
                    if distance_start > 10:
                        print(
                            f"Warning: crossroad {crossroad.name} is far from road {road.name} start. Distance: {distance_start}."
                        )
                    road.startJunction = crossroad
                else:
                    if distance_end > 10:
                        print(
                            f"Warning: crossroad {crossroad.name} is far from road {road.name} end. Distance: {distance_end}."
                        )
                    road.endJunction = crossroad

    def __create_crossroad_between_roads(self):
        """Create junctions from roads."""
        point_list = []
        for road in Road.roads.values():
            point_list.append([road.originalPath[0], road.name, "start", False])
            point_list.append([road.originalPath[-1], road.name, "end", False])

        for i in range(len(point_list)):
            close_point = []
            for j in range(i + 1, len(point_list)):
                if point_list[i][3] or point_list[j][3]:
                    continue

                distance = (
                    (point_list[i][0][0] - point_list[j][0][0]) ** 2
                    + (point_list[i][0][1] - point_list[j][0][1]) ** 2
                ) ** 0.5
                if distance < 0.5:
                    close_point.append(j)

            if close_point:
                crossroad = Crossroad("RoadJunction")
                crossroad.name = "junction_" + point_list[i][1]
                crossroad.connectedRoadIDs.append(point_list[i][1])
                if point_list[i][2] == "start":
                    Road.roads[point_list[i][1]].startJunction = crossroad
                else:
                    Road.roads[point_list[i][1]].endJunction = crossroad
                crossroad.translation = point_list[i][0]
                point_list[i][3] = True

                for j in close_point:
                    crossroad.name += "_" + point_list[j][1]
                    crossroad.connectedRoadIDs.append(point_list[j][1])
                    if point_list[j][2] == "start":
                        Road.roads[point_list[j][1]].startJunction = crossroad
                    else:
                        Road.roads[point_list[j][1]].endJunction = crossroad
                    point_list[j][3] = True

                if crossroad.name not in Crossroad.crossroads:
                    Crossroad.crossroads[crossroad.name] = crossroad
                else:
                    print(
                        f"Warning: Crossroad {crossroad.name} already exists. Skipping."
                    )

    def __create_single_crossroad(self):
        for road in Road.roads.values():
            if road.startJunction is None:
                crossroad = Crossroad("SingleJunction")
                crossroad.name = "junction_" + road.name + "_start"
                crossroad.connectedRoadIDs.append(road.name)
                crossroad.translation = road.originalPath[0]
                road.startJunction = crossroad
                if crossroad.name not in Crossroad.crossroads:
                    Crossroad.crossroads[crossroad.name] = crossroad
                else:
                    print(
                        f"Warning: Crossroad {crossroad.name} already exists. Skipping."
                    )
            if road.endJunction is None:
                crossroad = Crossroad("SingleJunction")
                crossroad.name = "junction_" + road.name + "_end"
                crossroad.connectedRoadIDs.append(road.name)
                crossroad.translation = road.originalPath[-1]
                road.endJunction = crossroad
                if crossroad.name not in Crossroad.crossroads:
                    Crossroad.crossroads[crossroad.name] = crossroad
                else:
                    print(
                        f"Warning: Crossroad {crossroad.name} already exists. Skipping."
                    )

    def visualize(self):
        """Visualize the map."""
        try:
            import matplotlib.pyplot as plt

            # 创建图形
            fig, ax = plt.subplots(figsize=(15, 10))

            # 绘制所有道路
            for i, road in enumerate(Road.roads.values()):
                if len(road.wayPoints) < 2:
                    continue

                # 使用循环颜色
                colors = [
                    "blue",
                    "green",
                    "red",
                    "orange",
                    "purple",
                    "brown",
                    "pink",
                    "gray",
                ]
                color = colors[i % len(colors)]

                # 绘制道路
                road.visualize(
                    show=False,
                    ax=ax,
                    color=color,
                    show_waypoints=True,
                    show_info=False,
                    show_name=True,
                )

            # 绘制所有十字路口
            for i, crossroad in enumerate(Crossroad.crossroads.values()):
                # 使用循环颜色
                colors = [
                    "red",
                    "orange",
                    "purple",
                    "brown",
                    "pink",
                    "gray",
                    "olive",
                    "cyan",
                ]
                color = colors[i % len(colors)]

                # 绘制十字路口
                crossroad.visualize(
                    show=False,
                    ax=ax,
                    color=color,
                    show_name=True,
                    show_connected_roads=False,
                )

            # 设置图形属性
            ax.set_title("Roads and Crossroads Visualization")
            ax.set_xlabel("X Coordinate (m)")
            ax.set_ylabel("Y Coordinate (m)")
            ax.grid(True, alpha=0.3)
            ax.axis("equal")

            # 添加统计信息
            stats_text = f"Total Roads: {len(Road.roads)}\n"
            road_type_counts = {}
            for road in Road.roads.values():
                road_type_counts[road.roadType] = (
                    road_type_counts.get(road.roadType, 0) + 1
                )

            for road_type, count in road_type_counts.items():
                stats_text += f"{road_type}: {count}\n"

            if 1:
                stats_text += f"\nTotal Crossroads: {len(Crossroad.crossroads)}\n"
                crossroad_type_counts = {}
                for crossroad in Crossroad.crossroads.values():
                    crossroad_type_counts[crossroad.crossroadType] = (
                        crossroad_type_counts.get(crossroad.crossroadType, 0) + 1
                    )

                for crossroad_type, count in crossroad_type_counts.items():
                    stats_text += f"{crossroad_type}: {count}\n"

            ax.text(
                0.02,
                0.98,
                stats_text,
                transform=ax.transAxes,
                verticalalignment="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            )
            plt.show()

            # # 保存图像
            # if 0:
            #     plt.savefig(save_path, dpi=300, bbox_inches="tight")

            # # 显示图像
            # if show:
            # else:
            #     plt.close()

        except ImportError:
            print("Error: matplotlib not available. Cannot create visualization.")

    def export_to_sumo(self):
        """Export the map to SUMO files."""
        # Check options.
        if not os.path.isdir(self.output_dir):
            sys.exit("Output directory does not exist. Please create it.")

        # Compute the output file paths.
        nodesFilePath = os.path.join(self.output_dir, "sumo.nod.xml")
        edgesFilePath = os.path.join(self.output_dir, "sumo.edg.xml")
        configFilePath = os.path.join(self.output_dir, "sumo.sumocfg")

        # SUMO Node
        nodes = ET.Element("nodes")
        # road之间的junctions点
        for crossroad in Crossroad.crossroads.values():
            node = ET.SubElement(nodes, "node")
            node.attrib["id"] = crossroad.name
            node.attrib["x"] = str(crossroad.translation[0])
            node.attrib["y"] = str(crossroad.translation[1])
        tree = ET.ElementTree(nodes)
        tree.write(
            nodesFilePath, encoding="utf-8", xml_declaration=True, pretty_print=True
        )

        # SUMO edges
        edges = ET.Element("edges")
        for road in Road.roads.values():
            # Create the forward edge
            if road.forwardLanes > 0:
                edge = ET.SubElement(edges, "edge")
                edge.attrib["id"] = road.name
                edge.attrib["from"] = road.startJunction.name
                edge.attrib["to"] = road.endJunction.name
                edge.attrib["numLanes"] = str(road.forwardLanes)
                edge.attrib["width"] = str(road.laneWidth)
                edge.attrib["shape"] = Road._pathToString(road.originalPath)
            # Create the backward edge
            if road.backwardLanes > 0:
                edge = ET.SubElement(edges, "edge")
                edge.attrib["id"] = "-" + road.name
                edge.attrib["to"] = road.startJunction.name
                edge.attrib["from"] = road.endJunction.name
                edge.attrib["numLanes"] = str(road.backwardLanes)
                edge.attrib["width"] = str(road.laneWidth)
                edge.attrib["shape"] = Road._pathToString(road.originalPath[::-1])
        tree = ET.ElementTree(edges)
        tree.write(
            edgesFilePath, encoding="utf-8", xml_declaration=True, pretty_print=True
        )

        # Export SUMO configuration file
        configuration = ET.Element("configuration")
        tree = ET.ElementTree(configuration)
        input = ET.SubElement(configuration, "input")
        ET.SubElement(input, "net-file", value="sumo.net.xml")
        ET.SubElement(input, "route-files", value="sumo.rou.xml")
        time = ET.SubElement(configuration, "time")
        ET.SubElement(time, "begin", value="0")
        report = ET.SubElement(configuration, "report")
        ET.SubElement(report, "verbose", value="true")
        ET.SubElement(report, "no-step-log", value="true")
        tree.write(
            configFilePath, encoding="utf-8", xml_declaration=True, pretty_print=True
        )
