#include <lanelet2_core/LaneletMap.h>
#include <lanelet2_io/Io.h>
#include "webots_road.hpp"
#include "webots_node_extractor.hpp"
#include "had_map_utils/local_osm_parser.hpp"
#include "had_map_utils/had_map_parse.hpp"
int main(int argc, char **argv)
{
    if (argc < 3) {
        std::cerr << "Usage: " << argv[0] << " <wbt_file> <output_osm_file>" << std::endl;
        return 1;
    }
    std::string wbt_file = argv[1];
    std::string map_filename = argv[2];
    lanelet::LaneletMap map;
    std::cout << "Extracting root nodes from wbt file..." << std::endl;
    try {
        WebotsNodeExtractor extractor(wbt_file);
        std::vector<std::string> nodes = extractor.extractRootNodes("StraightRoadSegment");
        for (const auto &node : nodes) {
            auto road = WebotsRoad(node, "StraightRoadSegment");
            road.addRoadToLanelet(map);
        }

        std::vector<std::string> nodes_CurvedRoadSegment = extractor.extractRootNodes("CurvedRoadSegment");
        for (const auto &node : nodes_CurvedRoadSegment) {
            auto road = WebotsRoad(node, "CurvedRoadSegment");
            road.addRoadToLanelet(map);
        }
        lanelet::ErrorMessages errors;
        const lanelet::projection::LocalProjector projector;

        uslam::common::had_map_utils::osmMapWrite(map_filename, map);
    } catch (const std::exception &ex) {
        std::cerr << ex.what() << std::endl;
    }

    return 0;
}