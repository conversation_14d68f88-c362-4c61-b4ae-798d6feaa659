cmake_minimum_required(VERSION 3.10 FATAL_ERROR)
project(wbt_osm_transform)

find_package(ament_cmake REQUIRED)
find_package(had_map_utils REQUIRED)
find_package(lanelet2_projection REQUIRED)
find_package(uauto REQUIRED)

add_executable(wbt_osm_transform wbt_osm_transform.cpp)
target_link_libraries(wbt_osm_transform
    had_map_utils::had_map_utils
)
target_include_directories(wbt_osm_transform PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

#############
## Test    ##
#############
# if(BUILD_TESTING)
#     find_package(ament_cmake_gtest REQUIRED)
#     ament_add_gtest(test_lanelet2_map_provider  
#         test/test_lanelet2_map_provider.cpp
#     )

#     target_link_libraries(test_lanelet2_map_provider
#         gtest
#         gtest_main
#         lanelet2_map_provider
#         -lpthread
#     )

#     install(TARGETS test_lanelet2_map_provider
#         RUNTIME DESTINATION bin
#         LIBRARY DESTINATION lib
#         ARCHIVE DESTINATION lib
#     )

#     add_executable(test_map_pub test/test_map_pub.cpp)
#     target_link_libraries(test_map_pub map_manager_module)
# endif()



#############
## Install ##
#############

install(DIRECTORY include/ DESTINATION include)

install(TARGETS wbt_osm_transform
   RUNTIME DESTINATION bin
   LIBRARY DESTINATION lib
   ARCHIVE DESTINATION lib
)

ament_package()