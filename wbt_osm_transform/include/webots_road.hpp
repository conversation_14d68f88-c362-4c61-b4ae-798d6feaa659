#include <iostream>
#include <vector>
#include <string>
#include <cmath>
#include <regex>
#include <sstream>
#include "lanelet2_core/LaneletMap.h"
// #include "pugixml.hpp" // 使用 pugixml 作为 XML 库

static const std::string floatNumRex = R"(([+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?))";
static const std::string stringRegex = R"("([^"]*)\")";
static const std::string intNumRex = R"((\d+))";

class WebotsRoad
{
public:
    std::string roadType;
    std::string id;map/include/webots_node_extractor.hpp
    double width = 7;
    double speedLimit = 10;  // 默认 10 km/h
    std::vector<double> translation = {0.0, 0.0, 0.0};
    std::vector<double> rotation = {0.0, 0.0, 1.0, 0.0};
    std::string startJunctionID;
    std::string endJunctionID;
    int lanes = 2;
    int forwardLanes = 1;
    int backwardLanes = 1;
    bool oneWay = false;
    std::vector<std::string> roadLineTypes;

    struct RoadLine {
        std::string direction;
        std::string type;
        double offset;
        int index;  // 最左边一条为0，依次递增
        std::vector<std::vector<double>> points;
    };
    std::vector<RoadLine> lines;

    // CurvedRoadSegment
    double curvatureRadius = 10.0;
    double totalAngle = 1.5708;  // 默认值（90度，弧度制）
    int subdivision = 16;        // 默认值

    // StraightRoadSegment
    double length = 10.0;

    WebotsRoad(const std::string &wbtString, const std::string &roadTypeIn)
    {
        roadType = roadTypeIn;
        parseWBTString(wbtString, roadType);
    }

    void addRoadToLanelet(lanelet::LaneletMap &laneletMap)
    {
        // 创建道路
        std::map<int, std::pair<lanelet::LineString3d, RoadLine>> lineString_map;
        for (const auto &line : lines) {
            lanelet::Points3d points;
            for (int i = 0; i < line.points.size(); i++) {
                auto &wayPoint = line.points[i];
                if (i == 0 || i == line.points.size() - 1) {
                    auto nearest_pt = lanelet::geometry::findNearest(
                        laneletMap.pointLayer, lanelet::BasicPoint2d(wayPoint[0], wayPoint[1]), 1);
                    if (!nearest_pt.empty() && nearest_pt.front().first < 1e-2) {
                        points.push_back(nearest_pt.front().second);
                        continue;
                    }
                }
                points.push_back(lanelet::Point3d(lanelet::utils::getId(), wayPoint[0], wayPoint[1], 0.0));
            }
            lanelet::LineString3d lineString(lanelet::utils::getId(), points);
            if (line.type != "dashed") {
                lineString.setAttribute("subtype", "solid");
                lineString.setAttribute("type", "line_thin");
            } else {
                lineString.setAttribute("subtype", "dashed");
                lineString.setAttribute("type", "line_thin");
            }
            lineString_map.insert({line.index, {lineString, line}});
        }

        auto setLineType = [](lanelet::LineString3d &line, std::string type) {
            line.setAttribute("subtype", type);
            line.setAttribute("type", "line_thin");
            // std::cout << "set type" << type << std::endl;
        };
        auto setLaneletType = [this](lanelet::Lanelet &lanelet) {
            lanelet.setAttribute("subtype", "road");
            lanelet.setAttribute("speed_limit", speedLimit);
        };

        auto line_cur = lineString_map.begin();
        auto line_next = lineString_map.begin();
        line_next++;
        for (; line_next != lineString_map.end(); line_cur++, line_next++) {
            if (line_cur->second.second.direction == "forward" || line_next->second.second.direction == "forward") {
                lanelet::Lanelet roadLanelet(lanelet::utils::getId(), line_cur->second.first, line_next->second.first);
                printf("add road lanelet forward left:%f id:%d  right:%f  id:%d\n", line_cur->second.second.offset,
                       line_cur->second.first.id(), line_next->second.second.offset, line_next->second.first.id());
                setLaneletType(roadLanelet);
                laneletMap.add(roadLanelet);
            } else {
                lanelet::Lanelet roadLanelet(lanelet::utils::getId(), line_next->second.first, line_cur->second.first);
                setLaneletType(roadLanelet);
                laneletMap.add(roadLanelet);
            }
        }
    }

private:
    void parseWBTString(const std::string &wbtString, const std::string &roadType)
    {
        // printf("Parsing road: %s\n", wbtString.c_str());
        printf("***********************************\n");
        // 使用正则表达式解析 wbtString
        std::smatch match;

        // 解析 id
        std::string idRegex = R"(id\s*)" + stringRegex;
        if (std::regex_search(wbtString, match, std::regex(idRegex))) {
            id = match[1];
        }

        // 解析 width
        std::string widthRegex = R"(width\s*)" + floatNumRex;
        if (std::regex_search(wbtString, match, std::regex(widthRegex))) {
            width = std::stod(match[1]);
        }

        // 解析 speedLimit
        std::string speedLimitRegex = R"(speedLimit\s*)" + floatNumRex;
        if (std::regex_search(wbtString, match, std::regex(speedLimitRegex))) {
            speedLimit = std::stod(match[1]);
        }

        // 解析 translation
        std::string translationRegex = R"(translation\s+)" + floatNumRex + R"(\s+)" + floatNumRex + R"(\s+)" + floatNumRex;
        if (std::regex_search(wbtString, match, std::regex(translationRegex))) {
            translation = {std::stod(match[1]), std::stod(match[5]), std::stod(match[9])};
        } else {
            translation[0] = 0.0;
            translation[1] = 0.0;
            translation[2] = 0.0;
            printf("use default translation\n");
        }

        // 解析 rotation
        std::string rotationRegex =
            R"(rotation\s+)" + floatNumRex + R"(\s+)" + floatNumRex + R"(\s+)" + floatNumRex + R"(\s+)" + floatNumRex;
        if (std::regex_search(wbtString, match, std::regex(rotationRegex))) {
            rotation = {std::stod(match[1]), std::stod(match[5]), std::stod(match[9]), std::stod(match[13])};
        } else {
            rotation[0] = 0.0;
            rotation[1] = 0.0;
            rotation[2] = 1.0;
            rotation[3] = 0.0;
            printf("use default rotation\n");
        }

        // 解析 startJunctionID
        std::string startJunctionRegex = R"(startJunction\s*)" + stringRegex;
        if (std::regex_search(wbtString, match, std::regex(startJunctionRegex))) {
            startJunctionID = match[1];
        }

        // 解析 endJunctionID
        std::string endJunctionRegex = R"(endJunction\s*)" + stringRegex;
        if (std::regex_search(wbtString, match, std::regex(endJunctionRegex))) {
            endJunctionID = match[1];
        }

        // 解析 lanes
        std::string lanesRegex = R"(numberOfLanes\s*)" + intNumRex;
        if (std::regex_search(wbtString, match, std::regex(lanesRegex))) {
            lanes = std::stoi(match[1]);
        }

        // 解析 forwardLanes
        std::string forwardLanesRegex = R"(numberOfForwardLanes\s*)" + intNumRex;
        if (std::regex_search(wbtString, match, std::regex(forwardLanesRegex))) {
            forwardLanes = std::stoi(match[1]);
        }

        printf(
            "Road id:%s width::%f speedLimit:%f translation:%f %f %f rotation:%f %f %f %f startJunction:%s "
            "endJunction:%s lanes:%d forwardLanes:%d\n",
            id.c_str(), width, speedLimit, translation[0], translation[1], translation[2], rotation[0], rotation[1],
            rotation[2], rotation[3], startJunctionID.c_str(), endJunctionID.c_str(), lanes, forwardLanes);

        // 解析 RoadLine
        std::string roadLineRegexStr = R"(RoadLine\s*\{\s*([\s\S]*?)\s*\})";  // 匹配每个 RoadLine 块
        std::string typeRegexStr = R"(type\s*)" + stringRegex;                // 匹配 type 字段
        std::regex roadLineRegex(roadLineRegexStr);                           // extended 用于匹配多行
        auto roadLinesBegin = std::sregex_iterator(wbtString.begin(), wbtString.end(), roadLineRegex);
        auto roadLinesEnd = std::sregex_iterator();

        // 遍历所有 RoadLine 块
        for (std::sregex_iterator it = roadLinesBegin; it != roadLinesEnd; ++it) {
            std::string roadLineContent = (*it)[1].str();  // 获取 RoadLine 内容

            // 检查是否存在 type 字段
            std::regex typeRegex(typeRegexStr);
            if (std::regex_search(roadLineContent, match, typeRegex)) {
                roadLineTypes.push_back(match[1]);  // 提取 type 字段值
            } else {
                roadLineTypes.push_back("dashed");  // 如果没有 type 字段，为虚线 dashed
            }
        }

        printf("Road roadline size:%zu\n", roadLineTypes.size());
        for (int i = 0; i < roadLineTypes.size(); i++) printf("i:%d type:%s\n", i, roadLineTypes[i].c_str());

        // 计算 backwardLanes 和 oneWay
        backwardLanes = lanes - forwardLanes;
        oneWay = backwardLanes == 0;

        // 根据 roadType 解析 wayPoints
        if (roadType == "Road") {
            parseRoadWayPoints(wbtString);
        } else if (roadType == "StraightRoadSegment") {
            parseStraightRoadSegment(wbtString);
        } else if (roadType == "CurvedRoadSegment") {
            parseCurvedRoadSegment(wbtString);
        }
    }

    void parseRoadWayPoints(const std::string &wbtString)
    {
        // std::regex wayPointsRegex(R"(wayPoints\s*\[([^\]]*)\])");
        // std::smatch match;
        // if (std::regex_search(wbtString, match, wayPointsRegex)) {
        //     std::istringstream iss(match[1]);
        //     std::vector<double> points;
        //     double value;
        //     while (iss >> value) {
        //         points.push_back(value);
        //     }
        //     originWayPoints = grouper(3, points);
        // }
    }

    void parseStraightRoadSegment(const std::string &wbtString)
    {
        auto setLineData = [this](int i_offset, RoadLine &line) {
            // 点
            std::vector<std::vector<double>> points = {{0, line.offset, 0}, {length, line.offset, 0}};
            line.points = transformWaypoints(points);

            // 序号和类型
            int road_index = 0;  //  最左边为0
            if (line.direction == "center") {
                road_index = lanes - forwardLanes;
            } else if (line.direction == "forward") {
                road_index = lanes - forwardLanes + i_offset;
            } else if (line.direction == "backward") {
                road_index = lanes - forwardLanes - i_offset;
            }
            if (road_index - 1 >= roadLineTypes.size() || road_index - 1 < 0) {
                line.type = "solid";
                line.index = road_index;
            } else {
                line.type = roadLineTypes[road_index - 1];
                line.index = road_index;
            }
            printf("line: index:%d offset:%f direction:%s type:%s pt:(%f,%f)->(%f,%f)\n", line.index, line.offset,
                   line.direction.c_str(), line.type.c_str(), line.points[0][0], line.points[0][1], line.points[1][0],
                   line.points[1][1]);
        };

        std::string lengthRegex = R"(length\s*)" + floatNumRex;
        std::smatch match;
        if (std::regex_search(wbtString, match, std::regex(lengthRegex))) {
            length = std::stod(match[1]);
        }
        printf("StraightRoad length: %f\n", length);

        RoadLine center_line;
        center_line.offset = (forwardLanes - backwardLanes) * 0.5 * width / lanes;
        center_line.direction = "center";
        setLineData(0, center_line);
        lines.push_back(center_line);

        for (int i = 1; i <= forwardLanes; i++) {
            RoadLine line;
            line.offset = center_line.offset - i * 1.0 * (width / lanes);
            line.direction = "forward";
            setLineData(i, line);
            lines.push_back(line);
        }
        for (int i = 1; i <= backwardLanes; i++) {
            RoadLine line;
            line.offset = center_line.offset + i * 1.0 * (width / lanes);
            line.direction = "backward";
            setLineData(i, line);
            lines.push_back(line);
        }
    }

    void parseCurvedRoadSegment(const std::string &wbtString)
    {
        auto createWayPoints = [](double radius, double angle, double subdivision, double offset) {
            std::vector<std::vector<double>> points;
            for (int i = 0; i <= subdivision; ++i) {
                double x = (radius + offset) * sin(i * angle / subdivision);
                double y = (radius + offset) * cos(i * angle / subdivision);
                points.push_back({x, y, 0});
            }
            return points;
        };
        auto setLineData = [this](double radius, double angle, double subdivision, int i_offset, RoadLine &line) {
            // 点
            std::vector<std::vector<double>> points;
            for (int i = 0; i <= subdivision; ++i) {
                double x = (radius + line.offset) * sin(i * angle / subdivision);
                double y = (radius + line.offset) * cos(i * angle / subdivision);
                points.push_back({x, y, 0});
            }
            line.points = transformWaypoints(points);

            // 序号和类型
            int road_index = 0;  //  最左边为0
            if (line.direction == "center") {
                road_index = lanes - forwardLanes;
            } else if (line.direction == "forward") {
                road_index = lanes - forwardLanes + i_offset;
            } else if (line.direction == "backward") {
                road_index = lanes - forwardLanes - i_offset;
            }
            if (road_index - 1 >= roadLineTypes.size() || road_index - 1 < 0) {
                if (road_index == 0 || road_index == lanes) {
                    line.type = "solid";
                } else {
                    line.type = "dashed";
                }
                line.index = road_index;
            } else {
                line.type = roadLineTypes[road_index - 1];
                line.index = road_index;
            }
            printf("line: index:%d offset:%f direction:%s type:%s pt:(%f,%f)->(%f,%f)\n", line.index, line.offset,
                   line.direction.c_str(), line.type.c_str(), line.points[0][0], line.points[0][1], line.points[1][0],
                   line.points[1][1]);
        };

        // 构建具体字段的正则表达式
        std::string curvatureRadiusRegexStr = R"(curvatureRadius\s+)" + floatNumRex;
        std::string totalAngleRegexStr = R"(totalAngle\s+)" + floatNumRex;
        std::string subdivisionRegexStr = R"(subdivision\s+)" + intNumRex;

        // 使用正则表达式解析 wbtString
        std::smatch match;

        // 解析 curvatureRadius
        std::regex curvatureRadiusRegex(curvatureRadiusRegexStr);
        if (std::regex_search(wbtString, match, curvatureRadiusRegex)) {
            curvatureRadius = std::stod(match[1]);
        }

        // 解析 totalAngle
        std::regex totalAngleRegex(totalAngleRegexStr);
        if (std::regex_search(wbtString, match, totalAngleRegex)) {
            totalAngle = std::stod(match[1]);
        }

        // 解析 subdivision
        std::regex subdivisionRegex(subdivisionRegexStr);
        if (std::regex_search(wbtString, match, subdivisionRegex)) {
            subdivision = std::stoi(match[1]);
        }

        printf("CurvedRoad radius: %f, totalAngle: %f, subdivision: %d\n", curvatureRadius, totalAngle, subdivision);

        RoadLine center_line;
        center_line.offset = -(forwardLanes - backwardLanes) * 0.5 * width / lanes;
        center_line.direction = "center";
        setLineData(curvatureRadius, totalAngle, subdivision, 0, center_line);
        lines.push_back(center_line);

        for (int i = 1; i <= forwardLanes; i++) {
            RoadLine line;
            line.offset = center_line.offset + i * 1.0 * (width / lanes);
            line.direction = "forward";
            setLineData(curvatureRadius, totalAngle, subdivision, i, line);
            lines.push_back(line);
        }
        for (int i = 1; i <= backwardLanes; i++) {
            RoadLine line;
            line.offset = center_line.offset - i * 1.0 * (width / lanes);
            line.direction = "backward";
            setLineData(curvatureRadius, totalAngle, subdivision, i, line);
            lines.push_back(line);
        }
    }

    std::vector<std::vector<double>> transformWaypoints(std::vector<std::vector<double>> wayPoints)
    {
        std::vector<std::vector<double>> output;
        Eigen::Isometry3f transform = Eigen::Isometry3f::Identity();
        double qx = rotation[0] * sin(rotation[3] * 0.5);
        double qy = rotation[1] * sin(rotation[3] * 0.5);
        double qz = rotation[2] * sin(rotation[3] * 0.5);
        double qw = cos(rotation[3] * 0.5);
        transform.rotate(Eigen::Quaternionf(qw, qx, qy, qz));
        transform.pretranslate(Eigen::Vector3f(translation[0], translation[1], translation[2]));

        for (auto &wayPoint : wayPoints) {
            Eigen::Vector3f input_pt = Eigen::Vector3f(wayPoint[0], wayPoint[1], wayPoint[2]);
            Eigen::Vector3f output_pt = transform * input_pt;
            wayPoint[0] = output_pt.x();
            wayPoint[1] = output_pt.y();
            wayPoint[2] = output_pt.z();
            // printf("input:%f %f %f output:%f %f %f\n", input_pt.x(), input_pt.y(), input_pt.z(), wayPoint[0],
            //        wayPoint[1], wayPoint[2]);
            output.push_back(wayPoint);
        }
        return output;
    }

    std::string pathToString(const std::vector<std::pair<double, double>> &path)
    {
        std::ostringstream oss;
        for (const auto &coord : path) {
            oss << coord.first << "," << coord.second << " ";
        }
        return oss.str();
    }

    template <typename T>
    static std::vector<std::vector<T>> grouper(int n, const std::vector<T> &vec)
    {
        std::vector<std::vector<T>> result;
        for (size_t i = 0; i < vec.size(); i += n) {
            result.emplace_back(vec.begin() + i, vec.begin() + std::min(i + n, static_cast<size_t>(vec.size())));
        }
        return result;
    }
};