#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <regex>

class WebotsNodeExtractor
{
public:
    // 构造函数，读取文件内容
    WebotsNodeExtractor(const std::string &wbtFilename)
    {
        std::ifstream file(wbtFilename);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + wbtFilename);
        }
        std::string line;
        while (std::getline(file, line)) {
            wbtContent += line + "\n";
        }
        file.close();
    }

    // 提取指定节点名称的根节点字符串
    std::vector<std::string> extractRootNodes(const std::string &nodeName)
    {
        std::vector<std::string> nodes;
        size_t pos = 0;

        // 使用正则表达式匹配节点名称后跟左花括号
        std::string pattern = "[^a-zA-Z]" + nodeName + "\\s*\\{";
        std::regex regexPattern(pattern);

        // 遍历所有匹配项
        auto words_begin = std::sregex_iterator(wbtContent.begin(), wbtContent.end(), regexPattern);
        auto words_end = std::sregex_iterator();

        for (std::sregex_iterator i = words_begin; i != words_end; ++i) {
            std::smatch match = *i;
            size_t start = match.position() + match.length();  // 匹配结束位置（即左花括号后）

            std::string nodeString = nodeName + " {";
            int curlyBracketCounter = 1;

            // 遍历字符直到找到完整的节点结束
            for (size_t index = start; index < wbtContent.length(); ++index) {
                char c = wbtContent[index];
                nodeString += c;

                if (c == '{') {
                    ++curlyBracketCounter;
                } else if (c == '}') {
                    --curlyBracketCounter;
                }

                if (curlyBracketCounter == 0) {
                    break;  // 花括号平衡，节点结束
                }
            }

            nodes.push_back(nodeString);
        }

        return nodes;
    }

private:
    std::string wbtContent;  // 文件内容
};