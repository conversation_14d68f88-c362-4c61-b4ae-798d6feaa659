cmake_minimum_required(VERSION 3.10 FATAL_ERROR)
project(ground_truth_process_task)

# 生成版本描述字符串类似 TAG-X-gHASH
execute_process(COMMAND git describe --abbrev=6 --dirty --always --tags
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../
    OUTPUT_VARIABLE  GIT_REPO_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
# 获取最新 commit 日期，YYYY-MM-DD
execute_process(COMMAND git log -1 --format=%cd --date=short
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../
    OUTPUT_VARIABLE  GIT_REPO_DATE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
# 获取最新 commit Hash
execute_process(COMMAND git log -1 --format=%H
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../
    OUTPUT_VARIABLE  GIT_REPO_COMMIT
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

SET(CMAKE_BUILD_TYPE "Release")
option(DEBUG_MODE "Build Debug Version" OFF)
if(${CMAKE_SYSTEM_PROCESSOR} STREQUAL "aarch64")
  SET(CMAKE_CXX_FLAGS "-std=c++17 \
        -O2 -fno-omit-frame-pointer")
else()
  SET(CMAKE_CXX_FLAGS "-std=c++17 \
        -msse -msse2 -msse3 -msse4 -msse4.1 -msse4.2    \
        -O2 -fno-omit-frame-pointer")
endif()
if(DEBUG_MODE)
  SET(CMAKE_BUILD_TYPE "RelWithDebInfo")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
endif ()
add_compile_options(-std=c++17)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

find_package(ament_cmake REQUIRED)
find_package(uauto REQUIRED)
find_package(PCL  REQUIRED)

add_library(ground_truth_process_task
  src/ground_truth_process_module.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
                           $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
#                           $<INSTALL_INTERFACE:include>
)
target_link_libraries(${PROJECT_NAME}  
${PCL_LIBRARIES}
${PCL_LIBRARY_DIRS}/libpcl_io_ply.so
)

ament_target_dependencies(${PROJECT_NAME}
uauto
)

## install and export this project
add_executable(${PROJECT_NAME}_node node/ground_truth_process_node.cpp)
target_link_libraries(${PROJECT_NAME}_node
                      ${PROJECT_NAME}
                      ${PCL_LIBRARIES}
                      ${PCL_LIBRARY_DIRS}/libpcl_io_ply.so
                      ament_index_cpp::ament_index_cpp)

target_compile_definitions(${PROJECT_NAME}_node PRIVATE 
        COMPILE_TIME="${COMPILE_TIME}" 
        GIT_REPO_VERSION="${GIT_REPO_VERSION}"
        GIT_REPO_DATE="${GIT_REPO_DATE}"
        GIT_REPO_COMMIT="${GIT_REPO_COMMIT}"
)
                      
install(DIRECTORY include/ DESTINATION include)

# 安装配置文件
install(DIRECTORY config/ DESTINATION share/${PROJECT_NAME}/config)

install(TARGETS ${PROJECT_NAME}
        EXPORT export_${PROJECT_NAME}
        LIBRARY DESTINATION lib  
        ARCHIVE DESTINATION lib  
        RUNTIME DESTINATION bin  
)
ament_export_targets(export_${PROJECT_NAME} HAS_LIBRARY_TARGET)
install(TARGETS ${PROJECT_NAME}_node
        LIBRARY DESTINATION lib  
        ARCHIVE DESTINATION lib  
        RUNTIME DESTINATION bin  
)
#export dependencies of this project 
ament_export_dependencies(uauto)

ament_package() 