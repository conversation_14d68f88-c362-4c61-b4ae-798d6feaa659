---
include:
  - project: "pub/ci_templates"
    file: "/cpp.lint.gitlab-ci.yml"
  - project: "pub/ci_templates"
    file: "/py.lint.gitlab-ci.yml"

stages:
  - lint

lint:cpp:
  stage: lint
  tags:
    - docker
  image: ${CI_REGISTRY}/pub/docker/clang-format:latest
  variables:
    CLANG_FORMAT_CONFIG: "project"
    CLANG_FORMAT_BIN: clang-format-12
  script:
    - |
      if [ $CLANG_FORMAT_CONFIG != "project" ]; then
        cp -f ~/.clang-format ./;
        echo "Using general clang-format configuration";
      else
        echo "Using project-level clang-format configuration";
      fi
    - find . -regextype egrep -regex ".*\.(c|cc|cpp|h|hh|hpp)$" |
      xargs ${CLANG_FORMAT_BIN} -i
    - dirty_lines=$(bash -c "git status --short |
      grep -E '(.cc|.hh|.c|.h|.cpp|.hpp)$' | cat")
    - |
      if [[ -n $dirty_lines ]]; then
        echo "Please fix the coding style of these files.";
        git diff --stat;
        exit 1;
      else
        echo "Coding style check passed.";
      fi
  only:
    - merge_requests
