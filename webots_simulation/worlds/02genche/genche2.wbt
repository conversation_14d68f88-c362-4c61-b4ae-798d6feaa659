#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CrossRoadsTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BuildingUnderConstruction.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Auditorium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/FastFoodRestaurant.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/SimpleBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Forest.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/SimpleTree.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/YieldSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwayPole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwaySign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Crossroad.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/range_rover/RangeRoverSportSVRSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTrailerSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterDriver.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeDriver.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BungalowStyleHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuildingWithRoundFront.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
EXTERNPROTO "webots_simulation/protos/ForestDense.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation 0.34366545662921155 -0.6226315368691158 -0.7030106849940749 4.781874139406488
  position -125.8509574728769 58.12612411022118 273.4792453534224
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
SumoInterface {
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND Solid {
  boundingObject DEF GROUND_PLANE Plane {
    size 2000 2000
  }
  locked TRUE
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
StraightRoadSegment {
  translation 75 -215 0.02
  rotation 0 0 -1 3.14156
  name "road(2)"
  id "13"
  startJunction "36"
  width 6
  numberOfLanes 1
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 250
}
StraightRoadSegment {
  translation 75 -230 0.02
  rotation 0 0 -1 3.14156
  name "road(2)"
  id "13"
  startJunction "36"
  width 6
  numberOfLanes 1
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 250
}
CurvedRoadSegment {
  translation -64.5 4.5 0.02
  rotation 0 0 1 1.5708
  id "0"
  startJunction "40"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -105 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(1)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation -64.5 -64.5 0.02
  rotation 0 0 1 3.14156
  name "road(2)"
  id "2"
  startJunction "38"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -64.5 -105 0.02
  name "road(3)"
  id "3"
  startJunction "38"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation 4.5 -64.5 0.02
  rotation 0 0 1 -1.5708
  name "road(4)"
  id "4"
  startJunction "25"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation 45 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "43"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 4.5 4.5 0.02
  name "road(6)"
  id "6"
  startJunction "44"
  endJunction "43"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -25.5 45 0.02
  name "road(7)"
  id "7"
  startJunction "26"
  endJunction "44"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation -4.5 -4.5 0.02
  rotation 0 0 1 3.14156
  name "road(8)"
  id "8"
  startJunction "42"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -4.5 -45 0.02
  name "road(9)"
  id "9"
  startJunction "42"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 64.5 -4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(10)"
  id "10"
  startJunction "45"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 75 -97.5 0.02
  rotation 0 0 1 -1.5708
  name "road(11)"
  id "11"
  startJunction "35"
  endJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 90
}
StraightRoadSegment {
  translation 85.5 93 0.02
  rotation 0 0 1 3.14156
  name "road(12)"
  id "12"
  startJunction "27"
  endJunction "28"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 9
}
StraightRoadSegment {
  translation 75 -187.5 0.02
  rotation 0 0 -1 3.14156
  name "road(13)"
  id "13"
  startJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 250
}
StraightRoadSegment {
  translation 165 52.5 0.02
  rotation 0 0 1 -1.5708
  name "road(14)"
  id "14"
  startJunction "34"
  endJunction "35"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 150
}
StraightRoadSegment {
  translation 64.5 231 0.02
  rotation 0 0 1 3.14156
  name "road(15)"
  id "15"
  startJunction "32"
  width 16.5
  numberOfLanes 3
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 200
}
StraightRoadSegment {
  translation 105 112.5 0.02
  rotation 0 0 1 1.57079
  name "road(16)"
  id "16"
  startJunction "27"
  endJunction "33"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
StraightRoadSegment {
  translation 105 -4.5 0.02
  rotation 0 0 1 1.57079
  name "road(17)"
  id "17"
  startJunction "45"
  endJunction "27"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
CurvedRoadSegment {
  translation 76.5 133.5 0.02
  rotation 0 0 -1 3.14156
  name "road(18)"
  id "18"
  startJunction "28"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 64.5 190.5 0.02
  name "road(19)"
  id "19"
  startJunction "32"
  endJunction "33"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 124.5 52.5 0.02
  name "road(20)"
  id "20"
  startJunction "27"
  endJunction "34"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  name "road(21)"
  id "21"
  startJunction "30"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  rotation 0 0 1 1.5708
  name "road(22)"
  id "22"
  startJunction "31"
  endJunction "30"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -45 25.5 0.02
  rotation 0 0 -1 1.5708
  name "road(23)"
  id "23"
  startJunction "26"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
StraightRoadSegment {
  translation -45 64.5 0.02
  rotation 0 0 1 1.5708
  name "road(24)"
  id "24"
  startJunction "26"
  endJunction "31"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CrossRoadsTrafficLight {
  translation 45 -45 0
}
DEF STONES Solid {
  translation 5.03891 -136.158 -4.23581
  children [
    DEF STONES_GROUP Group {
      children [
        Pose {
          translation 0 2 -0.6
          children [
            Shape {
              appearance DEF OBJECTS_APPEARANCE PBRAppearance {
                baseColor 0.5 0.5 0.5
                roughness 1
                metalness 0
              }
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0.5 -3.5 -0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 4 2 -0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 6 -1 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 9 0 0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 5.5 -5 0.2
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0 0 0.05
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 10 5 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 1 6 0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13 -4 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13.5 1.5 0.4
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
      ]
    }
  ]
  name "solid(2)"
  boundingObject USE STONES_GROUP
}
BuildingUnderConstruction {
  translation 78.5008 143.08 0
}
CommercialBuilding {
  translation 70.9574 31.6315 0
}
UBuilding {
  translation -87.1466 81.9927 0
}
UBuilding {
  translation -33.0931 -148.903 0
  rotation 0 0 1 1.5708
  name "U building(1)"
}
HollowBuilding {
}
Hotel {
  translation -9.97953 71.6228 0
}
LargeResidentialTower {
  translation -17.51 105.22 0
  numberOfFloors 12
}
BungalowStyleHouse {
  translation 19.65 99.5 0
}
TheThreeTowers {
  translation 68.118 -90.636 0
}
TheThreeTowers {
  translation -128.998 -89.456 0
  rotation 0 0 1 0.523599
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation 68.6779 -9.29537 0
  rotation 0 0 1 1.5708
}
Auditorium {
  translation -63.9296 -61.9719 0
  rotation 0 0 1 0.654496
}
Auditorium {
  translation 148.81 132.46 0
  rotation 0 0 1 -3.0106953071795863
  name "auditorium(1)"
}
Museum {
  translation -0.191182 -68.6571 0
  rotation 0 0 1 1.5708
}
Museum {
  translation 110.334 -65.2535 0
  rotation 0 0 1 2.0944
  name "museum(1)"
}
ComposedHouse {
  translation -26.35 0 0
}
ResidentialBuildingWithRoundFront {
  translation 33.5652 -136.667 0
  rotation 0 0 1 1.8326
  name "residential building(1)"
}
ResidentialBuilding {
  translation -69.274 -1.81329 0
}
FastFoodRestaurant {
  translation 63.46 212.26 0
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
FastFoodRestaurant {
  translation 48.60864 48.933131 0
  rotation 0 0 -1 -2.8797953071795863
  name "fast food restaurant(1)"
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
SimpleBuilding {
  translation 46.9364 208.338 0
  wallType "glass highrise"
}
SimpleBuilding {
  translation 66.8317 195.97 0
  name "building(1)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 97.8047 -149.501 0
  name "building(2)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 117.415 -135.778 0
  name "building(3)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 126.106 -115.963 0
  name "building(4)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -134.064 -16.533 0
  name "building(14)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 139.996 -95.9459 0
  name "building(5)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 78.2778 175.999 0
  name "building(6)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 57.9852 259.763 0
  name "building(7)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "classic building"
}
SimpleBuilding {
  translation -53.2255 258.571 0
  name "building(8)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
}
SimpleBuilding {
  translation -56.5761 182.166 0
  rotation 0 0 -1 2.618
  name "building(9)"
  floorHeight 6
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  groundFloorScale [
    0
  ]
  roofType "old tiles"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -77.6413 142.999 0
  rotation 0 0 1 3.14159
  name "building(2)"
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -72.4649 -133.213 0
  rotation 0 0 1 5.23599
  name "building(10)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -131.19 52.9749 0
  rotation 0 0 1 -2.8797853071795863
  name "building(15)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -22.0181 258.921 0
  name "building(11)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "factory building"
}
SimpleBuilding {
  translation 20.4483 259.676 0
  name "building(12)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "tall house"
}
SimpleBuilding {
  translation -3.91795 144.18 0
  name "building(13)"
  wallType "residential building"
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -105.8 -117
    -99 -150.8
    -69.7 -174
    -186 -176.3
    -205.3 -140.5
    -195 -117
  ]
  density 0.3
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -80 -118
    -73.7 -141
    -49 -153
    64.3 -153
    12 -117
  ]
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -7.6 116.2
    72.4 116.5
    174.5 99.2
    175.8 191.2
    -56.5 122.9
  ]
}
SimpleTree {
  translation -86.3803 -3.69635 0
}
SimpleTree {
  translation -86.3803 -18.9264 0
  name "tree(1)"
}
SimpleTree {
  translation -43.078 -123.055 0
  name "tree(2)"
}
SimpleTree {
  translation -22.318 -123.055 0
  name "tree(3)"
}
SimpleTree {
  translation 1.70201 -123.055 0
  name "tree(4)"
}
SimpleTree {
  translation 20.4431 130.5 0
  name "tree(5)"
}
SimpleTree {
  translation 53.4842 123.995 0
  rotation 0 0 1 5.49779
  name "tree(6)"
}
SimpleTree {
  translation 83.0494 196.584 0
  name "tree(7)"
}
SimpleTree {
  translation -33.9112 179.976 0
  rotation 0 -1 0 6.28319
  name "tree(8)"
}
SimpleTree {
  translation -59.8734 114.947 0
  name "tree(9)"
}
SimpleTree {
  translation -22.2335 150.308 0
  rotation 0 -1 0 6.28319
  name "tree(10)"
}
SimpleTree {
  translation 16.4343 145.045 0
  name "tree(11)"
}
SimpleTree {
  translation 89.3742 77.3517 0
  name "tree(12)"
}
SimpleTree {
  translation 89.3742 59.8317 0
  name "tree(13)"
}
SimpleTree {
  translation 89.3742 42.5017 0
  name "tree(14)"
}
SimpleTree {
  translation 89.3742 24.6817 0
  name "tree(15)"
}
SimpleTree {
  translation 89.3742 6.71175 0
  name "tree(16)"
}
SimpleTree {
  translation 89.3742 -10.6583 0
  name "tree(17)"
}
SimpleTree {
  translation 137.36 -53.2389 0
  name "tree(18)"
}
SimpleTree {
  translation 122.111 -32.1201 0
  name "tree(19)"
}
SimpleTree {
  translation 110.762 -29.023 0
  name "tree(20)"
}
SimpleTree {
  translation 90.773 -54.456 0
  name "tree(21)"
}
SimpleTree {
  translation 40.292 -105.225 0
  name "tree(22)"
}
SimpleTree {
  translation -29.8047 -59.5626 0
  name "tree(23)"
}
SimpleTree {
  translation -56.111 -34.7085 0
  name "tree(24)"
}
SimpleTree {
  translation -86.3803 -38.0164 0
  name "tree(25)"
}
SimpleTree {
  translation -86.3803 9.02365 0
  name "tree(26)"
}
CautionSign {
  translation -91.9275 48.9391 0
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_left.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 45.9944 -94.6291 0
  rotation 0 0 1 -2.4871
  name "caution sign(1)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/pedestrian_crossing.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(2)"
}
CautionSign {
  translation 33.842 10.5534 0
  rotation 0 0 1 1.7017
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/bump.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 84.1154 -26.9475 0
  rotation 0 0 1 0.6545
  name "caution sign(4)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
CautionSign {
  translation -5.43669 -34.1146 0
  rotation 0 0 1 -0.5236
  name "caution sign(5)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_right.jpg"
      ]
    }
  ]
}
OrderSign {
  translation -67.6589 34.4983 0
  rotation 0 0 1 3.14159
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_right_turn.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
}
YieldSign {
  translation -55.468 66.4958 0
  rotation 0 0 1 1.5708
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 26.6277 -84.4244 0
  rotation 0 0 1 0.6545
  name "speed limit(1)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.3528 79.1341 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 87.1538 -50.335 0
  rotation 0 0 1 -3.14159
  name "speed limit(3)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 31.0289 -34.4459 0
  name "speed limit(4)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/one_way_sign_left.jpg"
      ]
    }
  ]
}
TrafficCone {
  hidden linearVelocity_0 1.5282554691661844e-15 6.136637336389231e-15 -3.966604790591735e-14
  hidden angularVelocity_0 -3.785631774048444e-14 9.399856516316552e-15 4.2871061181894804e-17
  translation 41.181608523319426 -54.869090377077995 0.23797576345451846
  rotation 0.7068402576257043 0.5358553741164556 0.4617746942288425 -2.4681182771203365
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -3.757537796382859e-15 -5.1342170758898104e-15 -3.790745456681273e-14
  hidden angularVelocity_0 3.173716110567228e-14 -2.3209442522789992e-14 1.675935552567083e-18
  translation 33.721549927500476 -50.63667696594388 0.23797576345451835
  rotation -0.44236039601191296 0.8490005570823964 -0.28899019726275105 -2.1287847577331647
  name "traffic cone(1)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -2.2296837172292622e-14 6.633894428748136e-14 -1.0922818448194586e-13
  hidden angularVelocity_0 -4.012650591408443e-13 -1.3486754282346596e-13 -1.0573514786396435e-18
  translation 28.073495724469122 -45.48178286032711 -0.004076718672606949
  rotation 0.0068424579514524105 0.009534634160358865 -0.9999311333890003 1.2455423457271724
  name "traffic cone(2)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -6.985705632158317e-14 8.706361991640783e-17 -1.0922818430048694e-13
  hidden angularVelocity_0 -5.248407420576202e-16 -4.2254645219011574e-13 -9.900309524004847e-19
  translation 33.71714525135761 -45.72340002636733 -0.004076718672606949
  rotation -0.00027317726281897296 0.9999998480993025 0.00047872283763935296 0.013690792774031124
  name "traffic cone(3)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 48.100535 -116.28367 0
  rotation 0 0 1 2.61799
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
HighwayPole {
  translation -17.67 -117.85 0
  rotation 0 0 -1 3.14159
  height 9
  length 12
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -4.56 0
      height 4
      length 5.5
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_bayonne.jpg"
      ]
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      name "highway sign(1)"
      height 2.5
      length 3
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_sebastian.jpg"
      ]
    }
  ]
}
HighwayPole {
  translation 118.15 72.97 0
  rotation 0 0 1 -1.5707953071795862
  name "highway pole(1)"
  stand 2
  height 8
  length 25
  thickness 0.3
  curveRadius 0.5
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -3.95 0
      height 4
      length 5.5
      color 0.6 0.6 0.6
    }
  ]
  rightVerticalSigns []
  leftVerticalSigns [
    HighwaySign {
      translation 0 0 0.58
      name "highway sign(1)"
      height 2.5
      length 3
      color 0.6 0.6 0.6
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
Crossroad {
  translation 76.500107 93 0
  id "28"
  shape []
  connectedRoadIDs [
    "18"
    "12"
  ]
}
Crossroad {
  translation 36 133.50015 0
  name "crossroad(1)"
  id "29"
  shape []
  connectedRoadIDs [
    "21"
    "18"
  ]
}
Crossroad {
  translation -4.500405 174 0
  name "crossroad(2)"
  id "30"
  shape []
  connectedRoadIDs [
    "22"
    "21"
  ]
}
Crossroad {
  translation -45.000035 133.49978 0
  name "crossroad(3)"
  id "31"
  shape []
  connectedRoadIDs [
    "24"
    "22"
  ]
}
Crossroad {
  translation 64.499851 231 0
  name "crossroad(4)"
  id "32"
  shape []
  connectedRoadIDs [
    "15"
    "19"
  ]
}
Crossroad {
  translation 104.99975 190.50007 0
  name "crossroad(5)"
  id "33"
  shape []
  connectedRoadIDs [
    "16"
    "19"
  ]
}
Crossroad {
  translation 165 52.500074 0
  name "crossroad(6)"
  id "34"
  shape []
  connectedRoadIDs [
    "20"
    "14"
  ]
}
Crossroad {
  translation 165.00028 -97.499835 0
  name "crossroad(7)"
  id "35"
  shape []
  connectedRoadIDs [
    "11"
    "14"
  ]
}
Crossroad {
  translation 75 -187.5 0
  name "crossroad(8)"
  id "36"
  shape []
  connectedRoadIDs [
    "13"
    "11"
  ]
}
Crossroad {
  translation 4.5 -104.99975 0
  name "crossroad(9)"
  id "37"
  shape []
  connectedRoadIDs [
    "3"
    "4"
  ]
}
Crossroad {
  translation -64.5 -105 0
  name "crossroad(10)"
  id "38"
  shape []
  connectedRoadIDs [
    "2"
    "3"
  ]
}
Crossroad {
  translation -104.99987 -64.499926 0
  name "crossroad(11)"
  id "39"
  shape []
  connectedRoadIDs [
    "1"
    "2"
  ]
}
Crossroad {
  translation -105 4.4999794 0
  name "crossroad(12)"
  id "40"
  shape []
  connectedRoadIDs [
    "0"
    "1"
  ]
}
Crossroad {
  translation -45.000015 -4.4999256 0
  name "crossroad(13)"
  id "41"
  shape []
  connectedRoadIDs [
    "23"
    "8"
  ]
}
Crossroad {
  translation -4.5 -45 0
  name "crossroad(14)"
  id "42"
  shape []
  connectedRoadIDs [
    "9"
    "8"
  ]
}
Crossroad {
  translation 45 4.5000744 0
  name "crossroad(15)"
  id "43"
  shape []
  connectedRoadIDs [
    "5"
    "6"
  ]
}
Crossroad {
  translation 4.4998512 45.00011 0
  name "crossroad(16)"
  id "44"
  shape []
  connectedRoadIDs [
    "7"
    "6"
  ]
}
Crossroad {
  translation 105 -4.4999256 0
  name "crossroad(17)"
  id "45"
  shape []
  connectedRoadIDs [
    "10"
    "17"
  ]
}
DEF SUMO_VEHICLE0 Solid {
  translation 24.384168784882352 236.03000000000003 0.5
  rotation 0 0 -1 3.141592653589793
  children [
    TruckSimple {
      translation -5.2 0 0
      color 0.1 0.15 0.18
      recognitionColors [
        0.1 0.15 0.18
      ]
      trailer NULL
    }
  ]
  name "routeFlow_2.0"
  linearVelocity -3.340062352269655 0 0
}
DEF SUMO_VEHICLE1 Solid {
  translation 23.99496070951513 231.03000000000003 0.5
  rotation 0 0 -1 3.141592653589793
  children [
    TruckSimple {
      translation -5.2 0 0
      color 0.85 0.85 0.05
      recognitionColors [
        0.85 0.85 0.05
      ]
      trailer NULL
    }
  ]
  name "routeFlow_3.0"
  linearVelocity -3.662835200057515 0 0
}
DEF SUMO_VEHICLE2 Solid {
  translation 24.167433030891853 226.03000000000003 0.5
  rotation 0 0 -1 3.141592653589793
  children [
    TruckSimple {
      translation -5.2 0 0
      color 0.72 0.52 0.18
      recognitionColors [
        0.72 0.52 0.18
      ]
      trailer NULL
    }
  ]
  name "routeFlow_4.0"
  linearVelocity -3.5828575846032606 0 0
}
DEF SUMO_VEHICLE3 Solid {
  translation -118.4 236.688302906935 0.36
  rotation 0 0 1 1.5707963267948966
  children [
    TeslaModel3Simple {
      translation -2.85 0 0
      color 0.1 0.15 0.18
      recognitionColors [
        0.1 0.15 0.18
      ]
    }
  ]
  name "routeFlow_0.0"
  linearVelocity 0 6.961504320600227 0
}
DEF SUMO_VEHICLE4 Solid {
  translation -81.6 227.49606508413635 0.36
  rotation 0 0 -1 1.5707963267948966
  children [
    TeslaModel3Simple {
      translation -2.85 0 0
      color 0.14 0.29 0.16
      recognitionColors [
        0.14 0.29 0.16
      ]
    }
  ]
  name "routeFlow_1.0"
  linearVelocity 0 -7.014050398085487 0
}
