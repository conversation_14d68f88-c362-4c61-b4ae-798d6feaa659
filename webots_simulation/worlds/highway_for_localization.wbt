#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "../protos/RoadIntersection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CrossRoadsTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BuildingUnderConstruction.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Auditorium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/FastFoodRestaurant.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/SimpleBuilding.proto"
EXTERNPROTO "../protos/PedestrianCrossing.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/YieldSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwayPole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwaySign.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/range_rover/RangeRoverSportSVRSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTrailerSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterDriver.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeDriver.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BungalowStyleHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuildingWithRoundFront.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Building.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/Truck.proto"
EXTERNPROTO "../protos/Forest.proto"
EXTERNPROTO "../protos/ForestCircle.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZ.proto"
EXTERNPROTO "../protos/ControlledStreetLight.proto"
EXTERNPROTO "../protos/Tunnel.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  fieldOfView 1.5707963267948966
  orientation -0.57735 0.57735 0.57735 2.0944
  position 13 -188 135
  near 1
  followType "None"
  lensFlare LensFlare {
  }
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 20000
}
DEF GROUND Solid {
  boundingObject DEF GROUND_PLANE Plane {
    size 1200 1200
  }
  locked TRUE
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 3000 3000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
  recognitionColors [
    0 0 0
  ]
}
StraightRoadSegment {
  translation -36.3847 1348.18 0.02
  name "highroad(12)"
  id "12"
  startJunction "65"
  endJunction "66"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  length 700
}
Tunnel {
  translation 313.66 1348.42 0
  radius 25
  thickness 2
  length 700
  locked TRUE
  topLightOffset 4
  topLightsPerSide 20
  topLightHeight 22
  topLightLeftIntensity 30
  topLightLeftcutOffAngle 1.5707963267948966
  topLightRightIntensity 30
  topLightRightcutOffAngle 1.5707963267948966
}
CurvedRoadSegment {
  translation -2036.38 348.184 0.02
  rotation 0 0 -1 1.5708
  name "highroad(7)"
  id "7"
  startJunction "61"
  endJunction "62"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.5
  rightBorderBoundingObject TRUE
  leftBorderBoundingObject TRUE
  rightBarrierBoundingObject FALSE
  leftBarrierBoundingObject FALSE
}
CurvedRoadSegment {
  translation -36.3784 348.18 0.02
  rotation 0 0 1 1.5708
  name "highroad(8)"
  id "8"
  startJunction "62"
  endJunction "63"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.5
}
CurvedRoadSegment {
  translation -36.3777 348.18 0.02
  rotation 0 0 1 1.0708
  name "highroad(9)"
  id "9"
  startJunction "62"
  endJunction "63"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.5
}
CurvedRoadSegment {
  translation -36.378 348.179 0.02
  rotation 0 0 1 0.5708
  name "highroad(10)"
  id "10"
  startJunction "63"
  endJunction "64"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.5
}
CurvedRoadSegment {
  translation 663.615 848.183 0.02
  name "highroad(13)"
  id "13"
  startJunction "63"
  endJunction "64"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 500
}
CurvedRoadSegment {
  translation 163.616 847.393 0.02
  rotation 0 0 -1 1.57
  name "highroad(14)"
  id "14"
  startJunction "63"
  endJunction "64"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.5
}
CurvedRoadSegment {
  translation 1919.17 -110.74 0.02
  rotation 0 0 1 2.0708003061004256
  name "highroad(15)"
  id "15"
  startJunction "64"
  endJunction "65"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 1
}
CurvedRoadSegment {
  translation 602.797 -829.88 0.02
  rotation 0 0 -1 1.0708
  name "highroad(16)"
  id "16"
  startJunction "65"
  endJunction "66"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 500
  totalAngle 2.0708
}
CurvedRoadSegment {
  translation -36.3825 348.178 0.02
  rotation 0 0 1 0.0708
  name "highroad(11)"
  id "11"
  startJunction "64"
  endJunction "65"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 1000
  totalAngle 0.0708
  locked FALSE
}
StraightRoadSegment {
  translation -1220 -929.89 0.02
  rotation 0 0 1 1.5708
  name "hightroad(5)"
  id "5"
  startJunction "59"
  endJunction "60"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  length 558.95
}
Forest {
  translation -1188 -930 0
  rotation 0 0 1 3.14159
  shape [
    -20 559
    -20 0
    20 0
    20 559
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
ForestCircle {
  translation -36.1 346.5 0
  rotation 0 0 1 2.6415953071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 989.25
  innerradius 949.25
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -36 346.5 0
  rotation 0 0 1 2.1416
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 989.25
  innerradius 949.25
  angleRange 0.5708
  segments 16
}
ForestCircle {
  translation -35.1 347.6 0
  rotation 0 0 -1 3.1415853071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 989.25
  innerradius 949.25
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -34.7 348.2 0
  rotation 0 0 -1 3.1415853071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1053.5
  innerradius 1013.5
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -34.6 348.2 0
  rotation 0 0 -1 -2.6415953071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1053.5
  innerradius 1013.5
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -36.1 346.8 0
  rotation 0 0 -1 -2.1416
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1053.5
  innerradius 1013.5
  angleRange 0.5708
  segments 16
}
Forest {
  translation -1252.36 -929.44 0
  rotation 0 0 1 3.14159
  shape [
    -20 558.5
    -20 0
    20 0
    20 558.5
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
ForestCircle {
  translation -720.5 -371 0
  rotation 0 0 1 -3.1415853071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 487.5
  innerradius 447.5
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -2037.67 348.45 0
  rotation 0 0 -1 -0.0001
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 989
  innerradius 949
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -2037.67 348.45 0
  rotation 0 0 -1 -0.0001
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1053.3
  innerradius 1013.3
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation -720.5 -371 0
  rotation 0 0 1 -3.1415853071795863
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 551.85
  innerradius 511.85
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation 663.85 848.44 0
  rotation 0 0 1 1.5708
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 551.85
  innerradius 511.85
  angleRange 1.5708
  segments 16
}
ForestCircle {
  translation 663.61 848.32 0
  rotation 0 0 1 1.5708
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 487.5
  innerradius 447.5
  angleRange 1.5708
  segments 16
}
ForestCircle {
  translation 162.9 848.4 0
  rotation 0 0 -1 0
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 988.6
  innerradius 948.5
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation 162.8 848.46 0
  rotation 0 0 -1 0
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1053.2
  innerradius 1013.4
  angleRange 0.5
  segments 16
}
ForestCircle {
  translation 1919.17 -110.74 0
  rotation 0 0 1 -2.6413
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 1052.5
  innerradius 1012.5
  angleRange 1
  segments 16
}
ForestCircle {
  translation 1919.17 -110.74 0
  rotation 0 0 1 -2.6413
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 987.5
  innerradius 948
  angleRange 1
  segments 16
}
ForestCircle {
  translation 602.9 -830.13 0
  rotation 0 0 1 0.5
  density 0.002
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 487.5
  innerradius 447.5
  angleRange 2.0708
  segments 16
}
ForestCircle {
  translation 602.9 -830.13 0
  rotation 0 0 1 0.5
  density 0.002
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 552.5
  innerradius 512.5
  angleRange 2.0708
  segments 16
}
CurvedRoadSegment {
  translation -720 -370.95 0.02
  rotation 0 0 1 1.5708
  name "highroad(6)"
  id "6"
  startJunction "60"
  endJunction "61"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 500
  totalAngle 0.5
}
CurvedRoadSegment {
  translation -820 -929.88 0.02
  rotation 0 0 1 -3.1415853071795863
  name "highroad(4)"
  id "4"
  startJunction "58"
  endJunction "59"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 400
}
StraightRoadSegment {
  translation -19.9 -1329.88 0.02
  rotation 0 0 -1 -3.1415853071795863
  name "hightroad(3)"
  id "3"
  startJunction "57"
  endJunction "58"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  length 800.1
}
StraightRoadSegment {
  translation 602.796 -1329.88 0.02
  rotation 0 0 -1 -3.1415853071795863
  name "hightroad(17)"
  id "17"
  startJunction "66"
  endJunction "67"
  width 21.5
  numberOfLanes 4
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  length 581.2
  locked FALSE
}
AdvertisingBoard {
  translation -61.9995 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(16)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
CurvedRoadSegment {
  translation -64.5 4.5 0.02
  rotation 0 0 1 1.5708
  name "road(0)"
  id "0"
  startJunction "40"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
Building {
  translation 188.83 0 0
  name "building(2)"
}
Building {
  translation 188.83 19.44 0
  name "building(2)"
}
Building {
  translation 188.83 39.21 0
  name "building(2)"
}
Building {
  translation 188.83 58.99 0
  name "building(2)"
}
Building {
  translation 188.83 78.76 0
  name "building(2)"
}
Building {
  translation 188.76 -19.93 0
  name "building(2)"
}
Building {
  translation 188.69 -39.28 0
  name "building(2)"
}
Building {
  translation 188.55 -56.4 0
  name "building(2)"
}
Building {
  translation 188.45 -75.98 0
  name "building(2)"
}
Building {
  translation 188.45 -94.62 0
  name "building(2)"
}
Building {
  translation 188.45 -114.15 0
  name "building(2)"
}
StraightRoadSegment {
  translation -105 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(1)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 99
}
StraightRoadSegment {
  translation -105 -115.5 0.02
  rotation 0 0 1 -1.5708
  name "road(2)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 31.5
}
CurvedRoadSegment {
  translation -64.5 -147 0.02
  rotation 0 0 1 3.14156
  name "road(42)"
  id "2"
  startJunction "38"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -94.5 -105 0.02
  name "road(3)"
  id "3"
  startJunction "38"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 99
}
CurvedRoadSegment {
  translation 4.5 -64.5 0.02
  rotation 0 0 1 -1.5708
  name "road(4)"
  id "4"
  startJunction "25"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
RoadIntersection {
  translation 45 -45 0.02
  rotation 0 0 1 0.785398
  name "roadintersection"
  id "25"
  connectedRoadIDs [
    "road(9)"
    "road(10)"
    "road(4)"
    "road(5)"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
StraightRoadSegment {
  translation 45 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "43"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 4.5 4.5 0.02
  name "road(6)"
  id "6"
  startJunction "44"
  endJunction "43"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -25.5 45 0.02
  name "road(7)"
  id "7"
  startJunction "26"
  endJunction "44"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
RoadIntersection {
  translation -45 45 0.02
  rotation 0 0 1 0.785398
  name "road intersection(1)"
  id "26"
  connectedRoadIDs [
    "road(7)"
    "road(23)"
    "road(0)"
    "road(24)"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation -105 -105 0.02
  rotation 0 0 1 0.785398
  name "road intersection(3)"
  id "26"
  connectedRoadIDs [
    "road(1)"
    "road(3)"
    "road(2)"
  ]
  roadsWidth 21.5
  startRoads FALSE
  startRoadsLength [
    2.5
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation 0.843731 -187.47 0.02
  rotation 0 0 1 -0.785395307179586
  name "road intersection(4)"
  id "26"
  connectedRoadIDs [
    "road(13)"
    "hightroad(1)"
    "road(15)"
  ]
  roadsWidth 21.5
  startRoads FALSE
  startRoadsLength [
    2.5
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation 105 93 0.02
  rotation 0 0 1 0.785398
  name "road intersection(2)"
  id "27"
  connectedRoadIDs [
    "road(12)"
    "road(17)"
    "road(20)"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation 0.848393 -1329.88 0
  rotation 0 0 1 0.7854
  name "highroad intersection(2)"
  id "2"
  connectedRoadIDs [
    "hightroad(17)"
    "hightroad(3)"
    "hightroad(1)"
  ]
  roadsWidth 21.5
  startRoadsLength [
    10
  ]
  startRoadsNumberOfLanes 4
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  locked FALSE
}
CurvedRoadSegment {
  translation -4.5 -4.5 0.02
  rotation 0 0 1 3.14156
  name "road(8)"
  id "8"
  startJunction "42"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -4.5 -45 0.02
  name "road(9)"
  id "9"
  startJunction "42"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 64.5 -4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(10)"
  id "10"
  startJunction "45"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 75 -97.5 0.02
  rotation 0 0 1 -1.5708
  name "road(11)"
  id "11"
  startJunction "35"
  endJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 90
}
StraightRoadSegment {
  translation 85.5 93 0.02
  rotation 0 0 1 3.14156
  name "road(12)"
  id "12"
  startJunction "27"
  endJunction "28"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 9
}
StraightRoadSegment {
  translation 75 -187.5 0.02
  rotation 0 0 -1 3.14156
  name "road(13)"
  id "13"
  startJunction "36"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 63.41
}
StraightRoadSegment {
  translation -9.9 -187.496 0.02
  rotation 0 0 -1 3.14156
  name "road(15)"
  id "15"
  startJunction "37"
  endJunction "38"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 54.605
}
StraightRoadSegment {
  translation 165 52.5 0.02
  rotation 0 0 1 -1.5708
  name "road(14)"
  id "14"
  startJunction "34"
  endJunction "35"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 150
}
StraightRoadSegment {
  translation 105 -4.5 0.02
  rotation 0 0 1 1.57079
  name "road(17)"
  id "17"
  startJunction "45"
  endJunction "27"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
CurvedRoadSegment {
  translation 76.5 133.5 0.02
  rotation 0 0 -1 3.14156
  name "road(18)"
  id "18"
  startJunction "28"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 124.5 52.5 0.02
  name "road(20)"
  id "20"
  startJunction "27"
  endJunction "34"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  name "road(21)"
  id "21"
  startJunction "30"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  rotation 0 0 1 1.5708
  name "road(22)"
  id "22"
  startJunction "31"
  endJunction "30"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -45 25.5 0.02
  rotation 0 0 -1 1.5708
  name "road(23)"
  id "23"
  startJunction "26"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
StraightRoadSegment {
  translation -45 64.5 0.02
  rotation 0 0 1 1.5708
  name "road(24)"
  id "24"
  startJunction "26"
  endJunction "31"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden position_0_0 3.350598059622414e-08
  hidden position2_0_0 74.69204674336285
  hidden position_0_1 3.370359787521986e-08
  hidden position2_0_1 74.31645233488706
  hidden position_0_2 58.479422364023335
  hidden position_0_3 57.66874140404725
  hidden rotation_1 1.6752990298112053e-08 0.9999999999999989 -4.545865673049928e-08 0.7061769427921868
  hidden rotation_2 -1.685179893760992e-08 -0.9999999999999993 -2.8056676781217237e-08 1.0817713512679779
  hidden rotation_3 0 1 0 4.3524307077725295
  hidden rotation_4 0 -1 0 5.163111667748612
  translation 4 -200 0.25641317686433435
  rotation 0.001497091102247994 -0.00045235497295302064 0.9999987770458573 -2.960626691375295
  name "chitu"
  controller "webots_chitu"
  controllerArgs ["webots_chitu_gt.yaml"]
}
CrossRoadsTrafficLight {
  translation 45 -45 0
}
CrossRoadsTrafficLight {
  translation 46 -45 1
  name "cross road traffic light(1)"
}
BuildingUnderConstruction {
  translation 104.031 121 0
}
CommercialBuilding {
  translation 70.9574 31.6315 0
}
UBuilding {
  translation -87.1466 81.9927 0
}
UBuilding {
  translation -33.0931 -148.903 0
  rotation 0 0 1 1.5708
  name "U building(1)"
}
HollowBuilding {
}
Hotel {
  translation -9.97953 71.6228 0
}
LargeResidentialTower {
  translation -17.51 105.22 0
  numberOfFloors 12
}
BungalowStyleHouse {
  translation 19.65 99.5 0
}
TheThreeTowers {
  translation 68.118 -90.636 0
}
TheThreeTowers {
  translation -130.504 -95.176 0
  rotation 0 0 -1 4.692820414042842e-06
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation 68.6779 -9.29537 0
  rotation 0 0 1 1.5708
}
Auditorium {
  translation -63.9296 -61.9719 0
  rotation 0 0 1 0.654496
}
Auditorium {
  translation 148.81 132.46 0
  rotation 0 0 1 -3.0106953071795863
  name "auditorium(1)"
}
Museum {
  translation -0.191182 -68.6571 0
  rotation 0 0 1 1.5708
}
Museum {
  translation 110.334 -65.2535 0
  rotation 0 0 1 2.0944
  name "museum(1)"
}
ComposedHouse {
  translation -26.35 0 0
}
ResidentialBuildingWithRoundFront {
  translation 31.1742 -127.739 0
  rotation 0 0 1 1.8326
  name "residential building(1)"
}
ResidentialBuilding {
  translation -69.274 -1.81329 0
}
FastFoodRestaurant {
  translation 33.27 203.63 0
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
FastFoodRestaurant {
  translation 48.60864 48.933131 0
  rotation 0 0 -1 -2.8797953071795863
  name "fast food restaurant(1)"
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
SimpleBuilding {
  translation 46.9364 186.088 0
  wallType "glass highrise"
}
SimpleBuilding {
  translation 66.8317 174.83 0
  name "building(1)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 117.415 -135.778 0
  name "building(3)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 126.106 -115.963 0
  name "building(4)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -134.064 -16.533 0
  name "building(14)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 71.4078 153.379 0
  name "building(6)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation -56.5761 182.166 0
  rotation 0 0 -1 2.618
  name "building(9)"
  floorHeight 6
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  groundFloorScale [
    0
  ]
  roofType "old tiles"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -77.6413 142.999 0
  rotation 0 0 1 3.14159
  name "building(2)"
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -72.4649 -133.213 0
  rotation 0 0 1 5.23599
  name "building(10)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -131.19 52.9749 0
  rotation 0 0 1 -2.8797853071795863
  name "building(15)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -3.91795 144.18 0
  name "building(13)"
  wallType "residential building"
}
PedestrianCrossing {
  translation 45 -26 -0.06
  rotation 0 0 1 -3.1415853071795863
}
PedestrianCrossing {
  translation -61.4608 45.0693 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(1)"
}
PedestrianCrossing {
  translation 26.9799 -45.1201 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(2)"
}
CautionSign {
  translation -91.9275 48.9391 0
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_left.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 45.9944 -94.6291 0
  rotation 0 0 1 -2.4871
  name "caution sign(1)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/pedestrian_crossing.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(2)"
}
CautionSign {
  translation 33.842 10.5534 0
  rotation 0 0 1 1.7017
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/bump.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 84.1154 -26.9475 0
  rotation 0 0 1 0.6545
  name "caution sign(4)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
CautionSign {
  translation -5.43669 -34.1146 0
  rotation 0 0 1 -0.5236
  name "caution sign(5)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_right.jpg"
      ]
    }
  ]
}
OrderSign {
  translation -67.6589 34.4983 0
  rotation 0 0 1 3.14159
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_right_turn.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
}
YieldSign {
  translation -55.468 66.4958 0
  rotation 0 0 1 1.5708
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 26.6277 -84.4244 0
  rotation 0 0 1 0.6545
  name "speed limit(1)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.3528 79.1341 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 87.1538 -50.335 0
  rotation 0 0 1 -3.14159
  name "speed limit(3)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 31.0289 -34.4459 0
  name "speed limit(4)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/one_way_sign_left.jpg"
      ]
    }
  ]
}
TrafficCone {
  translation 41.181608522664696 -54.869090379886984 0.23797576345451715
  rotation 0.7068402576249376 0.5358553741178995 0.4617746942283409 -2.4681182771186565
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.72154992903466 -50.63667696350416 0.23797576345451704
  rotation -0.4423603960134105 0.8490005570812835 -0.28899019726372877 -2.1287847577342784
  name "traffic cone(1)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 28.073495731623936 -45.48178288292993 0.03592328132738881
  rotation 0.006842457951429043 0.009534634203558929 -0.9999311333885885 1.2455423414318143
  name "traffic cone(2)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.688602522682224 -45.709135192706995 -0.004076718672611196
  rotation 0.0067629857717647944 0.12045947335115988 -0.992695208663471 0.11353613224778862
  name "traffic cone(3)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 48.100535 -116.28367 0
  rotation 0 0 1 2.61799
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 21.3005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(1)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 13.1005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(5)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 4.7805 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(6)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -3.5095 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(7)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -11.8395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(8)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -20.3495 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(9)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -28.6395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(10)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -36.8795 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(11)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -45.2395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(12)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -53.4595 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(13)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -61.9995 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(14)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -69.9667 -172.691 -3.86
  rotation 0 0 1 1.30899
  name "advertising board(15)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 29.5305 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(2)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 37.9205 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(3)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 8.1398 -1341.69 -3.86
  rotation 0 0 0.9999999999999999 1.5707953071795862
  name "advertising board(17)"
  backTexture [
    ""
  ]
  displayBackLight TRUE
  displayWidth 7
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 0.9298 -1341.69 -3.86
  rotation 0 0 0.9999999999999999 1.5707953071795862
  name "advertising board(18)"
  backTexture []
  displayBackLight TRUE
  displayWidth 7
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -6.3502 -1341.69 -3.86
  rotation 0 0 0.9999999999999999 1.5707953071795862
  name "advertising board(19)"
  backTexture []
  displayBackLight TRUE
  displayWidth 7
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 46.3005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(4)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
HighwayPole {
  translation -17.67 -117.85 0
  rotation 0 0 -1 3.14159
  height 9
  length 12
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -4.56 0
      height 4
      length 5.5
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_bayonne.jpg"
      ]
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      name "highway sign(1)"
      height 2.5
      length 3
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_sebastian.jpg"
      ]
    }
  ]
}
HighwayPole {
  translation 118.15 72.97 0
  rotation 0 0 1 -1.5707953071795862
  name "highway pole(1)"
  stand 2
  height 8
  length 25
  thickness 0.3
  curveRadius 0.5
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -3.95 0
      height 4
      length 5.5
      color 0.6 0.6 0.6
    }
  ]
  rightVerticalSigns []
  leftVerticalSigns [
    HighwaySign {
      translation 0 0 0.58
      name "highway sign(1)"
      height 2.5
      length 3
      color 0.6 0.6 0.6
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
OrderSign {
  translation 77.2 107.29 0
  rotation 0 0 1 1.5708
  name "order sign(1)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -2.769846666614805e-08 -0.9925026035994192 0.12222349139332105 3.5638394186202926e-07
  hidden translation_2 0 0 0
  hidden rotation_2 2.8134230669668264e-08 0.990931584599981 0.1343673868249668 3.6256089987148215e-07
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.1900144899933154e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.001198764348059e-06
  hidden translation_7 0 0 -0.08000000000000008
  hidden rotation_7 0 0 -1 0.00544829923638549
  hidden translation_9 0 0 0
  hidden rotation_9 0 -0.9999999999999999 0 0.006819409217427712
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819021100032248
  hidden translation_11 0 0 0
  hidden rotation_11 0 -0.9999999999999999 0 0.01439648752655997
  hidden translation_12 0 0 0
  hidden rotation_12 0 0.9999999999999999 0 0.014548080276717489
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014442819594125963
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.014590371722484652
  translation 78.84861492244764 111.08617891642172 0.5004975572545725
  rotation 0.004315837690078407 0.0275237548473125 -0.9996118328952184 0.3111941286356404
  name "vehicle(1)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 9.145679138526716e-08 -0.10410602277310739 0.9945662049468385 9.428203381688014e-05
  hidden translation_2 0 0 0
  hidden rotation_2 8.906478677849968e-07 0.05052172670742885 0.9987229621520207 9.504627844926232e-05
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.210199382035633e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.002086213954584e-06
  hidden translation_7 0 0 -0.08000000000000006
  hidden rotation_7 0 0 -1 0.005448298843589769
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.006819402620357378
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819027670640129
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.014396397513400367
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014548167826818361
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014442756468181564
  hidden translation_14 0 0 0
  hidden rotation_14 0 0.9999999999999999 0 0.014590434200677719
  translation 170.3424302596758 98.96093527618255 0.5004975572531608
  rotation 0.004317331620916395 0.007330427198175961 -0.9999638120876012 1.0645642508319564
  name "vehicle(2)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 6.798614983198884e-07 -0.13747031251922856 -0.9905058875016358 4.060333195016216e-05
  hidden translation_2 0 0 0
  hidden rotation_2 -2.7717793452604065e-08 0.9925107832320328 -0.12215705124198074 3.5638394186202926e-07
  hidden translation_3 0 0 0
  hidden rotation_3 0 -1 0 2.0009768404478096e-06
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 2.1900144899933154e-07
  hidden translation_7 0 0 -0.08000000000000008
  hidden rotation_7 0 0 1 0.005448298842937691
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.006819026060225699
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819404286296672
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.014548120460610329
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014396447581724928
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014590394868352414
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.014442796473797828
  translation 93.3488748983753 -167.0690081803683 0.5004975572545747
  rotation -0.00431588324400496 0.02688842094107881 0.9996291242111343 0.3184250015185593
  name "vehicle(3)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -2.867466047215313e-07 -0.06899253363698461 0.9976171762265661 0.00011830246876984722
  hidden translation_2 0 0 0
  hidden rotation_2 2.8098617522721485e-08 0.9909189080538625 0.13446084062410135 3.6256089987148215e-07
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.1900144899933154e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.0018643884357652e-06
  hidden translation_7 0 0 -0.08000000000000006
  hidden rotation_7 0 0 -1 0.005448298843834298
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.006819405081756168
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819025225581772
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.014396431201003371
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014548135083594276
  hidden translation_13 0 0 0
  hidden rotation_13 0 -0.9999999999999999 0 0.014442780107638296
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.014590410808816977
  translation 115.82629761477547 -193.5450510594915 0.5004975572530417
  rotation -0.004316970178335603 0.015829767865334743 0.9998653820479079 0.5325396874880617
  name "vehicle(4)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -1.4512675296698946e-07 -0.0801521959787355 0.9967826370276347 0.00011276112657115648
  hidden translation_2 0 0 0
  hidden rotation_2 1.5280228105284602e-06 0.04403934630713992 0.9990297973406523 0.00011404391280138666
  hidden translation_3 0 0 0
  hidden rotation_3 0 0.9999999999999999 0 2.210199382035633e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.002086213954584e-06
  hidden translation_7 0 0 -0.08000000000000007
  hidden rotation_7 0 0 -1 0.005448298843834298
  hidden translation_9 0 0 0
  hidden rotation_9 0 -0.9999999999999999 0 0.006819401305422908
  hidden translation_10 0 0 0
  hidden rotation_10 0 0.9999999999999999 0 0.00681902898453952
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.014396379554831418
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014548185310895648
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014442743867626043
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.014590446674752913
  translation 66.93298394787399 -167.2925631164689 0.500497557252512
  rotation -0.004317265783292322 0.010904326211812742 0.9999312260780853 0.75401063438423
  name "vehicle(7)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -7.915578300590613e-07 -0.04545762838662568 0.998966267709194 0.0001294591856925221
  hidden translation_2 0 0 0
  hidden rotation_2 1.6114963648429362e-06 0.03331298744952473 0.9994449684022583 0.00013150569483252813
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.210199382035633e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.002086213954584e-06
  hidden translation_7 0 0 -0.08000000000000007
  hidden rotation_7 0 0 -1 0.005448298844078828
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.0068194001040602125
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819030185120973
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.014396363135472977
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014548201291716956
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014442732351929891
  hidden translation_14 0 0 0
  hidden rotation_14 0 0.9999999999999999 0 0.0145904580773965
  translation 82.15127826125202 -164.11513621063023 0.5004975572518094
  rotation 0.004308204785862035 0.06433526567216351 -0.9979190412866242 0.13401831952214735
  name "vehicle(5)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -3.973135332716785e-07 -0.20218404444501056 0.9793475441188896 3.284174008926349e-05
  hidden translation_2 0 0 0
  hidden rotation_2 -1.2306745969619038e-07 0.23637015027761776 0.9716630856720457 3.338843173241124e-05
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.210199382035633e-07
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 2.0014206636406797e-06
  hidden translation_7 0 0 -0.08000000000000008
  hidden rotation_7 0 0 -1 0.00544829923630398
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.006819406959144404
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 0.006819023345809059
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.01439645673090802
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.014548110217000414
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 0.014442797997965827
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.014590393091236414
  translation 99.9980214110775 -161.3562401746592 0.5004975572544373
  rotation -0.004315855906520711 0.027678851540933518 0.999607550274191 0.3094780873065227
  name "vehicle(6)"
}
StraightRoadSegment {
  translation 0.84839 -198.22 0.02
  rotation 0 0 -0.9999999999999999 1.5707953071795862
  name "hightroad(1)"
  id "1"
  endJunction "56"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBarrier TRUE
  leftBarrier TRUE
  length 1110.91
}
HighwayPole {
  translation -12.8484 -335.14 0
  rotation 0 0 1 1.5708
  name "highway pole(2)"
  height 10
  length 6
}
SpeedLimitSign {
  translation -11.93 -444.21 0
  name "speed limit(5)"
  height 5
  radius 0.04
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 2.85
      rotation 0 0 1 1.5708
    }
  ]
}
SpeedLimitSign {
  translation -13.62 1334.63 0
  rotation 0 0 1 1.5708
  name "speed limit(6)"
  height 5
  radius 0.04
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 2.85
      rotation 0 0 1 1.5708
    }
  ]
}
HighwayPole {
  translation -13.7705 -569.51 0
  rotation 0 0 1 1.5708
  name "highway pole(3)"
  stand 2
  height 10
  length 30
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -6.98 0
      name "vertical sign"
    }
    HighwaySign {
      translation 0 -17.62 0
      name "vertical sign(1)"
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      translation 0 0 3.52
      name "horizontal sign"
      height 2.1
      length 3.2
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
HighwayPole {
  translation -151.99 1325.89 0
  rotation 0 0 1 -3.1415853071795863
  name "highway pole(4)"
  stand 2
  height 10
  length 30
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -6.98 0
      name "vertical sign"
    }
    HighwaySign {
      translation 0 -17.62 0
      name "vertical sign(1)"
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      translation 0 0 3.52
      name "horizontal sign"
      height 2.1
      length 3.2
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
Forest {
  translation -31.54 -200 0
  shape [
    -20 1117.7
    -20 0
    20 0
    20 1117.7
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation -51.47 -1297.68 0
  rotation 0 0 1 -1.5707953071795862
  shape [
    -20 768.5
    -20 0
    20 0
    20 768.5
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation -11.77 -1362.07 0
  rotation 0 0 1 -1.5707953071795862
  shape [
    -20 808.2
    -20 0
    20 0
    20 808.2
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation 12.54 -1362.61 0
  rotation 0 0 1 1.5708
  shape [
    -20 590.3
    -20 0
    20 0
    20 590.3
  ]
  density 0
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation 52.9 -1297.63 0
  rotation 0 0 1 1.5708
  shape [
    -20 550
    -20 0
    20 0
    20 550
  ]
  density 0
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation 32.99 -200 0
  shape [
    -20 1117.63
    -20 0
    20 0
    20 1117.63
  ]
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
ForestCircle {
  translation -820 -929.7 0
  rotation 0 0 -1 1.5708
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 388
  innerradius 348
  angleRange 1.5708
  segments 16
}
ForestCircle {
  translation -819.95 -929.46 0
  rotation 0 0 -1 1.5708
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
  radius 452.42
  innerradius 412.42
  angleRange 1.5708
  segments 16
}
Forest {
  translation -36.04 1315.78 0
  rotation 0 0 1 1.5708
  shape [
    -20 700
    -20 0
    7 0
    7 700
  ]
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
Forest {
  translation -36.1 1380.32 0
  rotation 0 0 1 1.5708
  shape [
    -7 700
    -7 0
    20 0
    20 700
  ]
  density 0.1
  groundTexture [
    "textures/ground_grass.jpg"
  ]
}
DEF vehicle_car_0 LincolnMKZ {
  hidden translation_1 0 0 0
  hidden rotation_1 1.334337059717268e-09 -0.9999999999997441 7.154368680838107e-07 0.02456801753875002
  hidden translation_2 0 0 0
  hidden rotation_2 -9.831627122601256e-09 0.9999999999986021 1.6718990049309655e-06 0.024566397525920974
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.9802322387695313e-08
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 2.9802322387695313e-08
  translation 3.959999849365528 -921.1925426433028 0.3131167334607218
  rotation 0.0014803499954811202 0.001480313314246342 -0.9999978086157902 1.5708222464063006
  name "vehicle_car_0"
  controller "participant"
  controllerArgs [
    "3.96 -202 0.4 3.96 -1312 0.4 0.4"
  ]
  supervisor TRUE
}
ControlledStreetLight {
  translation -12 -225 0
  name "street_light_0"
}
ControlledStreetLight {
  translation -12 -275 0
  name "street_light_1"
}
ControlledStreetLight {
  translation -12 -325 0
  name "street_light_2"
}
ControlledStreetLight {
  translation -12 -375 0
  name "street_light_3"
}
ControlledStreetLight {
  translation -12 -425 0
  name "street_light_4"
}
ControlledStreetLight {
  translation -12 -475 0
  name "street_light_5"
}
ControlledStreetLight {
  translation -12 -525 0
  name "street_light_6"
}
ControlledStreetLight {
  translation -12 -575 0
  name "street_light_7"
}
ControlledStreetLight {
  translation -12 -625 0
  name "street_light_8"
}
ControlledStreetLight {
  translation -12 -675 0
  name "street_light_9"
}
ControlledStreetLight {
  translation -12 -725 0
  name "street_light_10"
}
ControlledStreetLight {
  translation -12 -775 0
  name "street_light_11"
}
ControlledStreetLight {
  translation -12 -825 0
  name "street_light_12"
}
ControlledStreetLight {
  translation -12 -875 0
  name "street_light_13"
}
ControlledStreetLight {
  translation -12 -925 0
  name "street_light_14"
}
ControlledStreetLight {
  translation -12 -975 0
  name "street_light_15"
}
ControlledStreetLight {
  translation -12 -1025 0
  name "street_light_16"
}
ControlledStreetLight {
  translation -12 -1075 0
  name "street_light_17"
}
ControlledStreetLight {
  translation -12 -1125 0
  name "street_light_18"
}
ControlledStreetLight {
  translation -12 -1175 0
  name "street_light_19"
}
ControlledStreetLight {
  translation -12 -1225 0
  name "street_light_20"
}
ControlledStreetLight {
  translation -12 -1275 0
  name "street_light_21"
}
ControlledStreetLight {
  translation 13.5 -1275 0
  rotation 0 0 1 3.1415
  name "street_light_22"
}
ControlledStreetLight {
  translation 13.5 -1225 0
  rotation 0 0 1 3.1415
  name "street_light_23"
}
ControlledStreetLight {
  translation 13.5 -1125 0
  rotation 0 0 1 3.1415
  name "street_light_24"
}
ControlledStreetLight {
  translation 13.5 -1075 0
  rotation 0 0 1 3.1415
  name "street_light_25"
}
ControlledStreetLight {
  translation 13.5 -1025 0
  rotation 0 0 1 3.1415
  name "street_light_26"
}
ControlledStreetLight {
  translation 13.5 -975 0
  rotation 0 0 1 3.1415
  name "street_light_27"
}
ControlledStreetLight {
  translation 13.5 -925 0
  rotation 0 0 1 3.1415
  name "street_light_28"
}
ControlledStreetLight {
  translation 13.5 -875 0
  rotation 0 0 1 3.1415
  name "street_light_29"
}
ControlledStreetLight {
  translation 13.5 -825 0
  rotation 0 0 1 3.1415
  name "street_light_30"
}
ControlledStreetLight {
  translation 13.5 -775 0
  rotation 0 0 1 3.1415
  name "street_light_31"
}
ControlledStreetLight {
  translation 13.5 -725 0
  rotation 0 0 1 3.1415
  name "street_light_32"
}
ControlledStreetLight {
  translation 13.5 -675 0
  rotation 0 0 1 3.1415
  name "street_light_33"
}
ControlledStreetLight {
  translation 13.5 -625 0
  rotation 0 0 1 3.1415
  name "street_light_34"
}
ControlledStreetLight {
  translation 13.5 -575 0
  rotation 0 0 1 3.1415
  name "street_light_35"
}
ControlledStreetLight {
  translation 13.5 -525 0
  rotation 0 0 1 3.1415
  name "street_light_36"
}
ControlledStreetLight {
  translation 13.5 -475 0
  rotation 0 0 1 3.1415
  name "street_light_37"
}
ControlledStreetLight {
  translation 13.5 -425 0
  rotation 0 0 1 3.1415
  name "street_light_38"
}
ControlledStreetLight {
  translation 13.5 -375 0
  rotation 0 0 1 3.1415
  name "street_light_39"
}
ControlledStreetLight {
  translation 13.5 -325 0
  rotation 0 0 1 3.1415
  name "street_light_40"
}
ControlledStreetLight {
  translation 13.5 -275 0
  rotation 0 0 1 3.1415
  name "street_light_41"
}
ControlledStreetLight {
  translation 13.5 -225 0
  rotation 0 0 1 3.1415
  name "street_light_42"
}
SumoInterface {
  networkFiles "/home/<USER>/workspace/webots_simulation/worlds/highway_for_localization_random_net"
}
DEF OBJECT_PROCESSOR Robot {
  controller "object_process"
  supervisor TRUE
}
