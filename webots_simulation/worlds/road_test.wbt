#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
IMPORTABLE EXTERNPROTO "../protos/DBOXUltrasonicSensor.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation 0.5997203439125616 -0.570770438728884 -0.5608534704988783 4.121942966473802
  position 104.85423237701136 -84.59299724549159 110.13887857007316
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
SumoInterface {
  gui FALSE
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden translation_1 0 0 0
  hidden rotation_1 0 0 1 0
  hidden translation_2 0 0 0
  hidden rotation_2 0 0 1 0
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 0.007814281330603016
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 0.007814252434573567
  translation -0.002570532721896557 -4.0547458401505675e-07 0.23710632061823936
  rotation 0.0001322263492669441 -0.9999999900161962 -4.983773771997266e-05 0.0009719493142075552
  controllerArgs [
    "webots_chitu_gt.yaml"
  ]
}
DEF OBJECT_PROCESSOR Robot {
  controller "object_process"
  supervisor TRUE
}
StraightRoadSegment {
  translation 27.0701 -12.0379 0
  name "road f1-b1"
  locked FALSE
}
StraightRoadSegment {
  translation 30 -50 0
  name "road c1 1"
  length 20
  locked FALSE
}
StraightRoadSegment {
  translation 60 -60 0
  rotation 0 0 1 -1.5707953071795862
  name "road c1 1(1)"
  locked FALSE
}
StraightRoadSegment {
  translation 20 -70 0
  rotation 0 0 1 1.5708
  name "road c1 1(3)"
  locked FALSE
}
StraightRoadSegment {
  translation 50 -80 0
  rotation 0 0 1 3.1415
  name "road c1 1(2)"
  length 20
  locked FALSE
}
StraightRoadSegment {
  translation 41.6623 -11.9588 0
  name "road f1-b2"
  numberOfLanes 3
  locked FALSE
}
StraightRoadSegment {
  translation 70.4962 -12.1915 0
  name "road b3"
  numberOfLanes 3
  numberOfForwardLanes 0
  locked FALSE
}
StraightRoadSegment {
  translation 56.1257 -12.2171 0
  name "road f3"
  numberOfLanes 3
  numberOfForwardLanes 3
  locked FALSE
}
CurvedRoadSegment {
  translation 120.375 -30.599 0
  name "road rad2 f2"
  numberOfForwardLanes 2
  totalAngle 2
  locked FALSE
}
CurvedRoadSegment {
  translation 104.159 -30.5953 0
  name "road rad2 f1"
  totalAngle 2
  locked FALSE
}
CurvedRoadSegment {
  translation 50 -60 0
  name "road c1 2"
  locked FALSE
}
CurvedRoadSegment {
  translation 50 -70 0
  rotation 0 0 1 -1.57
  name "road c1 2(1)"
  locked FALSE
}
CurvedRoadSegment {
  translation 30 -70 0
  rotation 0 0 1 3.1415
  name "road c1 2(2)"
  locked FALSE
}
CurvedRoadSegment {
  translation 30 -60 -2.84217e-14
  rotation 0 0 1 1.57
  name "road c1 2(3)"
  locked FALSE
}
CurvedRoadSegment {
  translation 88.2532 -31.4972 0
  name "road pi/2"
  locked FALSE
}
Group {
  children [
    CurvedRoadSegment {
      translation 100 -110 -2.84217e-14
      rotation 0 0 1 1.57
      name "road c2 1"
      numberOfForwardLanes 2
      locked FALSE
    }
    StraightRoadSegment {
      translation 90 -120 0
      rotation 0 0 1 1.5708
      name "road c2 2"
      numberOfForwardLanes 2
      locked FALSE
    }
    CurvedRoadSegment {
      translation 100 -120 0
      rotation 0 0 1 3.1415
      name "road c2 3"
      numberOfForwardLanes 2
      locked FALSE
    }
    CurvedRoadSegment {
      translation 120 -120 0
      rotation 0 0 1 -1.57
      name "road c2 4"
      numberOfForwardLanes 2
      locked FALSE
    }
    StraightRoadSegment {
      translation 120 -130 0
      rotation 0 0 1 3.1415
      name "road c2 5"
      numberOfForwardLanes 2
      length 20
      locked FALSE
    }
    StraightRoadSegment {
      translation 130 -110 0
      rotation 0 0 1 -1.5707953071795862
      name "road c2 6"
      numberOfForwardLanes 2
      locked FALSE
    }
    CurvedRoadSegment {
      translation 120 -110 0
      name "road c2 8"
      numberOfForwardLanes 2
      locked FALSE
    }
    StraightRoadSegment {
      translation 100 -100 0
      name "road c2 7"
      numberOfForwardLanes 2
      length 20
      locked FALSE
    }
  ]
}
Group {
  children [
    Pose {
      children [
        Group {
          children [
            CurvedRoadSegment {
              translation 100 -60 -2.84217e-14
              rotation 0 0 1 1.57
              name "road c1 2(7)"
              numberOfForwardLanes 0
              locked FALSE
            }
            StraightRoadSegment {
              translation 90 -70 0
              rotation 0 0 1 1.5708
              name "road c1 1(7)"
              numberOfForwardLanes 0
              locked FALSE
            }
            CurvedRoadSegment {
              translation 100 -70 0
              rotation 0 0 1 3.1415
              name "road c1 2(6)"
              numberOfForwardLanes 0
              locked FALSE
            }
            CurvedRoadSegment {
              translation 120 -70 0
              rotation 0 0 1 -1.57
              name "road c1 2(5)"
              numberOfForwardLanes 0
              locked FALSE
            }
            StraightRoadSegment {
              translation 120 -80 0
              rotation 0 0 1 3.1415
              name "road c1 1(6)"
              numberOfForwardLanes 0
              length 20
              locked FALSE
            }
            StraightRoadSegment {
              translation 130 -60 0
              rotation 0 0 1 -1.5707953071795862
              name "road c1 1(5)"
              numberOfForwardLanes 0
              locked FALSE
            }
            CurvedRoadSegment {
              translation 120 -60 0
              name "road c1 2(4)"
              numberOfForwardLanes 0
              locked FALSE
            }
            StraightRoadSegment {
              translation 100 -50 0
              name "road c1 1(4)"
              numberOfForwardLanes 0
              length 20
              locked FALSE
            }
          ]
        }
      ]
    }
  ]
}
