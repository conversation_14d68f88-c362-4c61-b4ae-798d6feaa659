#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
IMPORTABLE EXTERNPROTO "../protos/DBOXUltrasonicSensor.proto"
IMPORTABLE EXTERNPROTO "../protos/DynamicTrailerModule.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.6810761223826647 -0.2680564683951667 0.6813817177410212 3.6641298364587147
  position 125.27334838615701 100.82854074050802 18.578462558701773
  near 1
  follow "chitu"
  lensFlare LensFlare {
  }
}
SumoInterface {
  gui FALSE
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden position_0_0 -0.04581122567538788
  hidden position2_0_0 773.042850558492
  hidden position_0_1 -0.044511618587889594
  hidden position2_0_1 789.3589109759886
  hidden position_0_2 769.2678951321459
  hidden position_0_3 786.0242360840274
  hidden rotation_1 -0.022386248400575796 0.977154958170576 -0.21134579154837108 6.067231014805658
  hidden rotation_2 0.02225293356059466 -0.9997056961602135 0.009660642461589219 2.322652747947506
  hidden rotation_3 0 -1 0 2.7192876562363453
  hidden rotation_4 0 -1 0 5.657112620600503
  translation 126.19063143256959 100.16951358371695 0.23710659617817803
  rotation 0.00048802258137758926 -0.00020671293901542535 0.9999998595518506 2.319740288208282
  controllerArgs [
    "webots_chitu_gt.yaml"
  ]
}
DEF OBJECT_PROCESSOR Robot {
  controller "object_process"
  supervisor TRUE
}
