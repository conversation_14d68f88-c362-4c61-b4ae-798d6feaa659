#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "../protos/t8000.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.5654183646051698 0.7283142468901462 0.3871181095532143 2.2167400003833526
  position 33.440156185106304 70.7120550900357 345.1829896030384
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
SumoInterface {
  gui FALSE
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}

DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
DEF WEBOTS_VEHICLE0 t8000 {
  hidden position_0_0 0.10968478730010416
  hidden position2_0_0 1044.3926430063557
  hidden position_0_1 0.11787714369264157
  hidden position2_0_1 1053.9448268840563
  hidden position_0_2 1045.4346899310526
  hidden position_0_3 1050.6545033533635
  hidden rotation_1 -0.05469683442468583 -0.9963457602827572 -0.06560016974438972 1.3936191371768414
  hidden rotation_2 -0.058815066953497454 -0.9967487555673794 -0.055070011568331016 1.6397908143639133
  hidden rotation_3 0 -1 0 1.5377839609160049
  hidden rotation_4 0 -1 0 5.81811363880573
  translation 0 0 0.257108
  rotation -0.0005984136460566911 -0.0044610643848100595 -0.9999898703515265 0.21614105680976511
  controllerArgs [
    "webots_chitu_gt.yaml"
  ]
}
DEF OBJECT_PROCESSOR Robot {
  controller "object_process"
  supervisor TRUE
}
