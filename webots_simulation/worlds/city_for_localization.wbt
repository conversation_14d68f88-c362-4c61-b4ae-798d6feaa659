#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "../protos/RoadIntersection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CrossRoadsTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BuildingUnderConstruction.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Auditorium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/FastFoodRestaurant.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/SimpleBuilding.proto"
EXTERNPROTO "../protos/PedestrianCrossing.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/YieldSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwayPole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwaySign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Crossroad.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/range_rover/RangeRoverSportSVRSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTrailerSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterDriver.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeDriver.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BungalowStyleHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuildingWithRoundFront.proto"
EXTERNPROTO "../protos/ForestDense.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Building.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/Truck.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.6635814635865317 -0.006864486699938621 0.7480725365943484 3.140859925967284
  position 87.87458005559654 -42.42661060150227 244.09259027610491
  near 1
  follow "vehicle"
  followType "None"
  lensFlare LensFlare {
  }
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND Solid {
  boundingObject DEF GROUND_PLANE Plane {
    size 2000 2000
  }
  locked TRUE
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
AdvertisingBoard {
  translation -61.9995 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(16)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
CurvedRoadSegment {
  translation -64.5 4.5 0.02
  rotation 0 0 1 1.5708
  id "0"
  startJunction "40"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
Building {
  translation 188.83 0 0
  name "building(2)"
}
Building {
  translation 188.83 19.44 0
  name "building(2)"
}
Building {
  translation 188.83 39.21 0
  name "building(2)"
}
Building {
  translation 188.83 58.99 0
  name "building(2)"
}
Building {
  translation 188.83 78.76 0
  name "building(2)"
}
Building {
  translation 188.76 -19.93 0
  name "building(2)"
}
Building {
  translation 188.69 -39.28 0
  name "building(2)"
}
Building {
  translation 188.55 -56.4 0
  name "building(2)"
}
Building {
  translation 188.45 -75.98 0
  name "building(2)"
}
Building {
  translation 188.45 -94.62 0
  name "building(2)"
}
Building {
  translation 188.45 -114.15 0
  name "building(2)"
}
StraightRoadSegment {
  translation -105 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(1)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 99
}
StraightRoadSegment {
  translation -105 -115.5 0.02
  rotation 0 0 1 -1.5708
  name "road(2)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 31.5
}
CurvedRoadSegment {
  translation -64.5 -147 0.02
  rotation 0 0 1 3.14156
  name "road(2)"
  id "2"
  startJunction "38"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -94.5 -105 0.02
  name "road(3)"
  id "3"
  startJunction "38"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 99
}
CurvedRoadSegment {
  translation 4.5 -64.5 0.02
  rotation 0 0 1 -1.5708
  name "road(4)"
  id "4"
  startJunction "25"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
RoadIntersection {
  translation 45 -45 0.02
  rotation 0 0 1 0.785398
  id "25"
  connectedRoadIDs [
    "9"
    "10"
    "4"
    "5"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
StraightRoadSegment {
  translation 45 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "43"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 4.5 4.5 0.02
  name "road(6)"
  id "6"
  startJunction "44"
  endJunction "43"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -25.5 45 0.02
  name "road(7)"
  id "7"
  startJunction "26"
  endJunction "44"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
RoadIntersection {
  translation -45 45 0.02
  rotation 0 0 1 0.785398
  name "road intersection(1)"
  id "26"
  connectedRoadIDs [
    "7"
    "23"
    "0"
    "24"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation -105 -105 0.02
  rotation 0 0 1 0.785398
  name "road intersection(3)"
  id "26"
  connectedRoadIDs [
    "7"
    "23"
    "0"
    "24"
  ]
  roadsWidth 21.5
  startRoads FALSE
  startRoadsLength [
    2.5
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation 105 93 0.02
  rotation 0 0 1 0.785398
  name "road intersection(2)"
  id "27"
  connectedRoadIDs [
    "12"
    "20"
    "17"
    "16"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
CurvedRoadSegment {
  translation -4.5 -4.5 0.02
  rotation 0 0 1 3.14156
  name "road(8)"
  id "8"
  startJunction "42"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -4.5 -45 0.02
  name "road(9)"
  id "9"
  startJunction "42"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 64.5 -4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(10)"
  id "10"
  startJunction "45"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 75 -97.5 0.02
  rotation 0 0 1 -1.5708
  name "road(11)"
  id "11"
  startJunction "35"
  endJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 90
}
StraightRoadSegment {
  translation 85.5 93 0.02
  rotation 0 0 1 3.14156
  name "road(12)"
  id "12"
  startJunction "27"
  endJunction "28"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 9
}
StraightRoadSegment {
  translation 75 -187.5 0.02
  rotation 0 0 -1 3.14156
  name "road(13)"
  id "13"
  startJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 139.5
}
StraightRoadSegment {
  translation 165 52.5 0.02
  rotation 0 0 1 -1.5708
  name "road(14)"
  id "14"
  startJunction "34"
  endJunction "35"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 150
}
StraightRoadSegment {
  translation 105 -4.5 0.02
  rotation 0 0 1 1.57079
  name "road(17)"
  id "17"
  startJunction "45"
  endJunction "27"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
CurvedRoadSegment {
  translation 76.5 133.5 0.02
  rotation 0 0 -1 3.14156
  name "road(18)"
  id "18"
  startJunction "28"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 124.5 52.5 0.02
  name "road(20)"
  id "20"
  startJunction "27"
  endJunction "34"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  name "road(21)"
  id "21"
  startJunction "30"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  rotation 0 0 1 1.5708
  name "road(22)"
  id "22"
  startJunction "31"
  endJunction "30"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -45 25.5 0.02
  rotation 0 0 -1 1.5708
  name "road(23)"
  id "23"
  startJunction "26"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
StraightRoadSegment {
  translation -45 64.5 0.02
  rotation 0 0 1 1.5708
  name "road(24)"
  id "24"
  startJunction "26"
  endJunction "31"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden translation_1 0 0 0
  hidden rotation_1 3.9974391519016104e-11 -0.9999999999999998 1.303219797068466e-08 0.006134692895401442
  hidden translation_2 0 0 0
  hidden rotation_2 -2.2124451180423748e-11 -1 -7.094650176629626e-09 0.006236919061843249
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 0.8881768466637797
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 0.8975172434367198
  translation -85.47281178642945 45.6309487164251 0.2571060247277312
  rotation 0.00048610102708244383 8.477891906799809e-05 0.9999998782591558 -2.7964455794462886
}
CrossRoadsTrafficLight {
  translation 45 -45 0
}
CrossRoadsTrafficLight {
  translation 46 -45 1
  name "cross road traffic light(1)"
}
BuildingUnderConstruction {
  translation 104.031 121 0
}
CommercialBuilding {
  translation 70.9574 31.6315 0
}
UBuilding {
  translation -87.1466 81.9927 0
}
UBuilding {
  translation -33.0931 -148.903 0
  rotation 0 0 1 1.5708
  name "U building(1)"
}
HollowBuilding {
}
Hotel {
  translation -9.97953 71.6228 0
}
LargeResidentialTower {
  translation -17.51 105.22 0
  numberOfFloors 12
}
BungalowStyleHouse {
  translation 19.65 99.5 0
}
TheThreeTowers {
  translation 68.118 -90.636 0
}
TheThreeTowers {
  translation -130.504 -95.176 0
  rotation 0 0 -1 4.692820414042842e-06
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation 68.6779 -9.29537 0
  rotation 0 0 1 1.5708
}
Auditorium {
  translation -63.9296 -61.9719 0
  rotation 0 0 1 0.654496
}
Auditorium {
  translation 148.81 132.46 0
  rotation 0 0 1 -3.0106953071795863
  name "auditorium(1)"
}
Museum {
  translation -0.191182 -68.6571 0
  rotation 0 0 1 1.5708
}
Museum {
  translation 110.334 -65.2535 0
  rotation 0 0 1 2.0944
  name "museum(1)"
}
ComposedHouse {
  translation -26.35 0 0
}
ResidentialBuildingWithRoundFront {
  translation 31.1742 -127.739 0
  rotation 0 0 1 1.8326
  name "residential building(1)"
}
ResidentialBuilding {
  translation -69.274 -1.81329 0
}
FastFoodRestaurant {
  translation 33.27 203.63 0
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
FastFoodRestaurant {
  translation 48.60864 48.933131 0
  rotation 0 0 -1 -2.8797953071795863
  name "fast food restaurant(1)"
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
SimpleBuilding {
  translation 46.9364 186.088 0
  wallType "glass highrise"
}
SimpleBuilding {
  translation 66.8317 174.83 0
  name "building(1)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 117.415 -135.778 0
  name "building(3)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 126.106 -115.963 0
  name "building(4)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -134.064 -16.533 0
  name "building(14)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 71.4078 153.379 0
  name "building(6)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation -56.5761 182.166 0
  rotation 0 0 -1 2.618
  name "building(9)"
  floorHeight 6
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  groundFloorScale [
    0
  ]
  roofType "old tiles"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -77.6413 142.999 0
  rotation 0 0 1 3.14159
  name "building(2)"
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -72.4649 -133.213 0
  rotation 0 0 1 5.23599
  name "building(10)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -131.19 52.9749 0
  rotation 0 0 1 -2.8797853071795863
  name "building(15)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -3.91795 144.18 0
  name "building(13)"
  wallType "residential building"
}
PedestrianCrossing {
  translation 45 -26 -0.06
  rotation 0 0 1 -3.1415853071795863
}
PedestrianCrossing {
  translation -61.4608 45.0693 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(1)"
}
PedestrianCrossing {
  translation 26.9799 -45.1201 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(2)"
}
CautionSign {
  translation -91.9275 48.9391 0
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_left.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 45.9944 -94.6291 0
  rotation 0 0 1 -2.4871
  name "caution sign(1)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/pedestrian_crossing.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(2)"
}
CautionSign {
  translation 33.842 10.5534 0
  rotation 0 0 1 1.7017
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/bump.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 84.1154 -26.9475 0
  rotation 0 0 1 0.6545
  name "caution sign(4)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
CautionSign {
  translation -5.43669 -34.1146 0
  rotation 0 0 1 -0.5236
  name "caution sign(5)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_right.jpg"
      ]
    }
  ]
}
OrderSign {
  translation -67.6589 34.4983 0
  rotation 0 0 1 3.14159
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_right_turn.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
}
YieldSign {
  translation -55.468 66.4958 0
  rotation 0 0 1 1.5708
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 26.6277 -84.4244 0
  rotation 0 0 1 0.6545
  name "speed limit(1)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.3528 79.1341 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 87.1538 -50.335 0
  rotation 0 0 1 -3.14159
  name "speed limit(3)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 31.0289 -34.4459 0
  name "speed limit(4)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/one_way_sign_left.jpg"
      ]
    }
  ]
}
TrafficCone {
  translation 41.18160852300362 -54.869090378433015 0.23797576345451704
  rotation 0.7068402576253341 0.5358553741171531 0.4617746942285998 -2.4681182771195256
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.72154992824049 -50.63667696476707 0.23797576345451701
  rotation -0.44236039601263766 0.8490005570818582 -0.28899019726322384 -2.128784757733704
  name "traffic cone(1)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 28.07349572672252 -45.48178286783129 0.035923281327388784
  rotation 0.006842457951432572 0.009534634193585335 -0.9999311333886837 1.245542342423228
  name "traffic cone(2)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.68860250690697 -45.70913519072896 -0.004076718672611196
  rotation 0.0067629857729184055 0.12045947230285256 -0.992695208790671 0.11353613323373805
  name "traffic cone(3)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 48.100535 -116.28367 0
  rotation 0 0 1 2.61799
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 21.3005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(1)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 13.1005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(5)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 4.7805 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(6)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -3.5095 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(7)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -11.8395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(8)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -20.3495 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(9)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -28.6395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(10)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -36.8795 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(11)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -45.2395 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(12)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -53.4595 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(13)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -61.9995 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(14)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation -69.9667 -172.691 -3.86
  rotation 0 0 1 1.30899
  name "advertising board(15)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 29.5305 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(2)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 37.9205 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(3)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
AdvertisingBoard {
  translation 46.3005 -173.671 -3.86
  rotation 0 0 -1 -1.5707953071795862
  name "advertising board(4)"
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
HighwayPole {
  translation -17.67 -117.85 0
  rotation 0 0 -1 3.14159
  height 9
  length 12
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -4.56 0
      height 4
      length 5.5
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_bayonne.jpg"
      ]
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      name "highway sign(1)"
      height 2.5
      length 3
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_sebastian.jpg"
      ]
    }
  ]
}
HighwayPole {
  translation 118.15 72.97 0
  rotation 0 0 1 -1.5707953071795862
  name "highway pole(1)"
  stand 2
  height 8
  length 25
  thickness 0.3
  curveRadius 0.5
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -3.95 0
      height 4
      length 5.5
      color 0.6 0.6 0.6
    }
  ]
  rightVerticalSigns []
  leftVerticalSigns [
    HighwaySign {
      translation 0 0 0.58
      name "highway sign(1)"
      height 2.5
      length 3
      color 0.6 0.6 0.6
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
Crossroad {
  translation 76.500107 93 0
  id "28"
  shape []
  connectedRoadIDs [
    "18"
    "12"
  ]
}
Crossroad {
  translation 36 133.50015 0
  name "crossroad(1)"
  id "29"
  shape []
  connectedRoadIDs [
    "21"
    "18"
  ]
}
Crossroad {
  translation -4.500405 174 0
  name "crossroad(2)"
  id "30"
  shape []
  connectedRoadIDs [
    "22"
    "21"
  ]
}
Crossroad {
  translation -45.000035 133.49978 0
  name "crossroad(3)"
  id "31"
  shape []
  connectedRoadIDs [
    "24"
    "22"
  ]
}
Crossroad {
  translation 64.499851 231 0
  name "crossroad(4)"
  id "32"
  shape []
  connectedRoadIDs [
    "15"
    "19"
  ]
}
Crossroad {
  translation 104.99975 190.50007 0
  name "crossroad(5)"
  id "33"
  shape []
  connectedRoadIDs [
    "16"
    "19"
  ]
}
Crossroad {
  translation 165 52.500074 0
  name "crossroad(6)"
  id "34"
  shape []
  connectedRoadIDs [
    "20"
    "14"
  ]
}
Crossroad {
  translation 165.00028 -97.499835 0
  name "crossroad(7)"
  id "35"
  shape []
  connectedRoadIDs [
    "11"
    "14"
  ]
}
Crossroad {
  translation 75 -187.5 0
  name "crossroad(8)"
  id "36"
  shape []
  connectedRoadIDs [
    "13"
    "11"
  ]
}
Crossroad {
  translation 4.5 -104.99975 0
  name "crossroad(9)"
  id "37"
  shape []
  connectedRoadIDs [
    "3"
    "4"
  ]
}
Crossroad {
  translation -64.5 -105 0
  name "crossroad(10)"
  id "38"
  shape []
  connectedRoadIDs [
    "2"
    "3"
  ]
}
Crossroad {
  translation -104.99987 -64.499926 0
  name "crossroad(11)"
  id "39"
  shape []
  connectedRoadIDs [
    "1"
    "2"
  ]
}
Crossroad {
  translation -105 4.4999794 0
  name "crossroad(12)"
  id "40"
  shape []
  connectedRoadIDs [
    "0"
    "1"
  ]
}
Crossroad {
  translation -45.000015 -4.4999256 0
  name "crossroad(13)"
  id "41"
  shape []
  connectedRoadIDs [
    "23"
    "8"
  ]
}
Crossroad {
  translation -4.5 -45 0
  name "crossroad(14)"
  id "42"
  shape []
  connectedRoadIDs [
    "9"
    "8"
  ]
}
Crossroad {
  translation 45 4.5000744 0
  name "crossroad(15)"
  id "43"
  shape []
  connectedRoadIDs [
    "5"
    "6"
  ]
}
Crossroad {
  translation 4.4998512 45.00011 0
  name "crossroad(16)"
  id "44"
  shape []
  connectedRoadIDs [
    "7"
    "6"
  ]
}
Crossroad {
  translation 105 -4.4999256 0
  name "crossroad(17)"
  id "45"
  shape []
  connectedRoadIDs [
    "10"
    "17"
  ]
}
ForestDense {
  translation 66.78 70.8 0
  density 0.03
  type "random"
}
ForestDense {
  translation -134.19 -49.5328 0
  rotation 0 0 1 0.523599
  shape [
    -6 -10
    20 -10
    0 25
  ]
  density 0.03
  type "random"
}
ForestDense {
  translation -109.519 -174.579 0
  rotation 0 0 -1 2.61801
  shape [
    1 -30
    10 -20
    0 70
  ]
  density 0.08
  type "random"
  maxHeight 10
  minHeight 4
}
ForestDense {
  translation -134.49 18.5672 0
  rotation 0 0 1 -5.307179586466759e-06
  shape [
    1 -20
    20 -10
    0 25
  ]
  density 0.06
  type "random"
  randomSeed 2
}
ForestDense {
  translation -118.275 58.7929 0.02
  rotation 0 0 -1 0.261806
  shape [
    1 -20
    20 -10
    0 25
  ]
  density 0.06
  type "random"
}
ForestDense {
  translation 150.267 -71.1951 -0.44
  rotation 0 0 1 3.14159
  shape [
    1 -50
    1 50
    10 50
    10 -50
  ]
  density 0.07
  type "random"
  randomSeed 1
}
ForestDense {
  translation 150.267 23.8549 -0.44
  rotation 0 0 1 3.14159
  shape [
    1 -50
    1 50
    10 50
    10 -50
  ]
  density 0.07
  type "random"
  randomSeed 1
}
ForestDense {
  translation -21.2484 186.462 -0.44
  rotation 0 0 -1 -1.5707853071795865
  shape [
    1 -20
    1 50
    6 50
    6 -25
  ]
  density 0.08
  type "random"
  randomSeed 2
}
ForestDense {
  translation 8.39735 -208.368 -0.44
  rotation 0 0 -1 -1.5707853071795865
  shape [
    1 -100
    1 100
    6 100
    6 -100
  ]
  density 0.08
  type "random"
  randomSeed 3
}
ForestDense {
  translation 158.186 -174.832 -0.44
  rotation 0 0 1 2.35618
  shape [
    1 -50
    1 40
    10 60
    10 -50
  ]
  density 0.06
  type "random"
  randomSeed 2
}
OrderSign {
  translation 77.2 107.29 0
  rotation 0 0 1 1.5708
  name "order sign(1)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 2.3718239617106433e-07 -0.12504018312341805 0.9921516782248598 3.8236507380628026e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -1.2216196746915002e-07 0.06376652471093107 0.997964844233641 3.839297989652249e-06
  hidden translation_3 0 0 0
  hidden rotation_3 0 -1 0 4.088395292838304e-05
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 0
  hidden translation_7 0 0 -0.08000000000000008
  hidden rotation_7 0 0 1 0.17868825288507462
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 4.742254288128462
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 1.079233850132217
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 0.059237709504182584
  hidden translation_12 0 0 0
  hidden rotation_12 0 -1 0 6.265103161574909
  hidden translation_13 0 0 0
  hidden rotation_13 0 1 0 0.07368230222721517
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 2.453898979952661
  translation 78.85842085607109 111.0808123511053 0.5010019251395625
  rotation -0.0007104107197611193 0.02865841959577398 -0.9995890106953367 0.30946349955640706
  name "vehicle(1)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 1.815120694560102e-07 -0.1453679715521189 0.9893776593630914 2.5238889884640614e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -2.3136769967901126e-07 0.1843469199375032 0.9828612379728392 2.5539738280332096e-06
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 1.8342330746291376e-05
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 0
  hidden translation_7 0 0 -0.08000000000000004
  hidden rotation_7 0 0 1 0.0877122940225961
  hidden translation_9 0 0 0
  hidden rotation_9 0 0.9999999999999999 0 3.639623811681845
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 4.5042203669697525
  hidden translation_11 0 0 0
  hidden rotation_11 0 1 0 4.286259459725609
  hidden translation_12 0 0 0
  hidden rotation_12 0 -1 0 2.8409215209537746
  hidden translation_13 0 0 0
  hidden rotation_13 0 0.9999999999999999 0 6.257066888776492
  hidden translation_14 0 0 0
  hidden rotation_14 0 -1 0 0.21782486804179885
  translation 170.3456130543657 98.94942832599762 0.5006187356268923
  rotation 0.0037231198395716574 0.00770886511231025 -0.9999633552172501 1.0630834153354607
  name "vehicle(2)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 2.4668092875952244e-07 -0.6288431866405045 0.7775321514997013 1.0088867473489605e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -2.0935015386742988e-07 0.5217393278214733 0.8531049606023597 9.405454300677565e-07
  hidden translation_3 0 0 0
  hidden rotation_3 0 -1 0 9.424321830774485e-08
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 1.3505023588623395e-05
  hidden translation_7 0 0 -0.08000000000000007
  hidden rotation_7 0 0 -1 0.06881542045160999
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 5.62200199056689
  hidden translation_10 0 0 0
  hidden rotation_10 0 0.9999999999999999 0 5.806219946095351
  hidden translation_11 0 0 0
  hidden translation_12 0 0 0
  hidden rotation_12 0 1 0 0.38796090761246
  hidden translation_13 0 0 0
  hidden rotation_13 0 1 0 3.6582771439431268
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 5.652529335062648
  translation 93.3126541525015 -166.97078444006024 0.5005186295814966
  rotation -0.002600981724652704 0.02873460949957321 0.9995836918992702 0.30095227644274336
  name "vehicle(3)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 1.879057959033806e-07 -0.07592072463779782 0.9971138568741467 4.9643906765938285e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -1.2661959527276652e-07 0.05080170361186051 0.9987087598044371 4.991243982634163e-06
  hidden translation_3 0 0 0
  hidden rotation_3 0 -1 0 3.0911718139817554e-05
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 0
  hidden translation_7 0 0 -0.08000000000000007
  hidden rotation_7 0 0 1 0.13797967734241978
  hidden translation_9 0 0 0
  hidden rotation_9 0 1 0 0.9443960517613847
  hidden translation_10 0 0 0
  hidden rotation_10 0 1 0 4.4085117979468125
  hidden translation_11 0 0 0
  hidden rotation_11 0 -0.9999999999999999 0 3.1268396924018393
  hidden translation_12 0 0 0
  hidden rotation_12 0 -1 0 2.347219998585886
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 1.968353116047772
  hidden translation_14 0 0 0
  hidden rotation_14 0 -1 0 5.94674349765864
  translation 115.83743255031125 -193.54176003101819 0.5008502171300222
  rotation -0.006521980921872616 0.015258841272730124 0.9998623062841545 0.5342675228197931
  name "vehicle(4)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 1.914098226894526e-07 -0.06561448976031656 0.9978450474564959 5.847030334376722e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -1.370795113917313e-07 0.04668025730266551 0.9989098826111084 5.879523133143832e-06
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 2.712468158603982e-05
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 0
  hidden translation_7 0 0 -0.08000000000000006
  hidden rotation_7 0 0 1 0.12270387100636826
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 0.5234559331542196
  hidden translation_10 0 0 0
  hidden rotation_10 0 -1 0 2.026645721845542
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 2.4705057994528317
  hidden translation_12 0 0 0
  hidden rotation_12 0 -0.9999999999999999 0 3.7003638992335275
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 5.565206731054286
  hidden translation_14 0 0 0
  hidden rotation_14 0 -0.9999999999999999 0 0.5841725147716185
  translation 66.94341691035122 -167.28698537710125 0.5007851515752691
  rotation -0.005656109848294321 0.010398536567620792 0.9999299369749042 0.7557181875362992
  name "vehicle(7)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 2.5726633030574295e-07 -0.14936203612610907 0.9887825757790216 3.4839411997944198e-06
  hidden translation_2 0 0 0
  hidden rotation_2 -5.968395038752281e-08 0.03306870089665511 0.9994530809502786 3.6116185113768015e-06
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 0.0001012717207104469
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 5.1749242089917444e-05
  hidden translation_7 0 0 -0.08000000000000007
  hidden rotation_7 0 0 1 0.24798410380041996
  hidden translation_9 0 0 0
  hidden rotation_9 0 -1 0 2.496305293599675
  hidden translation_10 0 0 0
  hidden rotation_10 0 -0.9999999999999999 0 2.756016847108159
  hidden translation_11 0 0 0
  hidden rotation_11 0 -1 0 4.685677045469913
  hidden translation_12 0 0 0
  hidden rotation_12 0 -1 0 1.5438470506800361
  hidden translation_13 0 0 0
  hidden rotation_13 0 -1 0 3.8701015685331672
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 1.8800982625043774
  translation 82.20451405597888 -164.0989771637513 0.501191837673562
  rotation -0.008667646166270838 0.05255496660867106 -0.9985804160881073 0.16876383919834562
  name "vehicle(5)"
}
Truck {
  hidden translation_1 0 0 0
  hidden rotation_1 -3.3056779261168754e-08 -0.9926389289076968 -0.12111134057956498 5.503352079576088e-07
  hidden translation_2 0 0 0
  hidden rotation_2 -1.7360258879180138e-08 0.9969987374502126 0.07741781140462155 4.490172616636573e-07
  hidden translation_3 0 0 0
  hidden rotation_3 0 1 0 1.6942823414268875e-06
  hidden translation_4 0 0 0
  hidden rotation_4 0 1 0 5.647404054031326e-05
  hidden translation_7 0 0 -0.08000000000000006
  hidden rotation_7 0 0 1 0.04271159521560932
  hidden translation_9 0 0 0
  hidden rotation_9 0 -0.9999999999999999 0 1.5227380330030253
  hidden translation_10 0 0 0
  hidden rotation_10 0 -1 0 3.8596289853622308
  hidden translation_11 0 0 0
  hidden rotation_11 0 1 0 4.136403721278472
  hidden translation_12 0 0 0
  hidden rotation_12 0 -1 0 1.2733841684816873
  hidden translation_13 0 0 0
  hidden rotation_13 0 0.9999999999999999 0 1.5082518485490848
  hidden translation_14 0 0 0
  hidden rotation_14 0 1 0 0.7453195943738036
  translation 99.99870318444502 -161.30861376989034 0.5004999766485605
  rotation -0.005290742554027503 0.02819141645421517 0.9995885413916729 0.3023500860926853
  name "vehicle(6)"
}
