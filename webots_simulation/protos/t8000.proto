#VRML_SIM R2023a utf8
# Describe the functionality of your PROTO here.

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/Car.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/VehicleLights.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/VehicleWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Plastic.proto"
EXTERNPROTO "VelodyneVLP-16.proto"
EXTERNPROTO "UltrasonicSensor-12.proto"


PROTO t8000 [
  field SFVec3f translation 0 0 0.4
  field SFRotation rotation 0 0 1 0
  field SFString name "t8000"
  field SFString controller "webots_chitu"
  field MFString controllerArgs ["webots_chitu.yaml"]
  field MFNode extensionSlot [
    Group {
      children [
        Accelerometer {
            translation 0.866 0 0.663
            rotation 0 0 1  0.0
        }
        Gyro {
            translation 0.866 0 0.663
            rotation 0 0 1  0.0
        }
        InertialUnit {
            translation 0.866 0 0.663
            rotation 0 0 1  0.0
        }
      ]
    }
    #UltrasonicSensor-12{
    #  name "UltrasonicSensor-12"
    #}

    DEF laser_top VelodyneVLP-16 {
      translation 0.194 0 1.702
      rotation 0 0 1  -1.570796
      name "laser_top"
    }
    VelodyneVLP-16 {
      translation 1.719 0.671 0.181
      rotation 0 0 1 -0.28
      name "laser_left"
    }
    VelodyneVLP-16 {
      translation 1.719 -0.671  0.181
      rotation  0 0 1 -2.88
      name "laser_right"
    }
    Lidar {
      numberOfLayers 16
      maxRange 100
      type "rotating"
    }
    Camera {
      translation 1.910 0.050 0.275
      rotation 0.0 0.0 1.0 0.0
      width 1280
      height 720
      name "camera_0"
      fieldOfView 1.74
      recognition Recognition{
        occlusion 0
        segmentation TRUE
      }
    }
    Camera {
      translation 1.910 -0.050 0.275
      rotation 0.0 0.0 1.0 0.0
      width 1280
      height 720
      name "camera_1"
      fieldOfView 1.74
      recognition Recognition{
        occlusion 0
        segmentation TRUE
      }
    }
    Transform {
      translation 0 0 0.0
      children [
        Shape {
          appearance Plastic {
          }
          geometry Mesh {
            url [
              "../protos/T8000.dae"
            ]
          }
          castShadows FALSE
        }
      ]
    }
  ]
]
{
  Car {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    extensionSlot IS extensionSlot

    supervisor TRUE
    trackFront 0.001
    trackRear 1.075
    wheelbase 1.57197
    minSteeringAngle -1.57
    maxSteeringAngle 1.57
    maxVelocity 10
    suspensionFrontSpringConstant 100000
    suspensionFrontDampingConstant 4000
    suspensionRearSpringConstant 100000
    suspensionRearDampingConstant 4000
    physics Physics {
      density -1
      mass 1000
      centerOfMass [
        0.8 0 0.4
      ]
    }
    boundingObject Group {
      children [
        Transform {
          translation 0.785 0.0 0.755
          rotation 1 0 0 0
          children [
            Box {
              size 2.513 1.448 1.75
            }
          ]
        }
        #Transform {
        #  translation 1.05 0 0.2
        #  rotation 1 0 0 0
        #  children [
        #    Box {
        #      size 3.3 1.4 0.5
        #    }
        #  ]
        #}
      ]
    }

    wheelFrontRight VehicleWheel { name "front right wheel" thickness 0.104 tireRadius 0.241 rimRadius 0.15}
    wheelFrontLeft VehicleWheel { name "front left wheel" wheelSide TRUE thickness 0.104 tireRadius 0.241 rimRadius 0.15}
    wheelRearRight VehicleWheel { name "rear right wheel" thickness 0.151 tireRadius 0.241 rimRadius 0.15}
    wheelRearLeft VehicleWheel { name "rear left wheel" wheelSide TRUE thickness 0.151 tireRadius 0.241 rimRadius 0.15}

  }

}
