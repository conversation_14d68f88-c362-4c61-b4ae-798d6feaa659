#VRML_SIM R2023b utf8
# license: Apache License 2.0.
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/pal_robotics/tiago_base/protos/TiagoBase.proto
# This is a PROTO file for Webots for the base diff chassis robot WheelDistance = 0.4 WheelRadius = 0.0985
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Asphalt.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/BrushedAluminium.proto"

PROTO TralierBase [
  field  SFVec3f     translation      0 0 0.097
  field  SFRotation  rotation         0 0 1 0
  field  SFString    name             "TralierBase"
  field  SFFloat     tralierWidth  1.2
  field  SFFloat     tralierLength 1.5
]
{
  Solid {
    translation IS translation
    rotation IS rotation
    name IS name
    children [
      DEF BODY Solid {
        translation 0 0 0.17
        children Shape {
          appearance PBRAppearance {
            baseColor 0.752941 0.752941 0.752941
            roughness 1.000000
            metalness 0
          }
          geometry DEF BODY_BOX Box {
            size %<= fields.tralierLength.value >% %<= fields.tralierWidth.value >% 0.1
          }

        }
        boundingObject USE BODY_BOX
        physics Physics {
          density -1
          mass 10
        }
      }
      DEF RIGHT_WHEEL_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 1 0
          anchor %<= -(fields.tralierLength.value/2.0) >% %<= -fields.tralierWidth.value/2.0 >% %<= 0 >%
          suspensionSpringConstant 0
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF RIGHT_WHEEL Solid {
          translation %<= -(fields.tralierLength.value/2.0) >% %<= -(fields.tralierWidth.value/2.0) >% %<= 0 >%
          rotation 1 0 0 1.5708
          children [
            DEF BIG_WHEEL Group {
              children [
                Pose {
                  translation 0 0.00 0.03
                  children [
                    Shape {
                      appearance PBRAppearance {
                        baseColor 1 1 1
                        roughness 1
                        metalness 0
                      }
                      geometry Box {
                        size 0.05 0.01 0.01
                      }
                    }
                  ]
                }
                DEF TIRE Shape {
                  appearance Asphalt {
                    textureTransform TextureTransform {
                      scale 25 25
                    }
                    IBLStrength 0.5
                  }
                  geometry  DEF WHEEL_BOUNDING_OBJECT Cylinder {
                    height 0.04
                    radius 0.1
                  }
                }
              ]
            }
          ]
          name "wheel_right_link"
          boundingObject Pose {
            children [
              USE WHEEL_BOUNDING_OBJECT
            ]
          }
          physics Physics {
            density -1
            mass 1.82362
          }
        }
      }
      DEF LEFT_WHEEL_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 1 0
          anchor %<= -fields.tralierLength.value/2.0 >% %<= fields.tralierWidth.value/2.0 >% %<= 0 >%
          suspensionSpringConstant 0
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF LEFT_WHEEL Solid {
          translation %<= -fields.tralierLength.value/2.0 >% %<= fields.tralierWidth.value/2.0 >% %<= 0 >%
          rotation 1 0 0 1.5708
          children [
            Pose {
              rotation -1 0 0 3.141593
              children [
                USE BIG_WHEEL
              ]
            }
          ]
          name "wheel_left_link"
          boundingObject Pose {
            children [
              USE WHEEL_BOUNDING_OBJECT
            ]
          }
          physics Physics {
            density -1
            mass 1.82362
          }
        }
      }
      DEF CASTER_BIG_WHEEL_LEFT Solid {
        translation %<= -fields.tralierLength.value/2.0 >% %<= fields.tralierWidth.value/2.0 >% %<= 0 >%
        children [
          Pose {
            translation 0 0.00 0.06
            children [
              Shape {
                appearance PBRAppearance {
                  baseColor 1 1 1
                  roughness 1
                  metalness 0
                }
                geometry DEF CASTER_BIG_WHEEL_LEFT_BOX Box {
                  size 0.05 0.05 0.12
                }
              }
            ]
          }
        ]
        boundingObject Pose {
          translation 0 0 0.06
          children [
            USE CASTER_BIG_WHEEL_LEFT_BOX
          ]
        }
        physics Physics {
          density -1
          mass 0.1
        }
      }
      DEF CASTER_BIG_WHEEL_RIGHT Solid {
        translation %<= -fields.tralierLength.value/2.0 >% %<= -fields.tralierWidth.value/2.0 >% %<= 0 >%
        children [
          Pose {
            translation 0 0.00 0.06
            children [
              Shape {
                appearance PBRAppearance {
                  baseColor 1 1 1
                  roughness 1
                  metalness 0
                }
                geometry DEF CASTER_BIG_WHEEL_RIGHT_BOX Box {
                  size 0.05 0.05 0.12
                }
              }
            ]
          }
        ]
        boundingObject Pose {
          translation 0 0 0.06
          children [
            USE CASTER_BIG_WHEEL_RIGHT_BOX
          ]
        }
        physics Physics {
          density -1
          mass 0.1
        }
      }
      DEF CASTER_WHEEL_FRONT_RIGHT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= fields.tralierLength.value/2.0 >% %<= -(fields.tralierWidth.value/2.0) >% %<= 0.12 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_FRONT_RIGHT Solid {
          translation %<= fields.tralierLength.value/2.0 >% %<= -(fields.tralierWidth.value/2.0) >% %<= 0.0 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.00 0 0.0
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL Solid {
                translation -0.00 0 0.0
                rotation -1 0 0 1.5708
                children [
                  USE BIG_WHEEL
                ]
                name "caster_front_right_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    USE WHEEL_BOUNDING_OBJECT
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            DEF CASTER_SUPPORT Pose {
              translation 0 0 0.06
              children [
                Shape {
                  appearance BrushedAluminium {
                    textureTransform TextureTransform {
                      rotation -1.57
                      scale 2 2
                    }
                  }
                  geometry Box {
                    size 0.05 0.05 0.12
                  }
                }
              ]
            }
          ]
          name "caster_front_right_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.00 0 0.06
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.05 0.05 0.12
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
      DEF CASTER_WHEEL_FRONT_LEFT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= fields.tralierLength.value/2.0 >% %<= fields.tralierWidth.value/2.0 >% %<= 0.12 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_FRONT_LEFT Solid {
          translation %<= fields.tralierLength.value/2.0 >% %<= fields.tralierWidth.value/2.0 >% %<= 0.0 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.00 0 0.0
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL Solid {
                translation -0.00 0 0.0
                rotation -1 0 0 1.5708
                children [
                  USE BIG_WHEEL
                ]
                name "caster_front_left_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    USE WHEEL_BOUNDING_OBJECT
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            USE CASTER_SUPPORT
          ]
          name "caster_front_left_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.00 0 0.06
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.05 0.05 0.12
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
    ]
    boundingObject Group {
      children [
        DEF BASE_BO Pose {
          translation 0 0 0.06
          children [
            Shape {
              geometry Cylinder {
                height 0.276
                radius 0.20
              }
            }
          ]
        }
      ]
    }
    physics Physics {
      density -1
      mass 28.26649
    }
  }
}
