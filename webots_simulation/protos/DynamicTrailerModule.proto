#VRML_SIM R2023a utf8

PROTO DynamicTrailerModule [
    field SFString   name        "dynamic_trailer_module"
    field SFVec3f    translation  0 0 0
    field SFRotation rotation     0 0 1 0

    field SFString   description "Shelves"  
    field SFVec3f    size         1 1 1  
    field SFColor    color       1 0 0   

    field SFString   name1        "Tractor_module1"
    field SFVec3f    translation1 0 0 0        
    field SFRotation rotation1    0 0 1 0     

    field SFString   name2        "Tractor_module2"
    field SFVec3f    translation2 0 0 0        
    field SFRotation rotation2    0 0 1 0  

    field SFString   name3        "Tractor_module3"
    field SFVec3f    translation3 0 0 0        
    field SFRotation rotation3    0 0 1 0  

    field SFString   name4        "Tractor_module4"
    field SFVec3f    translation4 0 0 0        
    field SFRotation rotation4    0 0 1 0   
]
{
    DEF specificSolid Solid {
        name IS name
        translation IS translation
        rotation IS rotation
        children [
                Solid {
                    description IS description
                    translation IS translation1
                    rotation IS rotation1
                    children [
                    Shape {
                        appearance Appearance {
                        material Material {
                            diffuseColor IS color
                        }
                        }
                        geometry Box {
                        size IS size
                        }
                    }
                    ]
                    name IS name1
                    boundingObject Box {
                    size IS size 
                    }
                }
                Solid {
                    description IS description
                    translation IS translation2
                    rotation IS rotation2
                    children [
                    Shape {
                        appearance Appearance {
                        material Material {
                            diffuseColor IS color
                        }
                        }
                        geometry Box {
                        size IS size
                        }
                    }
                    ]
                    name IS name2
                    boundingObject Box {
                    size IS size 
                    }
                }
                Solid {
                    description IS description
                    translation IS translation3
                    rotation IS rotation3
                    children [
                    Shape {
                        appearance Appearance {
                        material Material {
                            diffuseColor IS color
                        }
                        }
                        geometry Box {
                        size IS size
                        }
                    }
                    ]
                    name IS name3
                    boundingObject Box {
                    size IS size
                    }
                }
                Solid {
                    description IS description
                    translation IS translation4
                    rotation IS rotation4
                    children [
                    Shape {
                        appearance Appearance {
                        material Material {
                            diffuseColor IS color
                        }
                        }
                        geometry Box {
                        size IS size
                        }
                    }
                    ]
                    name IS name4
                    boundingObject Box {
                    size IS size 
                    }
                }
            ]
        }
}

    