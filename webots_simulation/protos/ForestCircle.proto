#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/trees/protos/Forest.proto
# tags: nonDeterministic
# keywords: exterior/tree
# Efficient and customizable forest.
# Tree types are:
#  - 'oak tree'
#  - 'crab apple tree'
#  - 'cherry tree'
#  - 'birch tree'
#  - 'palm tree'
#  - 'spruce'
#  - 'white pine'
#  - 'hackberry tree'
#  - 'hazel tree'
#
# The 'random' type choose randomly a tree type each time the node is regenerated.
# The shape of the forest can either be defined using the 'shape' and 'density' fields or the coordinate of each tree can be defined in external files ( X,Y,Z per tree, one tree per line).
# The path to those files must be defined with respect to the world file.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).
# template language: javascript

PROTO ForestCircle [
  field SFVec3f    translation          0 0 0
  field SFRotation rotation             0 0 1 0
  field MFString   treesFiles           []                        # Can be used to define the paths to several files in which are defined the positions of the trees (one tree per line, using the format 'X, Y, Z'). The path to these files must be defined with respect to the world file.
  field SFFloat    density              0.2                       # If the forest is defined using the `shape` field, this field defines the density of three to be generated (in trees per meter square).
  field SFString{"random", "oak tree", "crab apple tree", "cherry tree", "birch tree", "palm tree", "spruce", "white pine", "hackberry tree", "hazel tree"}
                   type                 "random"                  # Defines the type of threes, in case of `random` type, the forest will be mixed.
  field SFInt32    randomSeed           0                         # Defines the seed of the random number generator. A value of 0 sets the seed to the node id and a value smaller than 0 sets a time based random seed.
  field MFString   groundTexture        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/dry_leaf.jpg"   # Defines the texture of the forest ground. If this field is empty the forest ground is not displayed.
  field SFBool     withSnow             FALSE                     # Defines whether the texture used should have snow on top.
  field SFFloat    maxHeight            6                         # Defines the maximum height of the trees.
  field SFFloat    minHeight            2                         # Defines the minimum height of the trees.
  field SFFloat    maxRadius            3                         # Defines the maximum radius of the trees.
  field SFFloat    minRadius            1                         # Defines the minimum radius of the trees.
  field SFFloat    centerX       0.0
  field SFFloat    centerY       0.0
  field SFFloat    radius        50.0   # 扇形区域的外半径
  field SFFloat    innerradius   20.0   # 扇形区域的内半径
  field SFFloat    startAngle     0.0      #扇形起始角度（弧度）
  field SFFloat    angleRange     3.14     #扇形角度范围（弧度）
  field SFInt32    segments   10       #扇形渲染地面采样段数
]
{
  %<
    import * as wbgeometry from 'wbgeometry.js';
    import * as wbrandom from 'wbrandom.js';

    if (fields.randomSeed.value === 0)
      wbrandom.seed(context.id);
    else if (fields.randomSeed.value < 0)
      wbrandom.seed(Date.now());
    else
      wbrandom.seed(fields.randomSeed.value);

    const treeType = {
      'birch tree': 8,
      'cherry tree': 7,
      'crab apple tree': 6,
      'hackberry tree': 5,
      'hazel tree': 4,
      'oak tree': 3,
      'palm tree': 2,
      'spruce': 1,
      'white pine': 0};

    const keys = Object.keys(treeType);
    const treeTypeNumber = keys.length;
    const type = fields.type.value;

    // fields checks
    const groundTexture = fields.groundTexture.value;
    const maxHeight = fields.maxHeight.value;
    const minHeight = fields.minHeight.value;
    const maxRadius = fields.maxRadius.value;
    const minRadius = fields.minRadius.value;
    const centerX = fields.centerX.value;
    const centerY = fields.centerY.value;
    const radiuscircle = fields.radius.value;
    const innerradius = fields.innerradius.value;
    const startAngle = fields.startAngle.value;
    const angleRange = fields.angleRange.value;
    const segments = fields.segments.value;
    const shape = [];
    const shapetheta = [];

    for (let i = 0; i <= segments; ++i) {
      let theta = startAngle + (i / segments) * angleRange;
      shapetheta.push(theta);
      shape.push({
        x: centerX + innerradius * Math.cos(theta),
        y: centerY + innerradius * Math.sin(theta)
      });
      // 外圆点
      shape.push({
        x: centerX + radiuscircle * Math.cos(theta),
        y: centerY + radiuscircle * Math.sin(theta)
      });
    }

    let nbShapePoint = shape.length; 

    let density = fields.density.value;
    if (density < 0) {
      density = fields.density.defaultValue;
      console.error('\'density\' must be greater or equal to 0. Value reset to ' + density + '.');
    }

    const treesFiles = fields.treesFiles.value;
    const treesFilesNumber = treesFiles.length;

    let trees = [];
    // generate the trees table using the treesFiles if set and the shape otherwise
    if (treesFilesNumber > 0) {
      for (let i = 0; i < treesFilesNumber; ++i) {
        // coordinate files are assumed to be relative to the world file
        let worldPath = context.world;
        worldPath = worldPath.substring(0, worldPath.lastIndexOf('/') + 1);
        // read content
        let content = wbfile.readTextFile(worldPath + treesFiles[i]);
        let lines = content.split('\n');
        for (let j = 0; j < lines.length; ++j) {
          let coordinates = lines[j].split(',');
          if(coordinates.length === 3) {
            let x = Number(coordinates[0]);
            let y = Number(coordinates[1]);
            let z = Number(coordinates[2]);
            let sizeFactor = wbrandom.real();
            if (typeof x !== 'undefined' && typeof y !== 'undefined' && typeof z !== 'undefined')
              trees.push({x: x, y: -y, z: z, angle: wbrandom.real(2 * Math.PI), height: maxHeight * sizeFactor + minHeight * (1 - sizeFactor), radius: maxRadius * sizeFactor + minRadius * (1 - sizeFactor)});
          }
        }
      }
    } else {
      // compute the maximum tree number
      let innerRadius = innerradius;
      let outerRadius = radiuscircle;

      let ringArea = Math.PI * (outerRadius * outerRadius - innerRadius * innerRadius) * (angleRange / (2 * Math.PI));

      // 计算当前圆环的树木数量
      let numberOfTree = Math.round(ringArea * density);

      // generate the list of threes
      for (let i = 0; i < numberOfTree; ++i) {

        // 在扇形角度范围内随机采样
        let theta = startAngle + wbrandom.real(0, 1) * angleRange;

        // 修正半径分布（均匀采样）
        let r = Math.sqrt(innerRadius * innerRadius + wbrandom.real(0, 1) * (outerRadius * outerRadius - innerRadius * innerRadius));

        // 转换为笛卡尔坐标
        let x = centerX + r * Math.cos(theta);
        let y = centerY + r * Math.sin(theta);

        // 随机化树木属性
        let sizeFactor = wbrandom.real();
        trees.push({
          x: x,
          y: -y, // 坐标系转换（如 Webots 的 Y 轴向上）
          z: 0,  // 假设地面高度为 0
          angle: wbrandom.real(0, 2 * Math.PI), // 随机旋转角度
          height: minHeight + sizeFactor * (maxHeight - minHeight),
          radius: minRadius + sizeFactor * (maxRadius - minRadius)
        });
      }

    }
    let numberOfTree = trees.length;

    // define the type for each tree in the table
    let types = [];
    if (type === 'random') {
      for (let i = 0; i < numberOfTree; ++i) {
        let j = wbrandom.integer(treeTypeNumber) - 1;
        types.push(keys[j]);
      }
    } else {
      for (let i = 0; i < numberOfTree; ++i)
        types.push(type);
    }
  >%
  Pose {
    translation IS translation
    rotation IS rotation
    children [
      %< if (numberOfTree > 0) { >%
        Shape {
          castShadows FALSE
          appearance PBRAppearance {
            baseColorMap ImageTexture {
              %< if (fields.withSnow.value) { >%
                url [ "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/trees_with_snow.png" ]
              %< } else { >%
                url [ "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/trees.png" ]
              %< } >%
              repeatS FALSE
              repeatT FALSE
            }
            metalness 0
            roughness 1
          }
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                %<
                  let coordBuffer = '';
                  for (let j = 0; j < numberOfTree; ++j) {
                    let circle = wbgeometry.circle(trees[j].radius, 4, {x: trees[j].x, y: trees[j].y}, trees[j].angle);
                    for (let i = 0; i <= 3; ++i) {
                      coordBuffer += circle[i].x + ' ';
                      coordBuffer += circle[i].y + ' ';
                      coordBuffer += trees[j].z + '\n';
                    }
                    // top cylinder
                    for (let i = 0; i <= 3; ++i) {
                      coordBuffer += circle[i].x + ' ';
                      coordBuffer += circle[i].y + ' ';
                      coordBuffer += trees[j].height + trees[j].z + '\n';
                    }
                  }
                >%
                %<= coordBuffer >%
              ]
            }
            texCoord TextureCoordinate {
              point [
                %< for (let i = 0; i <= treeTypeNumber; ++i) { >%
                  0 %<= i / treeTypeNumber >%
                  1 %<= i / treeTypeNumber >%
                %< } >%
              ]
            }
            coordIndex [
              # each tree (4 faces by tree)
              %<
                let coordIndexBuffer = '';
                for (let i = 0; i < numberOfTree; ++i) {
                  coordIndexBuffer += (4 + i * 8) + ' ' + (0 + i * 8) + ' ' + (2 + i * 8) + ' ' + (6 + i * 8) + ' -1\n';
                  coordIndexBuffer += (4 + i * 8) + ' ' + (6 + i * 8) + ' ' + (2 + i * 8) + ' ' + (0 + i * 8) + ' -1\n';
                  coordIndexBuffer += (5 + i * 8) + ' ' + (1 + i * 8) + ' ' + (3 + i * 8) + ' ' + (7 + i * 8) + ' -1\n';
                  coordIndexBuffer += (5 + i * 8) + ' ' + (7 + i * 8) + ' ' + (3 + i * 8) + ' ' + (1 + i * 8) + ' -1\n';
                }
              >%
              %<= coordIndexBuffer >%
            ]
            texCoordIndex [
              %<
                let texCoordIndexBuffer = '';
                for (let i = 0; i < numberOfTree; ++i) {
                  let ix = treeType[types[i]];
                  texCoordIndexBuffer += (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' ' + (2 * (ix + 1) + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1) + 1) + ' ' + (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' ' + (2 * (ix + 1) + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1) + 1) + ' ' + (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' -1\n';
                }
              >%
              %<= texCoordIndexBuffer >%
            ]
          }
        }
      %< } >%
      %< if (groundTexture.length > 0 && nbShapePoint > 2) { >%
        DEF GROUND Shape {
          castShadows FALSE
          appearance PBRAppearance {
            baseColorMap ImageTexture {
                url IS groundTexture
            }
            metalness 0
            roughness 1
          }
          geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= shape[i].x >% %<= -shape[i].y >% %<= 0 >%
                  %< } >%
                ]
              }
              texCoord TextureCoordinate {
                point [
                  %< 
                    const minX = Math.min(...shape.map(p => p.x));
                    const maxX = Math.max(...shape.map(p => p.x));
                    const minY = Math.min(...shape.map(p => p.y));
                    const maxY = Math.max(...shape.map(p => p.y));
                    const rangeX = maxX - minX;
                    const rangeY = maxY - minY;
                  >%
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= (shape[i].x - minX) / rangeX >%  %<= (shape[i].y - minY) / rangeY >%
                  %< } >%
                ]
              }
              coordIndex [
                %< for (let i = 0; i < segments; ++i) { >%
                  %<= 2*i >% %<= 2*i+2 >% %<= 2*i+3 >% %<= 2*i+1 >% -1  
                %< } >%
              ]
          }
        }
      %< } >%
    ]
  }
}
