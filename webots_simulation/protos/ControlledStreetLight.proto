#VRML_SIM R2023b utf8
# license: Creative Commons Attribution 3.0 United States License (original model by <PERSON> & <PERSON>).
# license url: https://creativecommons.org/licenses/by/3.0/legalcode
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/traffic/protos/ControlledStreetLight.proto
# keywords: exterior/street furniture
# Simple model of a controlled street light including a customizable SpotLight.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StreetLight.proto"

PROTO ControlledStreetLight [
  field SFVec3f    translation  0 0 0
  field SFRotation rotation     0 0 1 0
  field SFString   name         "street light"
  field SFString   controller   "<generic>"               # Defines the controller used to make the LED blink.
  field SFString   window       "<generic>"               # Is `Robot.window`.
  field SFBool     supervisor   FALSE                     # Is `Robot.supervisor`.
  field SFFloat    beamWidth    1.1                       # Defines the beam width of the spot light.
  field MFColor    color        [ 1 0.9 0.8 ]             # Defines the color of the spot light.
  field SFFloat    cutOffAngle  1.4                       # Defines the cut-off angle of the spot light.
  field SFVec3f    direction    0.1 0 -1                  # Defines the direction of the spot light.
  field SFFloat    radius       1000                      # Defines the radius of the spot light.
  field SFBool     castShadows  FALSE                     # Defines whether the spot light casts shadows.
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    window IS window
    recognitionColors [
      %<= fields.color.value[0].r >% %<= fields.color.value[0].g >% %<= fields.color.value[0].b >%
    ]
    children [
      LED {
        children [
          SpotLight {
            beamWidth IS beamWidth
            attenuation 0 0 1
            color 0 0 0
            cutOffAngle IS cutOffAngle
            direction IS direction
            location 3.1 0 7.2
            radius IS radius
            castShadows IS castShadows
            intensity 30
          }
        ]
        color IS color
        gradual TRUE
      }
      StreetLight {
        on FALSE
      }
    ]
    name IS name
    model "street light"
    controller IS controller
    supervisor IS supervisor
  }
}
