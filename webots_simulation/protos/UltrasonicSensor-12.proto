#VRML_SIM R2023a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://www.cyberbotics.com/doc/guide/lidar-sensors#velodyne-puck
# Velodyne VLP-16 sensor model based on the Lidar PROTO.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).


EXTERNPROTO "../protos/UltrasonicSensor.proto"

PROTO UltrasonicSensor-12 [
  field   SFVec3f    translation    0 0 0
  field   SFRotation rotation       0 0 0 0
  field   SFString   name           "UltrasonicSensor-12"
]
{
  Group {
    translation IS translation
    rotation IS rotation
    name IS name
    children [
      UltrasonicSensor {
        translation -0.50875549  0.65681165 0.06968092
        rotation  0  0  1   2.2657045
        name "ultrasonic0"
      }
      UltrasonicSensor {
        translation 2.73599417 0.26324976 0.06968092
        rotation  0 0 0 0
        name "ultrasonic1"
      }
      UltrasonicSensor {
        translation 1.64130326 0.71974983 0.06968092
        rotation  0, 0.        1         1.57079633
        name "ultrasonic2"
      }
      UltrasonicSensor {
        translation 2.64584787 -0.6218      0.06968092
        rotation   0.          0.  0       -0.36898405
        name "ultrasonic3"
      }
      UltrasonicSensor {
        translation -0.66599936 -0.26691678  0.06968092
        rotation 0 0 1 3.1415927
        name "ultrasonic4"
      }

      UltrasonicSensor {
        translation 2.73599397 -0.26675024  0.06968092
        rotation  0 0 0 0
        name "ultrasonic5"
      }
      UltrasonicSensor {
        translation 1.64130326 -0.72324147  0.06968092
        rotation  0 0 1   -1.57079633
        name "ultrasonic6"
      }
      UltrasonicSensor {
        translation -0.50876127 -0.66031891  0.06968092
        rotation  0 0 1  -2.2657045
        name "ultrasonic7"
      }
      UltrasonicSensor {
        translation 0.56717459 0.72474983 0.06968092
        rotation -0.4174239, -0.4174239, 0.8071645 1.7834037
        name "ultrasonic8"
      }
      UltrasonicSensor {
        translation 0.56717459 -0.72825017  0.06968092
        rotation  0 0 1  -1.57079633
        name "ultrasonic9"
      }
        UltrasonicSensor {
        translation 2.64584787 0.61829966 0.06968092
        rotation  0 0 1 0.36898405
        name "ultrasonic10"
      }
      UltrasonicSensor {
        translation -0.66599936  0.26341645  0.06968092
        rotation  0, 0, 1 3.14159265
        name "ultrasonic11"
      }
    ]
  }

}
