#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/trees/protos/Forest.proto
# tags: nonDeterministic
# keywords: exterior/tree
# Efficient and customizable forest.
# Tree types are:
#  - 'oak tree'
#  - 'crab apple tree'
#  - 'cherry tree'
#  - 'birch tree'
#  - 'palm tree'
#  - 'spruce'
#  - 'white pine'
#  - 'hackberry tree'
#  - 'hazel tree'
#
# The 'random' type choose randomly a tree type each time the node is regenerated.
# The shape of the forest can either be defined using the 'shape' and 'density' fields or the coordinate of each tree can be defined in external files ( X,Y,Z per tree, one tree per line).
# The path to those files must be defined with respect to the world file.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).
# template language: javascript

PROTO Forest [
  field SFVec3f    translation          0 0 0
  field SFRotation rotation             0 0 1 0
  field MFString   treesFiles           []                        # Can be used to define the paths to several files in which are defined the positions of the trees (one tree per line, using the format 'X, Y, Z'). The path to these files must be defined with respect to the world file.
  field MFVec2f    shape                [-20 -10, 20 -10, 0 25]   # Alternatively, the position of each trees can be defined using the shape field. This field defines the shape of the forest, in that case the position of the trees is randomly generated from this shape.
  field SFFloat    density              0.2                       # If the forest is defined using the `shape` field, this field defines the density of three to be generated (in trees per meter square).
  field SFString{"random", "oak tree", "crab apple tree", "cherry tree", "birch tree", "palm tree", "spruce", "white pine", "hackberry tree", "hazel tree"}
                   type                 "random"                  # Defines the type of threes, in case of `random` type, the forest will be mixed.
  field SFInt32    randomSeed           0                         # Defines the seed of the random number generator. A value of 0 sets the seed to the node id and a value smaller than 0 sets a time based random seed.
  field MFString   groundTexture        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/dry_leaf.jpg"   # Defines the texture of the forest ground. If this field is empty the forest ground is not displayed.
  field SFBool     withSnow             FALSE                     # Defines whether the texture used should have snow on top.
  field SFFloat    maxHeight            6                         # Defines the maximum height of the trees.
  field SFFloat    minHeight            2                         # Defines the minimum height of the trees.
  field SFFloat    maxRadius            3                         # Defines the maximum radius of the trees.
  field SFFloat    minRadius            1                         # Defines the minimum radius of the trees.
]
{
  %<
    import * as wbgeometry from 'wbgeometry.js';
    import * as wbrandom from 'wbrandom.js';

    if (fields.randomSeed.value === 0)
      wbrandom.seed(context.id);
    else if (fields.randomSeed.value < 0)
      wbrandom.seed(Date.now());
    else
      wbrandom.seed(fields.randomSeed.value);

    const treeType = {
      'birch tree': 8,
      'cherry tree': 7,
      'crab apple tree': 6,
      'hackberry tree': 5,
      'hazel tree': 4,
      'oak tree': 3,
      'palm tree': 2,
      'spruce': 1,
      'white pine': 0};

    const keys = Object.keys(treeType);
    const treeTypeNumber = keys.length;
    const type = fields.type.value;

    // fields checks
    const shape = fields.shape.value;
    const groundTexture = fields.groundTexture.value;
    const nbShapePoint = shape.length;
    const maxHeight = fields.maxHeight.value;
    const minHeight = fields.minHeight.value;
    const maxRadius = fields.maxRadius.value;
    const minRadius = fields.minRadius.value;

    let density = fields.density.value;
    if (density < 0) {
      density = fields.density.defaultValue;
      console.error('\'density\' must be greater or equal to 0. Value reset to ' + density + '.');
    }

    const treesFiles = fields.treesFiles.value;
    const treesFilesNumber = treesFiles.length;

    let trees = [];
    // generate the trees table using the treesFiles if set and the shape otherwise
    if (treesFilesNumber > 0) {
      for (let i = 0; i < treesFilesNumber; ++i) {
        // coordinate files are assumed to be relative to the world file
        let worldPath = context.world;
        worldPath = worldPath.substring(0, worldPath.lastIndexOf('/') + 1);
        // read content
        let content = wbfile.readTextFile(worldPath + treesFiles[i]);
        let lines = content.split('\n');
        for (let j = 0; j < lines.length; ++j) {
          let coordinates = lines[j].split(',');
          if(coordinates.length === 3) {
            let x = Number(coordinates[0]);
            let y = Number(coordinates[1]);
            let z = Number(coordinates[2]);
            let sizeFactor = wbrandom.real();
            if (typeof x !== 'undefined' && typeof y !== 'undefined' && typeof z !== 'undefined')
              trees.push({x: x, y: -y, z: z, angle: wbrandom.real(2 * Math.PI), height: maxHeight * sizeFactor + minHeight * (1 - sizeFactor), radius: maxRadius * sizeFactor + minRadius * (1 - sizeFactor)});
          }
        }
      }
    } else {
      // compute the maximum tree number
      let xMin = shape[0].x;
      let xMax = shape[0].x;
      let yMin = shape[0].y;
      let yMax = shape[0].y;

      for (let i = 0; i < nbShapePoint; ++i) {
        if (xMax < shape[i].x)
          xMax = shape[i].x;

        if (xMin > shape[i].x)
          xMin = shape[i].x;

        if (yMax < shape[i].y)
          yMax = shape[i].y;

        if (yMin > shape[i].y)
          yMin = shape[i].y;
      }
      let numberOfTree = Math.round((xMax - xMin) * (yMax - yMin) * density);  // we assume a density of 0.01 tree per meter square

      // generate the list of threes
      for (let i = 0; i < numberOfTree; ++i) {
        let x = wbrandom.real(xMin, xMax);
        let y = wbrandom.real(yMin, yMax);
        let sizeFactor = wbrandom.real();

        if (wbgeometry.isPoint2InPolygon({x: x, y: y}, shape))
            trees.push({x: x, y: -y, z: 0, angle: wbrandom.real(2 * Math.PI), height: maxHeight * sizeFactor + minHeight * (1 - sizeFactor), radius: maxRadius * sizeFactor + minRadius * (1 - sizeFactor)});
      }
    }
    let numberOfTree = trees.length;

    // define the type for each tree in the table
    let types = [];
    if (type === 'random') {
      for (let i = 0; i < numberOfTree; ++i) {
        let j = wbrandom.integer(treeTypeNumber) - 1;
        types.push(keys[j]);
      }
    } else {
      for (let i = 0; i < numberOfTree; ++i)
        types.push(type);
    }
  >%
  Pose {
    translation IS translation
    rotation IS rotation
    children [
      %< if (numberOfTree > 0) { >%
        Shape {
          castShadows FALSE
          appearance PBRAppearance {
            baseColorMap ImageTexture {
              %< if (fields.withSnow.value) { >%
                url [ "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/trees_with_snow.png" ]
              %< } else { >%
                url [ "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/trees.png" ]
              %< } >%
              repeatS FALSE
              repeatT FALSE
            }
            metalness 0
            roughness 1
          }
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                %<
                  let coordBuffer = '';
                  for (let j = 0; j < numberOfTree; ++j) {
                    let circle = wbgeometry.circle(trees[j].radius, 4, {x: trees[j].x, y: trees[j].y}, trees[j].angle);
                    for (let i = 0; i <= 3; ++i) {
                      coordBuffer += circle[i].x + ' ';
                      coordBuffer += circle[i].y + ' ';
                      coordBuffer += trees[j].z + '\n';
                    }
                    // top cylinder
                    for (let i = 0; i <= 3; ++i) {
                      coordBuffer += circle[i].x + ' ';
                      coordBuffer += circle[i].y + ' ';
                      coordBuffer += trees[j].height + trees[j].z + '\n';
                    }
                  }
                >%
                %<= coordBuffer >%
              ]
            }
            texCoord TextureCoordinate {
              point [
                %< for (let i = 0; i <= treeTypeNumber; ++i) { >%
                  0 %<= i / treeTypeNumber >%
                  1 %<= i / treeTypeNumber >%
                %< } >%
              ]
            }
            coordIndex [
              # each tree (4 faces by tree)
              %<
                let coordIndexBuffer = '';
                for (let i = 0; i < numberOfTree; ++i) {
                  coordIndexBuffer += (4 + i * 8) + ' ' + (0 + i * 8) + ' ' + (2 + i * 8) + ' ' + (6 + i * 8) + ' -1\n';
                  coordIndexBuffer += (4 + i * 8) + ' ' + (6 + i * 8) + ' ' + (2 + i * 8) + ' ' + (0 + i * 8) + ' -1\n';
                  coordIndexBuffer += (5 + i * 8) + ' ' + (1 + i * 8) + ' ' + (3 + i * 8) + ' ' + (7 + i * 8) + ' -1\n';
                  coordIndexBuffer += (5 + i * 8) + ' ' + (7 + i * 8) + ' ' + (3 + i * 8) + ' ' + (1 + i * 8) + ' -1\n';
                }
              >%
              %<= coordIndexBuffer >%
            ]
            texCoordIndex [
              %<
                let texCoordIndexBuffer = '';
                for (let i = 0; i < numberOfTree; ++i) {
                  let ix = treeType[types[i]];
                  texCoordIndexBuffer += (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' ' + (2 * (ix + 1) + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1) + 1) + ' ' + (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' ' + (2 * (ix + 1) + 1) + ' -1\n';
                  texCoordIndexBuffer += (2 * (ix + 1) + 1) + ' ' + (2 * (ix + 1)) + ' ' + (2 * ix) + ' ' + (2 * ix + 1) + ' -1\n';
                }
              >%
              %<= texCoordIndexBuffer >%
            ]
          }
        }
      %< } >%
      %< if (groundTexture.length > 0 && nbShapePoint > 2) { >%
        DEF GROUND Shape {
          castShadows FALSE
          appearance PBRAppearance {
            baseColorMap ImageTexture {
                url IS groundTexture
            }
            metalness 0
            roughness 1
          }
          geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= shape[i].x >% %<= -shape[i].y >% %<= 0 >%
                  %< } >%
                ]
              }
              texCoord TextureCoordinate {
                point [
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= shape[i].x - shape[0].x >% %<= shape[i].y - shape[0].y >%
                  %< } >%
                ]
              }
              coordIndex [
                %< for (let i = 0; i < nbShapePoint; ++i) { >%
                  %<= (nbShapePoint - i) - 1 >%
                %< } >%
                -1
              ]
              texCoordIndex [
                %< for (let i = 0; i < nbShapePoint; ++i) { >%
                  %<= (nbShapePoint - i) - 1 >%
                %< } >%
                -1
              ]
          }
        }
      %< } >%
    ]
  }
}
