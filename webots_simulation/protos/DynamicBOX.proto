#VRML_SIM R2023b utf8
# DynamicBOX.proto
# template language: javascript

PROTO DynamicBOX [
  field SFVec3f    translation     0 0 0
  field SFRotation rotation        0 0 1 0
  field SFString   name            "dbox"
  field SFString   controller      "path_motion"
  field MFString   controllerArgs  []
  field SFVec3f    boxSize        1 1 1
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    children [
      DBOXUltrasonicSensor {
        name "us1"
        translation %<= (fields.boxSize.value.x/2) >% %<= (fields.boxSize.value.y/2) >% 0.5
      }
      DBOXUltrasonicSensor {
        name "us0"
        translation %<= (fields.boxSize.value.x/2) >% 0 0.5
      }
      DBOXUltrasonicSensor {
        name "us2"
        translation %<= (fields.boxSize.value.x/2) >% %<= (-fields.boxSize.value.y/2) >% 0.5
      }
      Shape {
        appearance Appearance {
          material Material {
            diffuseColor 0 0 1
          }
        }
        geometry Box {
          size IS boxSize
        }
      }
    ]
    name IS name
    boundingObject Box {
      size IS boxSize
    }
    controller IS controller
    controllerArgs IS controllerArgs
    supervisor TRUE
  }
}
