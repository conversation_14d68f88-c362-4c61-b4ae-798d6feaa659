#VRML_SIM R2023b utf8
# license: Creative Commons Attribution 4.0 International License.
# license url: https://creativecommons.org/licenses/by/4.0/legalcode
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/trees/protos/PalmTree.proto
# keywords: exterior/tree
# A palm tree with optional boundingObject.
# template language: javascript

PROTO PalmTree [
  field SFVec3f    translation           0 0 0
  field SFRotation rotation              0 0 1 0
  field SFString   name                  "palm tree"
  field SFBool     enableBoundingObject  TRUE         # Defines whether the tree should have a bounding object.
]
{
  Solid {
    translation IS translation
    rotation IS rotation
    name IS name
    model "palm tree"
    recognitionColors [
      0 0.16470588235294117 0
      0.30196078431372547 0.2196078431372549 0.12156862745098039
    ]
    children [
      Shape {
        appearance PBRAppearance {
          baseColorMap ImageTexture {
            url [
              "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/palm_tree_branches.jpg"
            ]
          }
          metalness 0
          roughness 1
        }
        geometry IndexedFaceSet {
          coord Coordinate {
            point [
              -0.126382 -0.221372 2.13288 0.039536 -0.289285 2.124096 -0.039553 -1.220554 3.081188 0.005343 -1.211139 3.060389 -0.018031 -2.228758 3.505941 0.012291 -0.20026 2.13561 -0.004813 -1.223097 3.086561 0.228012 0.059203 2.14536 0.152984 0.222027 2.136575 0.8085 0.827415 3.112753 0.772509 0.849762 3.092441 1.463392 1.623439 3.306905 0.112196 0.138336 2.148091 0.785936 0.852495 3.12014 -0.154303 0 2.207042 -0.05735 -0.150834 2.198339 -0.589441 -0.819504 3.471406 -0.557067 -0.853777 3.450862 -1.069227 -1.468206 3.802844 -0.028598 -0.06227 2.209748 -0.558298 -0.838437 3.477005 0.10875 0.134289 2.187437 -0.065005 0.178455 2.178693 -0.111561 1.019182 3.377182 -0.161803 1.01116 3.354693 -0.278806 1.961841 3.477365 -0.025634 0.094082 2.190155 -0.153033 1.013478 3.383568 0.034366 -0.190452 2.171575 0.191565 -0.104253 2.162838 0.764002 -0.681121 3.400301 0.795899 -0.644258 3.378673 1.399493 -0.944456 3.839738 0.105227 -0.069396 2.174291 0.789025 -0.649822 3.407911 -0.182149 0.113953 2.157953 -0.23833 -0.056298 2.14923 -1.159628 -0.05237 3.480597 -1.156667 -0.100235 3.457923 -2.720327 -0.204312 2.673609 -0.151412 -0.022908 2.160665 -1.156708 -0.090886 3.486697 0.084785 -0.111183 2.232193 0.116729 0.065231 2.223504 0.573662 0.078156 3.693774 0.580333 0.130115 3.679664 1.808989 0.366767 4.602163 0.035299 0.020068 2.234894 0.560564 0.116409 3.698004 0.036616 0.12544 2.264981 -0.107062 0.018205 2.256299 -0.40414 0.268749 3.704693 -0.444085 0.239629 3.692159 -1.329179 0.761031 4.798984 -0.016705 -0.004301 2.26768 -0.41823 0.23549 3.707582 -0.11858 -0.04497 2.307797 0.059634 -0.064534 2.299132 0.019933 -0.213359 3.508347 0.070968 -0.221142 3.504105 0.117684 -0.471983 4.694347 0.0089 0.013552 2.310491 0.054968 -0.195734 3.510038
            ]
          }
          texCoord TextureCoordinate {
            point [
              0 0, 0.3333 0, 0.0432 1.25, 0.0432 1.25, 0.3333 0, 0.3765 1.25, 0.0432 1.25, 0.3765 1.25, 0.0863 2.5, 0.3333 0, 0.6665 0, 0.3765 1.25, 0.3765 1.25, 0.6665 0, 0.7095 1.25, 0.3765 1.25, 0.7095 1.25, 0.4194 2.5, 0.6665 0, 1 0, 0.7095 1.25, 0.7095 1.25, 1 0, 1.043 1.25, 0.7095 1.25, 1.043 1.25, 0.7529 2.5, 0 0, 0.3333 0, -0.0237 1.25, -0.0237 1.25, 0.3333 0, 0.3096 1.25, -0.0237 1.25, 0.3096 1.25, -0.0474 2.5, 0.3333 0, 0.6665 0, 0.3096 1.25, 0.3096 1.25, 0.6665 0, 0.6426 1.25, 0.3096 1.25, 0.6426 1.25, 0.2856 2.5, 0.6665 0, 1 0, 0.6426 1.25, 0.6426 1.25, 1 0, 0.9761 1.25, 0.6426 1.25, 0.9761 1.25, 0.6191 2.5, 0 0, 0.3333 0, 0.0319 1.25, 0.0319 1.25, 0.3333 0, 0.3652 1.25, 0.0319 1.25, 0.3652 1.25, 0.0638 2.5, 0.3333 0, 0.6665 0, 0.3652 1.25, 0.3652 1.25, 0.6665 0, 0.6982 1.25, 0.3652 1.25, 0.6982 1.25, 0.397 2.5, 0.6665 0, 1 0, 0.6982 1.25, 0.6982 1.25, 1 0, 1.0312 1.25, 0.6982 1.25, 1.0312 1.25, 0.7305 2.5, 0 0, 0.3333 0, 0.0098 1.25, 0.0098 1.25, 0.3333 0, 0.343 1.25, 0.0098 1.25, 0.343 1.25, 0.0196 2.5, 0.3333 0, 0.6665 0, 0.343 1.25, 0.343 1.25, 0.6665 0, 0.6763 1.25, 0.343 1.25, 0.6763 1.25, 0.3528 2.5, 0.6665 0, 1 0, 0.6763 1.25, 0.6763 1.25, 1 0, 1.0098 1.25, 0.6763 1.25, 1.0098 1.25, 0.686 2.5, 0 0, 0.3333 0, -0.0109 1.25, -0.0109 1.25, 0.3333 0, 0.3223 1.25, -0.0109 1.25, 0.3223 1.25, -0.0218 2.5, 0.3333 0, 0.6665 0, 0.3223 1.25, 0.3223 1.25, 0.6665 0, 0.6553 1.25, 0.3223 1.25, 0.6553 1.25, 0.3113 2.5, 0.6665 0, 1 0, 0.6553 1.25, 0.6553 1.25, 1 0, 0.9888 1.25, 0.6553 1.25, 0.9888 1.25, 0.6445 2.5, 0 0, 0.3333 0, 0.0206 1.25, 0.0206 1.25, 0.3333 0, 0.3538 1.25, 0.0206 1.25, 0.3538 1.25, 0.0412 2.5, 0.3333 0, 0.6665 0, 0.3538 1.25, 0.3538 1.25, 0.6665 0, 0.687 1.25, 0.3538 1.25, 0.687 1.25, 0.3745 2.5, 0.6665 0, 1 0, 0.687 1.25, 0.687 1.25, 1 0, 1.0205 1.25, 0.687 1.25, 1.0205 1.25, 0.7075 2.5, 0 0, 0.3333 0, 0.0148 1.25, 0.0148 1.25, 0.3333 0, 0.3481 1.25, 0.0148 1.25, 0.3481 1.25, 0.0296 2.5, 0.3333 0, 0.6665 0, 0.3481 1.25, 0.3481 1.25, 0.6665 0, 0.6812 1.25, 0.3481 1.25, 0.6812 1.25, 0.3628 2.5, 0.6665 0, 1 0, 0.6812 1.25, 0.6812 1.25, 1 0, 1.0146 1.25, 0.6812 1.25, 1.0146 1.25, 0.6963 2.5, 0 0, 0.3333 0, 0.0359 1.25, 0.0359 1.25, 0.3333 0, 0.3691 1.25, 0.0359 1.25, 0.3691 1.25, 0.0719 2.5, 0.3333 0, 0.6665 0, 0.3691 1.25, 0.3691 1.25, 0.6665 0, 0.7021 1.25, 0.3691 1.25, 0.7021 1.25, 0.405 2.5, 0.6665 0, 1 0, 0.7021 1.25, 0.7021 1.25, 1 0, 1.0352 1.25, 0.7021 1.25, 1.0352 1.25, 0.7383 2.5, 0 0, 0.3333 0, -0.0135 1.25, -0.0135 1.25, 0.3333 0, 0.3198 1.25, -0.0135 1.25, 0.3198 1.25, -0.0269 2.5, 0.3333 0, 0.6665 0, 0.3198 1.25, 0.3198 1.25, 0.6665 0, 0.6528 1.25, 0.3198 1.25, 0.6528 1.25, 0.3062 2.5, 0.6665 0, 1 0, 0.6528 1.25, 0.6528 1.25, 1 0, 0.9863 1.25, 0.6528 1.25, 0.9863 1.25, 0.6396 2.5
            ]
          }
          coordIndex [
            0, 1, 2, -1, 2, 1, 3, -1, 2, 3, 4, -1, 1, 5, 3, -1, 3, 5, 6, -1, 3, 6, 4, -1, 5, 0, 6, -1, 6, 0, 2, -1, 6, 2, 4, -1, 7, 8, 9, -1, 9, 8, 10, -1, 9, 10, 11, -1, 8, 12, 10, -1, 10, 12, 13, -1, 10, 13, 11, -1, 12, 7, 13, -1, 13, 7, 9, -1, 13, 9, 11, -1, 14, 15, 16, -1, 16, 15, 17, -1, 16, 17, 18, -1, 15, 19, 17, -1, 17, 19, 20, -1, 17, 20, 18, -1, 19, 14, 20, -1, 20, 14, 16, -1, 20, 16, 18, -1, 21, 22, 23, -1, 23, 22, 24, -1, 23, 24, 25, -1, 22, 26, 24, -1, 24, 26, 27, -1, 24, 27, 25, -1, 26, 21, 27, -1, 27, 21, 23, -1, 27, 23, 25, -1, 28, 29, 30, -1, 30, 29, 31, -1, 30, 31, 32, -1, 29, 33, 31, -1, 31, 33, 34, -1, 31, 34, 32, -1, 33, 28, 34, -1, 34, 28, 30, -1, 34, 30, 32, -1, 35, 36, 37, -1, 37, 36, 38, -1, 37, 38, 39, -1, 36, 40, 38, -1, 38, 40, 41, -1, 38, 41, 39, -1, 40, 35, 41, -1, 41, 35, 37, -1, 41, 37, 39, -1, 42, 43, 44, -1, 44, 43, 45, -1, 44, 45, 46, -1, 43, 47, 45, -1, 45, 47, 48, -1, 45, 48, 46, -1, 47, 42, 48, -1, 48, 42, 44, -1, 48, 44, 46, -1, 49, 50, 51, -1, 51, 50, 52, -1, 51, 52, 53, -1, 50, 54, 52, -1, 52, 54, 55, -1, 52, 55, 53, -1, 54, 49, 55, -1, 55, 49, 51, -1, 55, 51, 53, -1, 56, 57, 58, -1, 58, 57, 59, -1, 58, 59, 60, -1, 57, 61, 59, -1, 59, 61, 62, -1, 59, 62, 60, -1, 61, 56, 62, -1, 62, 56, 58, -1, 62, 58, 60, -1
          ]
          texCoordIndex [
            0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1, 60, 61, 62, -1, 63, 64, 65, -1, 66, 67, 68, -1, 69, 70, 71, -1, 72, 73, 74, -1, 75, 76, 77, -1, 78, 79, 80, -1, 81, 82, 83, -1, 84, 85, 86, -1, 87, 88, 89, -1, 90, 91, 92, -1, 93, 94, 95, -1, 96, 97, 98, -1, 99, 100, 101, -1, 102, 103, 104, -1, 105, 106, 107, -1, 108, 109, 110, -1, 111, 112, 113, -1, 114, 115, 116, -1, 117, 118, 119, -1, 120, 121, 122, -1, 123, 124, 125, -1, 126, 127, 128, -1, 129, 130, 131, -1, 132, 133, 134, -1, 135, 136, 137, -1, 138, 139, 140, -1, 141, 142, 143, -1, 144, 145, 146, -1, 147, 148, 149, -1, 150, 151, 152, -1, 153, 154, 155, -1, 156, 157, 158, -1, 159, 160, 161, -1, 162, 163, 164, -1, 165, 166, 167, -1, 168, 169, 170, -1, 171, 172, 173, -1, 174, 175, 176, -1, 177, 178, 179, -1, 180, 181, 182, -1, 183, 184, 185, -1, 186, 187, 188, -1, 189, 190, 191, -1, 192, 193, 194, -1, 195, 196, 197, -1, 198, 199, 200, -1, 201, 202, 203, -1, 204, 205, 206, -1, 207, 208, 209, -1, 210, 211, 212, -1, 213, 214, 215, -1, 216, 217, 218, -1, 219, 220, 221, -1, 222, 223, 224, -1, 225, 226, 227, -1, 228, 229, 230, -1, 231, 232, 233, -1, 234, 235, 236, -1, 237, 238, 239, -1, 240, 241, 242, -1
          ]
        }
      }
      Shape {
        appearance PBRAppearance {
          baseColorMap ImageTexture {
            url [
              "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/palm_tree_trunk_auxiliary.png"
            ]
          }
          metalness 0
          roughness 1
        }
        geometry IndexedFaceSet {
          coord Coordinate {
            point [
              0.287731 0.317623 0.197506 -0.101468 0.457981 0.327268 0.172911 0.42755 0.457489 0.526654 -0.174806 0.66774 0.082809 0.275801 1.966708 0.151885 0.162402 0.095735 0.276493 0.068679 0.085024 0.206463 0.223434 0.165144 0.355447 0.181216 0.09544 0.344861 0.293058 0.168911 -0.02941 0.221836 0.193717 0.120966 0.262867 0.18435 -0.060256 0.29953 0.301488 0.07234 0.388382 0.255927 -0.051411 0.47691 0.299102 0.095024 0.18384 0.327971 0.241578 0.129868 0.322296 0.103972 0.252722 0.430641 0.268181 0.267752 0.382011 0.235784 0.41771 0.436309 0.197666 -0.084649 0.427145 0.155477 -0.234422 0.416796 0.292778 -0.109865 0.595689 0.311171 -0.270719 0.567517 0.515646 -0.236694 0.649845 -0.116558 0.107515 1.749631 0.029857 0.099847 1.744313 -0.105048 0.20117 1.863664 0.055245 0.126268 1.873499 0.025732 0.296175 1.956728
            ]
          }
          texCoord TextureCoordinate {
            point [
              -1.7334 0, 1 0, 0.0533 0.5, 0.0533 0.5, 1 0, 1 0.5, -0.0804 1, 1 1, 1 0.5, 0.0533 0.5, -0.0804 1, 1 0.5, 0.0533 0.5, 1 0.5, -0.0804 1, -0.0804 1, 1 0.5, 1 1, 0.0533 0.5, 1 0.5, 1 0, -1.7334 0, 0.0533 0.5, 1 0, -1.7334 0, 1 0, 0.0533 0.5, 0.0533 0.5, 1 0, 1 0.5, -0.0804 1, 1 0.5, 1 1, 0.0533 0.5, -0.0804 1, 1 0.5, -1.7334 0, 1 0, 0.0533 0.5, 0.0533 0.5, 1 0, 1 0.5, -0.0804 1, 1 1, 1 0.5, 0.0533 0.5, -0.0804 1, 1 0.5, 0 1, 1.1006 0.5, 1.0703 1, 0 0.5, 0 1, 1.1006 0.5, 0 0.5, 1.1006 0.5, 2.7793 0, 0 0, 0 0.5, 2.7793 0
            ]
          }
          coordIndex [
            5, 6, 7, -1, 7, 6, 8, -1, 0, 9, 8, -1, 7, 0, 8, -1, 12, 13, 1, -1, 1, 13, 14, -1, 12, 13, 11, -1, 10, 12, 11, -1, 15, 16, 17, -1, 17, 16, 18, -1, 2, 18, 19, -1, 17, 2, 18, -1, 20, 21, 22, -1, 22, 21, 23, -1, 3, 24, 23, -1, 22, 3, 23, -1, 29, 28, 4, -1, 27, 29, 28, -1, 27, 28, 26, -1, 25, 27, 26, -1
          ]
          texCoordIndex [
            0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1
          ]
        }
      }
      Shape {
        appearance PBRAppearance {
          baseColorMap ImageTexture {
            url [
              "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/palm_tree_trunk_spikes.jpg"
            ]
          }
          metalness 0
          roughness 0.5
        }
        geometry IndexedFaceSet {
          coord Coordinate {
            point [
              0.236016 0.066708 0.020717 0.109833 0.23838 -0.007897 0.26163 0.147465 0.13747 0.160964 0.293628 0.09849 0.31577 0.297627 0.193369 0.040957 0.113691 0.088922 0.254073 0.385302 0.148018 0.129352 0.186851 0.190203 0.287731 0.317623 0.197506 0.05136 -0.205407 0.013457 0.268055 -0.072246 0.006886 0.160298 -0.252614 0.215931 0.307626 -0.148016 0.188906 0.334944 -0.277751 0.279363 0.123662 -0.016136 0.209451 0.384277 -0.209356 0.228168 0.185058 -0.113598 0.26138 0.3381 -0.272415 0.280091 -0.127117 -0.210021 -0.007609 0.082248 -0.248284 -0.037855 -0.090763 -0.293162 0.107121 0.082547 -0.32963 0.066493 -0.028236 -0.444049 0.158099 0.047583 -0.114329 0.064717 0.076739 -0.464464 0.111838 0.032836 -0.234184 0.162523 0.005907 -0.439744 0.162437 0.228104 -0.081467 0.161816 0.228523 0.132667 0.142712 0.302621 -0.04081 0.363141 0.315749 0.139539 0.340085 0.493405 0.072172 0.478497 0.084641 0.066625 0.210934 0.48637 0.177082 0.432985 0.207291 0.064503 0.404081 0.48033 0.104177 0.481773 0.107579 0.17304 0.015043 -0.142887 0.222597 0.010113 0.041287 0.300648 0.263998 -0.135096 0.317245 0.225874 -0.081473 0.463091 0.325544 -0.081466 0.093824 0.239929 -0.174578 0.455215 0.271697 -0.071507 0.21262 0.303959 -0.101468 0.457981 0.327268 -0.223368 -0.097394 0.13531 -0.077156 -0.25357 0.114277 -0.227716 -0.185816 0.313984 -0.112278 -0.320794 0.279404 -0.26968 -0.391508 0.391332 -0.017082 -0.107357 0.183017 -0.192948 -0.456238 0.344602 -0.083377 -0.195444 0.347742 -0.239249 -0.407911 0.393975 -0.192016 0.11431 0.010421 -0.260037 -0.130707 0.001795 -0.298191 0.078078 0.189705 -0.33329 -0.098257 0.160322 -0.474421 0.000704 0.24105 -0.102009 -0.072445 0.177659 -0.472728 -0.103151 0.199341 -0.223902 -0.040084 0.239128 -0.46676 -0.032894 0.245253 0.205384 0.123102 0.246697 0.03935 0.25839 0.228162 0.185315 0.193972 0.400441 0.053371 0.313762 0.370786 0.205489 0.415905 0.455126 -0.001157 0.102532 0.289668 0.124122 0.477833 0.417823 0.040485 0.182807 0.431653 0.172911 0.42755 0.457489 -0.065924 -0.231246 0.21843 0.147541 -0.216177 0.19802 -0.00228 -0.282286 0.382555 0.175497 -0.278319 0.349568 0.129536 -0.438583 0.450453 0.071497 -0.076704 0.264922 0.228602 -0.421362 0.406897 0.095925 -0.175781 0.415393 0.16142 -0.425202 0.452986 -0.110374 0.214612 0.189396 -0.257032 0.058482 0.171179 -0.174221 0.230842 0.355849 -0.30683 0.107792 0.333166 -0.404512 0.253409 0.436488 -0.105579 0.007938 0.236185 -0.468893 0.187403 0.392879 -0.174771 0.087803 0.392977 -0.408403 0.2436 0.437414 0.219378 0.035145 0.613611 0.108869 0.21948 0.608509 0.215345 0.061181 0.794444 0.13407 0.224197 0.786963 0.27798 0.24026 0.933658 0.012324 0.078034 0.628271 0.229055 0.330128 0.896037 0.072115 0.093648 0.810704 0.259888 0.253243 0.934587 0.13454 -0.195198 0.302608 0.259209 -0.020666 0.287934 0.235582 -0.210572 0.539449 0.345734 -0.069886 0.510155 0.516874 -0.206693 0.665745 0.097952 0.010098 0.340518 0.570707 -0.122012 0.622042 0.210928 -0.066655 0.565182 0.526654 -0.174806 0.66774 -0.23335 0.050337 0.276261 -0.206339 -0.162634 0.264792 -0.27117 0.013134 0.488503 -0.261817 -0.168322 0.473632 -0.438323 -0.105743 0.632748 -0.064534 -0.074114 0.30691 -0.409247 -0.209487 0.600801 -0.156602 -0.0759 0.516818 -0.411926 -0.151704 0.63645 -0.184537 -0.145505 0.360591 -0.002131 -0.25885 0.35058 -0.18198 -0.21691 0.564043 -0.035064 -0.319968 0.541727 -0.149346 -0.480982 0.681663 0.01876 -0.091623 0.386823 -0.064553 -0.535343 0.64475 -0.039451 -0.179982 0.58119 -0.12022 -0.487562 0.683004 0.03503 0.233837 0.333304 -0.175777 0.19257 0.324617 -0.001452 0.257904 0.528427 -0.182239 0.23748 0.518889 -0.138084 0.36467 0.667343 -0.076662 0.055229 0.356734 -0.21674 0.312974 0.622238 -0.081533 0.135235 0.549754 -0.155446 0.353484 0.668564 0.232768 -0.020198 0.388485 0.176728 0.187128 0.378805 0.25798 0.017921 0.569661 0.224935 0.196757 0.559287 0.376734 0.149349 0.691445 0.047224 0.078987 0.41419 0.322107 0.236305 0.660307 0.130039 0.089355 0.591266 0.350191 0.184493 0.694031 0.161288 0.163563 0.474532 -0.034988 0.251096 0.468747 0.138888 0.203118 0.682797 -0.021103 0.28682 0.665454 0.070791 0.428538 0.827124 -0.032923 0.07995 0.489264 -0.021388 0.470463 0.788584 0.002255 0.147663 0.697659 0.039936 0.430566 0.828235 -0.155241 0.173192 0.418613 -0.25635 -0.016464 0.413707 -0.175124 0.159909 0.604299 -0.272737 0.006141 0.596044 -0.374569 0.179831 0.743551 -0.085322 -0.02639 0.432928 -0.408032 0.067328 0.728743 -0.129056 0.020309 0.619686 -0.380938 0.10047 0.746665 -0.005426 -0.231191 0.447621 0.197942 -0.161551 0.44437 0.027825 -0.240493 0.709969 0.204379 -0.196081 0.708288 0.184973 -0.37252 0.924236 0.079295 -0.037125 0.456965 0.284406 -0.33411 0.889922 0.089038 -0.105794 0.719941 0.215108 -0.344587 0.925678 0.170986 -0.149058 0.530599 0.24488 0.052809 0.52776 0.217475 -0.122989 0.829381 0.290244 0.040959 0.809305 0.47761 -0.035494 1.043262 0.073514 0.038895 0.540226 0.519258 0.055312 0.998892 0.149303 0.008158 0.830086 0.476672 -0.011849 1.043746 -0.228014 -0.008542 0.501911 -0.144197 -0.206435 0.496364 -0.240872 -0.04211 0.757444 -0.182607 -0.214575 0.747606 -0.374776 -0.221059 0.955104 -0.029092 -0.079942 0.518026 -0.32893 -0.31815 0.921495 -0.103655 -0.094383 0.775432 -0.342895 -0.251097 0.95708 -0.022263 0.224288 0.559561 -0.213928 0.12699 0.55569 -0.062784 0.203876 0.757247 -0.229157 0.133746 0.742379 -0.244829 0.306756 0.900128 -0.079078 0.020224 0.567235 -0.33542 0.252266 0.871534 -0.105604 0.062097 0.766965 -0.278628 0.268276 0.901452 -0.136574 -0.177139 0.585279 0.069986 -0.236231 0.577549 -0.100048 -0.210805 0.786541 0.069851 -0.2713 0.767551 -0.008198 -0.423689 0.918343 0.04409 -0.067779 0.603437 0.088231 -0.452286 0.877442 0.027738 -0.137659 0.80411 0.019642 -0.421256 0.919457 0.111241 0.186943 0.697915 -0.101566 0.217103 0.693196 0.081108 0.236552 0.983808 -0.095158 0.272648 0.96287 -0.053743 0.456907 1.185678 -0.05238 0.052933 0.711761 -0.15408 0.474097 1.141224 -0.033946 0.143451 0.99195 -0.08467 0.450634 1.186953 -0.182672 0.123942 0.643017 -0.227788 -0.086242 0.640867 -0.188177 0.103072 0.848048 -0.240207 -0.0716 0.843503 -0.351299 0.010643 1.017331 -0.059841 -0.048624 0.650737 -0.380126 -0.086114 0.976937 -0.104786 -0.018349 0.860276 -0.347594 -0.007658 1.017952 0.047531 -0.213843 0.669541 0.223695 -0.090731 0.664257 0.083389 -0.21846 0.892425 0.240698 -0.126773 0.887361 0.260743 -0.26807 1.062046 0.075931 -0.004356 0.684834 0.330865 -0.20056 1.015728 0.105568 -0.072648 0.90702 0.273573 -0.247891 1.063184 -0.207668 -0.059342 0.726024 -0.072608 -0.226544 0.72144 -0.218083 -0.117433 1.0097 -0.114147 -0.26391 0.988742 -0.28277 -0.374154 1.207681 0.003402 -0.072905 0.739682 -0.219108 -0.456 1.169506 -0.070452 -0.127201 1.015628 -0.248999 -0.392771 1.208637 -0.070489 0.200872 0.782367 -0.227877 0.05449 0.77796 -0.101515 0.197618 1.035289 -0.243881 0.08404 1.026991 -0.314256 0.256175 1.235404 -0.069247 -0.010677 0.795524 -0.392347 0.182686 1.200724 -0.103348 0.050548 1.05111 -0.329574 0.220371 1.236951 0.190291 -0.098555 0.75313 0.20527 0.115795 0.746186 0.241359 -0.062355 0.969687 0.264732 0.115544 0.949823 0.471454 0.066062 1.105743 0.045892 0.055427 0.772049 0.471049 0.172467 1.08085 0.14047 0.045506 0.979962 0.454437 0.118189 1.107406 -0.085869 -0.193134 0.810891 0.129076 -0.193441 0.806816 -0.060107 -0.209413 1.053942 0.121509 -0.223806 1.047336 0.039779 -0.409069 1.245825 0.057466 -0.037422 0.822587 0.148701 -0.398347 1.220755 0.041473 -0.102916 1.068364 0.090861 -0.386883 1.248021 0.193527 0.080969 0.838682 0.036467 0.227675 0.833318 0.188218 0.127675 1.069246 0.064181 0.258827 1.051088 0.168162 0.377803 1.233641 -0.017367 0.065065 0.852709 0.118558 0.441429 1.186518 0.040892 0.119478 1.083555 -0.195032 0.073293 0.868317 -0.180467 -0.14119 0.866348 -0.216503 0.04053 1.097337 -0.216207 -0.140006 1.082704 -0.385596 -0.121196 1.268556 -0.029329 -0.058709 0.874703 -0.377919 -0.22445 1.236603 -0.101595 -0.05305 1.103013 -0.368868 -0.159187 1.269719 0.09081 -0.18572 0.895194 0.226187 -0.018801 0.889678 0.130326 -0.169584 1.087513 0.252882 -0.036864 1.071399 0.362025 -0.160363 1.220467 0.0602 0.02357 0.908895 0.427351 -0.083635 1.183351 0.112167 -0.023203 1.101701 0.370991 -0.133591 1.221415 -0.177522 -0.100061 0.952425 -0.001672 -0.223713 0.950186 -0.165332 -0.124051 1.201317 -0.025455 -0.240715 1.19985 -0.16581 -0.297259 1.400904 0.02931 -0.054444 0.960488 -0.072608 -0.341103 1.360096 -0.02044 -0.094344 1.211244 -0.124861 -0.306154 1.402569 0.196389 -0.048677 0.981433 0.152132 0.161699 0.979947 0.199627 -0.022253 1.195838 0.176993 0.158453 1.195528 0.331098 0.118949 1.374184 0.013859 0.058953 0.986417 0.309664 0.222253 1.342009 0.073093 0.054809 1.202102 0.312196 0.145243 1.375018 0.060963 0.195888 0.922763 -0.151881 0.166142 0.917217 0.029053 0.219394 1.196917 -0.152696 0.208081 1.18821 -0.11264 0.403922 1.41021 -0.059466 0.022159 0.938352 -0.217922 0.384818 1.374881 -0.056943 0.100375 1.21484 -0.148312 0.38216 1.412225 -0.194983 0.02494 1.094343 -0.121897 -0.177238 1.093564 -0.193891 0.002549 1.346093 -0.146234 -0.173257 1.346202 -0.290037 -0.149771 1.554673 0.000769 -0.056223 1.09875 -0.249248 -0.244263 1.511966 -0.057862 -0.056087 1.352809 -0.268105 -0.173236 1.555703 -0.108522 0.168914 1.008187 -0.2195 -0.015168 1.004211 -0.135147 0.156757 1.203615 -0.241662 0.009048 1.199915 -0.320779 0.105205 1.355615 -0.048882 -0.034137 1.019954 -0.362405 0.037805 1.314403 -0.095629 0.014338 1.214869 -0.32004 0.103132 1.357119 -0.036686 -0.196013 1.038032 0.170091 -0.137186 1.03656 -0.007354 -0.199566 1.296436 0.171328 -0.164474 1.296435 0.144663 -0.312778 1.505261 0.057943 -0.006404 1.042588 0.222132 -0.258263 1.459447 0.06071 -0.068167 1.304106 0.163122 -0.298286 1.506041 0.159992 0.116271 1.063561 -0.031437 0.213933 1.05755 0.149634 0.180427 1.350498 -0.005432 0.271362 1.328078 0.100193 0.436273 1.548298 -0.038245 0.043229 1.080567 0.008662 0.482002 1.505522 0.010584 0.130158 1.36114 0.063609 0.441146 1.549909 0.12367 -0.150879 1.121013 0.207949 0.046856 1.117024 0.140955 -0.131833 1.327508 0.22521 0.029708 1.321096 0.32069 -0.063283 1.49312 0.036393 0.041889 1.133224 0.366586 0.017654 1.449042 0.082803 0.003166 1.34304 0.320636 -0.054862 1.493464 0.013535 0.193253 1.149262 -0.182957 0.106114 1.145255 -0.017732 0.196386 1.37515 -0.1897 0.136386 1.371141 -0.142254 0.28979 1.549064 -0.054104 -0.007248 1.161505 -0.21965 0.215169 1.516942 -0.067193 0.057519 1.389364 -0.183704 0.246678 1.551913 -0.141284 -0.130323 1.177557 0.061507 -0.200301 1.17189 -0.109897 -0.15876 1.358525 0.056372 -0.228567 1.342885 -0.042001 -0.371394 1.481347 0.044581 -0.030164 1.191292 0.057898 -0.399709 1.452866 0.021267 -0.091832 1.372614 0.00346 -0.370032 1.482837 0.190557 -0.001775 1.207166 0.090634 0.187124 1.205862 0.180372 0.013187 1.395299 0.108262 0.17941 1.391102 0.265353 0.185856 1.551456 -0.013335 0.05107 1.212659 0.217318 0.280837 1.522986 0.03904 0.052227 1.40479 0.237193 0.208223 1.552394 -0.136276 0.130669 1.234439 -0.191449 -0.074786 1.22857 -0.15701 0.08879 1.42761 -0.214907 -0.080149 1.410871 -0.361307 0.034095 1.558781 -0.024242 -0.04628 1.247272 -0.375025 -0.07316 1.534948 -0.081613 -0.036577 1.440895 -0.35416 -0.028363 1.560829 -0.182734 -0.020823 1.320886 -0.05959 -0.191105 1.320686 -0.17272 -0.038715 1.511273 -0.080565 -0.191061 1.51182 -0.205588 -0.21474 1.670949 0.023102 -0.044303 1.323187 -0.153913 -0.291466 1.635317 -0.029032 -0.057092 1.515589 -0.19372 -0.221825 1.671266 -0.098139 -0.14988 1.403992 0.108982 -0.160938 1.3991 -0.060151 -0.162988 1.575653 0.112968 -0.183895 1.561355 0.092932 -0.32853 1.695162 0.047871 -0.007084 1.415547 0.17558 -0.34454 1.65823 0.043937 -0.065655 1.58803 0.098659 -0.326676 1.695345 -0.031354 0.177886 1.375085 -0.191091 0.044179 1.370572 -0.05318 0.171283 1.55091 -0.19684 0.068653 1.543573 -0.241329 0.213191 1.685816 -0.040924 -0.026895 1.388308 -0.321551 0.1505 1.653366 -0.0624 0.029093 1.565508 -0.253406 0.18909 1.686855 0.146119 -0.109047 1.34914 0.17208 0.098586 1.34938 0.141754 -0.092442 1.552558 0.177254 0.081427 1.549574 0.304489 0.014959 1.727659 0.01215 0.047838 1.35097 0.329706 0.112611 1.694879 0.050033 0.018231 1.559641 0.299096 0.032974 1.728053 0.172728 0.041706 1.432884 0.029485 0.190527 1.429649 0.155388 0.073212 1.605482 0.043581 0.206261 1.592938 0.199431 0.27515 1.730456 -0.03073 0.036617 1.440812 0.119302 0.341581 1.709608 0.013346 0.072623 1.615342 0.146418 0.300235 1.731999 -0.153226 0.087135 1.462995 -0.150059 -0.118511 1.460982 -0.139721 0.041115 1.666863 -0.150312 -0.131012 1.652293 -0.276966 -0.074206 1.82108 -0.001348 -0.047254 1.464059 -0.281093 -0.174372 1.784245 -0.034963 -0.054951 1.669538 -0.264557 -0.104957 1.821981 0.051547 -0.167024 1.489312 0.188893 -0.015116 1.487605 0.069781 -0.154857 1.733028 0.195796 -0.035464 1.728134 0.267904 -0.190903 1.935785 0.032354 0.033884 1.495625 0.336592 -0.114549 1.905114 0.058678 -0.014789 1.743862 0.280053 -0.153486 1.936927 0.157845 -0.065127 1.57501 0.126653 0.13458 1.574217 0.151899 -0.032768 1.775022 0.137558 0.136831 1.763766 0.300495 0.090048 1.931034 -0.008317 0.044883 1.576733 0.268085 0.191514 1.911699 0.037367 0.046368 1.781446 0.265938 0.150229 1.932563 0.076139 0.155823 1.518356 -0.127034 0.1385 1.518341 0.055996 0.156413 1.73922 -0.116766 0.155908 1.740125 -0.058238 0.280983 1.924912 -0.046257 -0.003648 1.520429 -0.155708 0.268183 1.888875 -0.031098 0.046078 1.74378 -0.085833 0.266179 1.925736 -0.160893 -0.061001 1.546541 -0.001183 -0.186341 1.546155 -0.147946 -0.089546 1.765319 -0.021179 -0.203823 1.753037 -0.140998 -0.302837 1.935129 0.036509 -0.028 1.548871 -0.062819 -0.362503 1.906098 -0.009596 -0.068088 1.768793 -0.101236 -0.315854 1.936151 -0.070011 0.154323 1.602811 -0.182909 -0.01226 1.602769 -0.081817 0.139565 1.78281 -0.188718 0.00674 1.783473 -0.245881 0.126608 1.933435 -0.023718 -0.038593 1.604726 -0.295427 0.044027 1.90282 -0.051277 0.004256 1.786793 -0.25154 0.097315 1.934112 -0.1589 0.041849 1.685327 -0.101843 -0.145242 1.680192 -0.161604 0.000423 1.88938 -0.124068 -0.159552 1.873115 -0.275831 -0.172832 2.033207 0.014661 -0.041535 1.697141 -0.250391 -0.261381 1.993816 -0.041939 -0.060565 1.902732 -0.259529 -0.193662 2.034152 0.146818 0.078506 1.656821 -0.025046 0.178026 1.652689 0.132694 0.096105 1.830535 -0.006037 0.191438 1.82395 0.117346 0.271783 1.965336 -0.039769 0.020267 1.669056 0.031289 0.318619 1.935917 0.002331 0.058005 1.844653 0.082809 0.275801 1.966708 0.086902 -0.136986 1.715021 0.171748 0.036012 1.714711 0.090719 -0.112414 1.868544 0.174165 0.028002 1.867371 0.220972 -0.080245 2.001243 0.017311 0.03976 1.717156 0.267756 0.004401 1.972861 0.044153 0.01143 1.875767 0.22184 -0.049139 2.001754 -0.126727 -0.09439 1.770903 0.046243 -0.164718 1.770826 -0.103021 -0.093756 1.948068 0.038285 -0.165029 1.948142 -0.064166 -0.216352 2.102004 0.039431 -0.015163 1.772939 0.02102 -0.256475 2.070234 0.0136 -0.040158 1.955254 -0.034041 -0.215149 2.102655 0.155075 -0.016704 1.798576 0.077686 0.149896 1.795186 0.131973 0.019559 1.936702 0.077616 0.163576 1.924743 0.209839 0.170132 2.033994 -0.017559 0.037886 1.804531 0.17622 0.252987 2.008501 0.011779 0.060113 1.942475 0.188473 0.190656 2.034773 -0.101078 0.116127 1.825957 -0.157279 -0.055664 1.824258 -0.105644 0.095794 1.978311 -0.164674 -0.045613 1.975389 -0.234263 0.049388 2.107659 -0.013494 -0.03897 1.831744 -0.269387 -0.035755 2.082303 -0.04653 -0.013066 1.987895 -0.231034 0.02038 2.108367 0.105234 0.107098 1.883223 -0.064265 0.149727 1.881522 0.064835 0.106365 2.045298 -0.074124 0.152944 2.03353 0.000224 0.240687 2.167519 -0.038483 0.011989 1.884169 -0.081025 0.265685 2.137502 -0.037026 0.041454 2.047272 -0.02775 0.237303 2.168256 -0.148111 -0.007131 1.909979 -0.054844 -0.151429 1.909274 -0.132132 -0.022258 2.044319 -0.063118 -0.150516 2.042806 -0.168501 -0.137915 2.159199 0.018925 -0.035131 1.912928 -0.125138 -0.212407 2.134207 -0.01614 -0.043591 2.05137 -0.145126 -0.152865 2.159707 0.112281 -0.094081 1.937995 0.141881 0.072119 1.93613 0.112456 -0.065664 2.039434 0.147433 0.072525 2.031605 0.177065 -0.015527 2.133163 0.011075 0.037996 1.941161 0.208831 0.069041 2.116478 0.044306 0.027909 2.0473 0.170939 0.010989 2.134014
            ]
          }
          texCoord TextureCoordinate {
            point [
              0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0.0121 0.5, 0.0121 0.5, 1 0, 1.0117 0.5, 0.0121 0.5, 1.0117 0.5, -0.2166 1, 1 0, 2 0, 1.0117 0.5, -0.2166 1, 1.0117 0.5, 0.7832 1, 1.0117 0.5, 2 0, 2.0117 0.5, 1.0117 0.5, 2.0117 0.5, 0.7832 1, 2 0, 3 0, 2.0117 0.5, 0.7832 1, 2.0117 0.5, 1.7832 1, 2.0117 0.5, 3 0, 3.0117 0.5, 2.0117 0.5, 3.0117 0.5, 1.7832 1, 1.7832 1, 3.0117 0.5, 2.7832 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0.0068 0.5, 0.0068 0.5, 1 0, 1.0059 0.5, 0.0068 0.5, 1.0059 0.5, -0.1117 1, 1 0, 2 0, 1.0059 0.5, -0.1117 1, 1.0059 0.5, 0.8882 1, 1.0059 0.5, 2 0, 2.0059 0.5, 1.0059 0.5, 2.0059 0.5, 0.8882 1, 2 0, 3 0, 2.0059 0.5, 0.8882 1, 2.0059 0.5, 1.8877 1, 2.0059 0.5, 3 0, 3.0059 0.5, 2.0059 0.5, 3.0059 0.5, 1.8877 1, 1.8877 1, 3.0059 0.5, 2.8867 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.186 1, 1 0, 2 0, 1 0.5, -0.186 1, 1 0.5, 0.8135 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8135 1, 2 0, 3 0, 2 0.5, 0.8135 1, 2 0.5, 1.8135 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.8135 1, 1.8135 1, 3 0.5, 2.8125 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0997 1, 1 0, 2 0, 1 0.5, -0.0997 1, 1 0.5, 0.8999 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8999 1, 2 0, 3 0, 2 0.5, 0.8999 1, 2 0.5, 1.8994 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.8994 1, 1.8994 1, 3 0.5, 2.8984 1, 0 0, 1 0, 0.0001 0.5, 0.0001 0.5, 1 0, 1 0.5, 0.0001 0.5, 1 0.5, -0.0102 1, 1 0, 2 0, 1 0.5, -0.0102 1, 1 0.5, 0.9897 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9897 1, 2 0, 3 0, 2 0.5, 0.9897 1, 2 0.5, 1.9893 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9893 1, 1.9893 1, 3 0.5, 2.9883 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.1384 1, 1 0, 2 0, 1 0.5, 0.1384 1, 1 0.5, 1.1377 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.1377 1, 2 0, 3 0, 2 0.5, 1.1377 1, 2 0.5, 2.1367 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.1367 1, 2.1367 1, 3 0.5, 3.1367 1, 0 0, 1 0, 0.0005 0.5, 0.0005 0.5, 1 0, 1 0.5, 0.0005 0.5, 1 0.5, -0.0392 1, 1 0, 2 0, 1 0.5, -0.0392 1, 1 0.5, 0.9604 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9604 1, 2 0, 3 0, 2 0.5, 0.9604 1, 2 0.5, 1.96 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.96 1, 1.96 1, 3 0.5, 2.959 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.1118 1, 1 0, 2 0, 1 0.5, -0.1118 1, 1 0.5, 0.8877 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8877 1, 2 0, 3 0, 2 0.5, 0.8877 1, 2 0.5, 1.8877 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.8877 1, 1.8877 1, 3 0.5, 2.8867 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0747 1, 1 0, 2 0, 1 0.5, 0.0747 1, 1 0.5, 1.0742 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.0742 1, 2 0, 3 0, 2 0.5, 1.0742 1, 2 0.5, 2.0742 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.0742 1, 2.0742 1, 3 0.5, 3.0742 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0309 1, 1 0, 2 0, 1 0.5, -0.0309 1, 1 0.5, 0.9688 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9688 1, 2 0, 3 0, 2 0.5, 0.9688 1, 2 0.5, 1.9688 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9688 1, 1.9688 1, 3 0.5, 2.9688 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.3013 1, 1 0, 2 0, 1 0.5, 0.3013 1, 1 0.5, 1.3008 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.3008 1, 2 0, 3 0, 2 0.5, 1.3008 1, 2 0.5, 2.3008 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.3008 1, 2.3008 1, 3 0.5, 3.3008 1, 0 0, 1 0, -0.0007 0.5, -0.0007 0.5, 1 0, 0.999 0.5, -0.0007 0.5, 0.999 0.5, 0.0516 1, 1 0, 2 0, 0.999 0.5, 0.0516 1, 0.999 0.5, 1.0508 1, 0.999 0.5, 2 0, 1.999 0.5, 0.999 0.5, 1.999 0.5, 1.0508 1, 2 0, 3 0, 1.999 0.5, 1.0508 1, 1.999 0.5, 2.0508 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0508 1, 2.0508 1, 2.998 0.5, 3.0508 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0892 1, 1 0, 2 0, 1 0.5, -0.0892 1, 1 0.5, 0.9106 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9106 1, 2 0, 3 0, 2 0.5, 0.9106 1, 2 0.5, 1.9102 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9102 1, 1.9102 1, 3 0.5, 2.9102 1, 0 0, 1 0, -0.0004 0.5, -0.0004 0.5, 1 0, 0.9995 0.5, -0.0004 0.5, 0.9995 0.5, 0.0723 1, 1 0, 2 0, 0.9995 0.5, 0.0723 1, 0.9995 0.5, 1.0723 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0723 1, 2 0, 3 0, 1.999 0.5, 1.0723 1, 1.999 0.5, 2.0723 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0723 1, 2.0723 1, 2.998 0.5, 3.0723 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.1254 1, 1 0, 2 0, 1 0.5, 0.1254 1, 1 0.5, 1.125 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.125 1, 2 0, 3 0, 2 0.5, 1.125 1, 2 0.5, 2.125 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.125 1, 2.125 1, 3 0.5, 3.125 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0551 1, 1 0, 2 0, 1 0.5, -0.0551 1, 1 0.5, 0.9448 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9448 1, 2 0, 3 0, 2 0.5, 0.9448 1, 2 0.5, 1.9443 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9443 1, 1.9443 1, 3 0.5, 2.9434 1, 0 0, 1 0, 0.0003 0.5, 0.0003 0.5, 1 0, 1 0.5, 0.0003 0.5, 1 0.5, -0.0254 1, 1 0, 2 0, 1 0.5, -0.0254 1, 1 0.5, 0.9746 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9746 1, 2 0, 3 0, 2 0.5, 0.9746 1, 2 0.5, 1.9746 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9746 1, 1.9746 1, 3 0.5, 2.9746 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.1271 1, 1 0, 2 0, 1 0.5, -0.1271 1, 1 0.5, 0.8726 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8726 1, 2 0, 3 0, 2 0.5, 0.8726 1, 2 0.5, 1.8721 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.8721 1, 1.8721 1, 3 0.5, 2.8711 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0869 1, 1 0, 2 0, 1 0.5, -0.0869 1, 1 0.5, 0.9126 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9126 1, 2 0, 3 0, 2 0.5, 0.9126 1, 2 0.5, 1.9121 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9121 1, 1.9121 1, 3 0.5, 2.9121 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0315 1, 1 0, 2 0, 1 0.5, 0.0315 1, 1 0.5, 1.0312 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.0312 1, 2 0, 3 0, 2 0.5, 1.0312 1, 2 0.5, 2.0312 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.0312 1, 2.0312 1, 3 0.5, 3.0312 1, 0 0, 1 0, -0.0003 0.5, -0.0003 0.5, 1 0, 0.9995 0.5, -0.0003 0.5, 0.9995 0.5, 0.0348 1, 1 0, 2 0, 0.9995 0.5, 0.0348 1, 0.9995 0.5, 1.0342 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0342 1, 2 0, 3 0, 1.999 0.5, 1.0342 1, 1.999 0.5, 2.0332 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0332 1, 2.0332 1, 2.998 0.5, 3.0332 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.1498 1, 1 0, 2 0, 1 0.5, 0.1498 1, 1 0.5, 1.1494 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.1494 1, 2 0, 3 0, 2 0.5, 1.1494 1, 2 0.5, 2.1484 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.1484 1, 2.1484 1, 3 0.5, 3.1484 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.1559 1, 1 0, 2 0, 1 0.5, 0.1559 1, 1 0.5, 1.1553 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.1553 1, 2 0, 3 0, 2 0.5, 1.1553 1, 2 0.5, 2.1543 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.1543 1, 2.1543 1, 3 0.5, 3.1543 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.2622 1, 1 0, 2 0, 1 0.5, -0.2622 1, 1 0.5, 0.7373 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.7373 1, 2 0, 3 0, 2 0.5, 0.7373 1, 2 0.5, 1.7373 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.7373 1, 0 0, 1 0, -0.0003 0.5, -0.0003 0.5, 1 0, 0.9995 0.5, -0.0003 0.5, 0.9995 0.5, 0.0555 1, 1 0, 2 0, 0.9995 0.5, 0.0555 1, 0.9995 0.5, 1.0547 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0547 1, 2 0, 3 0, 1.999 0.5, 1.0547 1, 1.999 0.5, 2.0547 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0547 1, 2.0547 1, 2.998 0.5, 3.0547 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0527 1, 1 0, 2 0, 1 0.5, -0.0527 1, 1 0.5, 0.9468 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9468 1, 2 0, 3 0, 2 0.5, 0.9468 1, 2 0.5, 1.9463 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9463 1, 1.9463 1, 3 0.5, 2.9453 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0581 1, 1 0, 2 0, 1 0.5, 0.0581 1, 1 0.5, 1.0576 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.0576 1, 2 0, 3 0, 2 0.5, 1.0576 1, 2 0.5, 2.0566 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.0566 1, 2.0566 1, 3 0.5, 3.0566 1, 0 0, 1 0, 0.0002 0.5, 0.0002 0.5, 1 0, 1 0.5, 0.0002 0.5, 1 0.5, -0.0193 1, 1 0, 2 0, 1 0.5, -0.0193 1, 1 0.5, 0.9805 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9805 1, 2 0, 3 0, 2 0.5, 0.9805 1, 2 0.5, 1.9805 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9805 1, 1.9805 1, 3 0.5, 2.9805 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 0.9995 0.5, 0 0.5, 0.9995 0.5, 0.0572 1, 1 0, 2 0, 0.9995 0.5, 0.0572 1, 0.9995 0.5, 1.0566 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0566 1, 2 0, 3 0, 1.999 0.5, 1.0566 1, 1.999 0.5, 2.0566 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0566 1, 2.0566 1, 2.998 0.5, 3.0566 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0213 1, 1 0, 2 0, 1 0.5, -0.0213 1, 1 0.5, 0.9785 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9785 1, 2 0, 3 0, 2 0.5, 0.9785 1, 2 0.5, 1.9785 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9785 1, 1.9785 1, 3 0.5, 2.9785 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.3035 1, 1 0, 2 0, 1 0.5, -0.3035 1, 1 0.5, 0.6963 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.6963 1, 2 0, 3 0, 2 0.5, 0.6963 1, 2 0.5, 1.6963 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.6963 1, 1.6963 1, 3 0.5, 2.6953 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0904 1, 1 0, 2 0, 1 0.5, -0.0904 1, 1 0.5, 0.9092 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9092 1, 2 0, 3 0, 2 0.5, 0.9092 1, 2 0.5, 1.9092 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9092 1, 1.9092 1, 3 0.5, 2.9082 1, 0 0, 1 0, -0.0002 0.5, -0.0002 0.5, 1 0, 0.9995 0.5, -0.0002 0.5, 0.9995 0.5, 0.0192 1, 1 0, 2 0, 0.9995 0.5, 0.0192 1, 0.9995 0.5, 1.0186 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0186 1, 2 0, 3 0, 1.999 0.5, 1.0186 1, 1.999 0.5, 2.0176 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0176 1, 2.0176 1, 2.998 0.5, 3.0176 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.2013 1, 1 0, 2 0, 1 0.5, -0.2013 1, 1 0.5, 0.7983 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.7983 1, 2 0, 3 0, 2 0.5, 0.7983 1, 2 0.5, 1.7979 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.7979 1, 1.7979 1, 3 0.5, 2.7969 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.183 1, 1 0, 2 0, 1 0.5, 0.183 1, 1 0.5, 1.1826 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.1826 1, 2 0, 3 0, 2 0.5, 1.1826 1, 2 0.5, 2.1816 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.1816 1, 2.1816 1, 3 0.5, 3.1816 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0856 1, 1 0, 2 0, 1 0.5, 0.0856 1, 1 0.5, 1.085 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.085 1, 2 0, 3 0, 2 0.5, 1.085 1, 2 0.5, 2.084 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.084 1, 2.084 1, 3 0.5, 3.084 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0124 1, 1 0, 2 0, 1 0.5, 0.0124 1, 1 0.5, 1.0117 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.0117 1, 2 0, 3 0, 2 0.5, 1.0117 1, 2 0.5, 2.0117 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.0117 1, 2.0117 1, 3 0.5, 3.0117 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.2059 1, 1 0, 2 0, 1 0.5, 0.2059 1, 1 0.5, 1.2051 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.2051 1, 2 0, 3 0, 2 0.5, 1.2051 1, 2 0.5, 2.2051 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.2051 1, 2.2051 1, 3 0.5, 3.2051 1, 0 0, 1 0, 0.0001 0.5, 0.0001 0.5, 1 0, 1 0.5, 0.0001 0.5, 1 0.5, -0.1605 1, 1 0, 2 0, 1 0.5, -0.1605 1, 1 0.5, 0.8394 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8394 1, 2 0, 3 0, 2 0.5, 0.8394 1, 2 0.5, 1.8389 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.8389 1, 1.8389 1, 3 0.5, 2.8379 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.2166 1, 1 0, 2 0, 1 0.5, -0.2166 1, 1 0.5, 0.7832 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.7832 1, 2 0, 3 0, 2 0.5, 0.7832 1, 2 0.5, 1.7832 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.7832 1, 1.7832 1, 3 0.5, 2.7832 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0563 1, 1 0, 2 0, 1 0.5, -0.0563 1, 1 0.5, 0.9434 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9434 1, 2 0, 3 0, 2 0.5, 0.9434 1, 2 0.5, 1.9434 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9434 1, 1.9434 1, 3 0.5, 2.9434 1, 0 0, 1 0, 0.0002 0.5, 0.0002 0.5, 1 0, 1 0.5, 0.0002 0.5, 1 0.5, -0.1223 1, 1 0, 2 0, 1 0.5, -0.1223 1, 1 0.5, 0.8774 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.8774 1, 2 0, 3 0, 2 0.5, 0.8774 1, 2 0.5, 1.877 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.877 1, 1.877 1, 3 0.5, 2.877 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.1907 1, 1 0, 2 0, 1 0.5, 0.1907 1, 1 0.5, 1.1904 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.1904 1, 2 0, 3 0, 2 0.5, 1.1904 1, 2 0.5, 2.1895 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.1895 1, 2.1895 1, 3 0.5, 3.1895 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, -0.0005 0.5, -0.0005 0.5, 1 0, 0.999 0.5, -0.0005 0.5, 0.999 0.5, 0.053 1, 1 0, 2 0, 0.999 0.5, 0.053 1, 0.999 0.5, 1.0527 1, 0.999 0.5, 2 0, 1.999 0.5, 0.999 0.5, 1.999 0.5, 1.0527 1, 2 0, 3 0, 1.999 0.5, 1.0527 1, 1.999 0.5, 2.0527 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0527 1, 2.0527 1, 2.998 0.5, 3.0527 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.2681 1, 1 0, 2 0, 1 0.5, 0.2681 1, 1 0.5, 1.2676 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.2676 1, 2 0, 3 0, 2 0.5, 1.2676 1, 2 0.5, 2.2676 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.2676 1, 2.2676 1, 3 0.5, 3.2676 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.014 1, 1 0, 2 0, 1 0.5, -0.014 1, 1 0.5, 0.9858 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9858 1, 2 0, 3 0, 2 0.5, 0.9858 1, 2 0.5, 1.9854 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9854 1, 1.9854 1, 3 0.5, 2.9844 1, 0 0, 1 0, -0.0003 0.5, -0.0003 0.5, 1 0, 0.9995 0.5, -0.0003 0.5, 0.9995 0.5, 0.0766 1, 1 0, 2 0, 0.9995 0.5, 0.0766 1, 0.9995 0.5, 1.0762 1, 0.9995 0.5, 2 0, 1.999 0.5, 0.9995 0.5, 1.999 0.5, 1.0762 1, 2 0, 3 0, 1.999 0.5, 1.0762 1, 1.999 0.5, 2.0762 1, 1.999 0.5, 3 0, 2.998 0.5, 1.999 0.5, 2.998 0.5, 2.0762 1, 2.0762 1, 2.998 0.5, 3.0762 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0234 1, 1 0, 2 0, 1 0.5, -0.0234 1, 1 0.5, 0.9766 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9766 1, 2 0, 3 0, 2 0.5, 0.9766 1, 2 0.5, 1.9766 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9766 1, 1.9766 1, 3 0.5, 2.9766 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, -0.0462 1, 1 0, 2 0, 1 0.5, -0.0462 1, 1 0.5, 0.9536 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 0.9536 1, 2 0, 3 0, 2 0.5, 0.9536 1, 2 0.5, 1.9531 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 1.9531 1, 1.9531 1, 3 0.5, 2.9531 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0.0244 1, 1 0, 2 0, 1 0.5, 0.0244 1, 1 0.5, 1.0234 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1.0234 1, 2 0, 3 0, 2 0.5, 1.0234 1, 2 0.5, 2.0234 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2.0234 1, 2.0234 1, 3 0.5, 3.0234 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1, 0 0, 1 0, 0 0.5, 0 0.5, 1 0, 1 0.5, 0 0.5, 1 0.5, 0 1, 1 0, 2 0, 1 0.5, 0 1, 1 0.5, 1 1, 1 0.5, 2 0, 2 0.5, 1 0.5, 2 0.5, 1 1, 2 0, 3 0, 2 0.5, 1 1, 2 0.5, 2 1, 2 0.5, 3 0, 3 0.5, 2 0.5, 3 0.5, 2 1, 2 1, 3 0.5, 3 1
            ]
          }
          coordIndex [
            0, 1, 2, -1, 2, 1, 3, -1, 2, 3, 4, -1, 1, 5, 3, -1, 4, 3, 6, -1, 3, 5, 7, -1, 3, 7, 6, -1, 5, 0, 7, -1, 6, 7, 8, -1, 7, 0, 2, -1, 7, 2, 8, -1, 8, 2, 4, -1, 9, 10, 11, -1, 11, 10, 12, -1, 11, 12, 13, -1, 10, 14, 12, -1, 13, 12, 15, -1, 12, 14, 16, -1, 12, 16, 15, -1, 14, 9, 16, -1, 15, 16, 17, -1, 16, 9, 11, -1, 16, 11, 17, -1, 17, 11, 13, -1, 18, 19, 20, -1, 20, 19, 21, -1, 20, 21, 22, -1, 19, 23, 21, -1, 22, 21, 24, -1, 21, 23, 25, -1, 21, 25, 24, -1, 23, 18, 25, -1, 24, 25, 26, -1, 25, 18, 20, -1, 25, 20, 26, -1, 26, 20, 22, -1, 27, 28, 29, -1, 29, 28, 30, -1, 29, 30, 31, -1, 28, 32, 30, -1, 31, 30, 33, -1, 30, 32, 34, -1, 30, 34, 33, -1, 32, 27, 34, -1, 33, 34, 35, -1, 34, 27, 29, -1, 34, 29, 35, -1, 35, 29, 31, -1, 36, 37, 38, -1, 38, 37, 39, -1, 38, 39, 40, -1, 37, 41, 39, -1, 40, 39, 42, -1, 39, 41, 43, -1, 39, 43, 42, -1, 41, 36, 43, -1, 42, 43, 44, -1, 43, 36, 38, -1, 43, 38, 44, -1, 44, 38, 40, -1, 45, 46, 47, -1, 47, 46, 48, -1, 47, 48, 49, -1, 46, 50, 48, -1, 49, 48, 51, -1, 48, 50, 52, -1, 48, 52, 51, -1, 50, 45, 52, -1, 51, 52, 53, -1, 52, 45, 47, -1, 52, 47, 53, -1, 53, 47, 49, -1, 54, 55, 56, -1, 56, 55, 57, -1, 56, 57, 58, -1, 55, 59, 57, -1, 58, 57, 60, -1, 57, 59, 61, -1, 57, 61, 60, -1, 59, 54, 61, -1, 60, 61, 62, -1, 61, 54, 56, -1, 61, 56, 62, -1, 62, 56, 58, -1, 63, 64, 65, -1, 65, 64, 66, -1, 65, 66, 67, -1, 64, 68, 66, -1, 67, 66, 69, -1, 66, 68, 70, -1, 66, 70, 69, -1, 68, 63, 70, -1, 69, 70, 71, -1, 70, 63, 65, -1, 70, 65, 71, -1, 71, 65, 67, -1, 72, 73, 74, -1, 74, 73, 75, -1, 74, 75, 76, -1, 73, 77, 75, -1, 76, 75, 78, -1, 75, 77, 79, -1, 75, 79, 78, -1, 77, 72, 79, -1, 78, 79, 80, -1, 79, 72, 74, -1, 79, 74, 80, -1, 80, 74, 76, -1, 81, 82, 83, -1, 83, 82, 84, -1, 83, 84, 85, -1, 82, 86, 84, -1, 85, 84, 87, -1, 84, 86, 88, -1, 84, 88, 87, -1, 86, 81, 88, -1, 87, 88, 89, -1, 88, 81, 83, -1, 88, 83, 89, -1, 89, 83, 85, -1, 90, 91, 92, -1, 92, 91, 93, -1, 92, 93, 94, -1, 91, 95, 93, -1, 94, 93, 96, -1, 93, 95, 97, -1, 93, 97, 96, -1, 95, 90, 97, -1, 96, 97, 98, -1, 97, 90, 92, -1, 97, 92, 98, -1, 98, 92, 94, -1, 99, 100, 101, -1, 101, 100, 102, -1, 101, 102, 103, -1, 100, 104, 102, -1, 103, 102, 105, -1, 102, 104, 106, -1, 102, 106, 105, -1, 104, 99, 106, -1, 105, 106, 107, -1, 106, 99, 101, -1, 106, 101, 107, -1, 107, 101, 103, -1, 108, 109, 110, -1, 110, 109, 111, -1, 110, 111, 112, -1, 109, 113, 111, -1, 112, 111, 114, -1, 111, 113, 115, -1, 111, 115, 114, -1, 113, 108, 115, -1, 114, 115, 116, -1, 115, 108, 110, -1, 115, 110, 116, -1, 116, 110, 112, -1, 117, 118, 119, -1, 119, 118, 120, -1, 119, 120, 121, -1, 118, 122, 120, -1, 121, 120, 123, -1, 120, 122, 124, -1, 120, 124, 123, -1, 122, 117, 124, -1, 123, 124, 125, -1, 124, 117, 119, -1, 124, 119, 125, -1, 125, 119, 121, -1, 126, 127, 128, -1, 128, 127, 129, -1, 128, 129, 130, -1, 127, 131, 129, -1, 130, 129, 132, -1, 129, 131, 133, -1, 129, 133, 132, -1, 131, 126, 133, -1, 132, 133, 134, -1, 133, 126, 128, -1, 133, 128, 134, -1, 134, 128, 130, -1, 135, 136, 137, -1, 137, 136, 138, -1, 137, 138, 139, -1, 136, 140, 138, -1, 139, 138, 141, -1, 138, 140, 142, -1, 138, 142, 141, -1, 140, 135, 142, -1, 141, 142, 143, -1, 142, 135, 137, -1, 142, 137, 143, -1, 143, 137, 139, -1, 144, 145, 146, -1, 146, 145, 147, -1, 146, 147, 148, -1, 145, 149, 147, -1, 148, 147, 150, -1, 147, 149, 151, -1, 147, 151, 150, -1, 149, 144, 151, -1, 150, 151, 152, -1, 151, 144, 146, -1, 151, 146, 152, -1, 152, 146, 148, -1, 153, 154, 155, -1, 155, 154, 156, -1, 155, 156, 157, -1, 154, 158, 156, -1, 157, 156, 159, -1, 156, 158, 160, -1, 156, 160, 159, -1, 158, 153, 160, -1, 159, 160, 161, -1, 160, 153, 155, -1, 160, 155, 161, -1, 161, 155, 157, -1, 162, 163, 164, -1, 164, 163, 165, -1, 164, 165, 166, -1, 163, 167, 165, -1, 166, 165, 168, -1, 165, 167, 169, -1, 165, 169, 168, -1, 167, 162, 169, -1, 168, 169, 170, -1, 169, 162, 164, -1, 169, 164, 170, -1, 170, 164, 166, -1, 171, 172, 173, -1, 173, 172, 174, -1, 173, 174, 175, -1, 172, 176, 174, -1, 175, 174, 177, -1, 174, 176, 178, -1, 174, 178, 177, -1, 176, 171, 178, -1, 177, 178, 179, -1, 178, 171, 173, -1, 178, 173, 179, -1, 179, 173, 175, -1, 180, 181, 182, -1, 182, 181, 183, -1, 182, 183, 184, -1, 181, 185, 183, -1, 184, 183, 186, -1, 183, 185, 187, -1, 183, 187, 186, -1, 185, 180, 187, -1, 186, 187, 188, -1, 187, 180, 182, -1, 187, 182, 188, -1, 188, 182, 184, -1, 189, 190, 191, -1, 191, 190, 192, -1, 191, 192, 193, -1, 190, 194, 192, -1, 193, 192, 195, -1, 192, 194, 196, -1, 192, 196, 195, -1, 194, 189, 196, -1, 195, 196, 197, -1, 196, 189, 191, -1, 196, 191, 197, -1, 197, 191, 193, -1, 198, 199, 200, -1, 200, 199, 201, -1, 200, 201, 202, -1, 199, 203, 201, -1, 202, 201, 204, -1, 201, 203, 205, -1, 201, 205, 204, -1, 203, 198, 205, -1, 204, 205, 206, -1, 205, 198, 200, -1, 205, 200, 206, -1, 206, 200, 202, -1, 207, 208, 209, -1, 209, 208, 210, -1, 209, 210, 211, -1, 208, 212, 210, -1, 211, 210, 213, -1, 210, 212, 214, -1, 210, 214, 213, -1, 212, 207, 214, -1, 213, 214, 215, -1, 214, 207, 209, -1, 214, 209, 215, -1, 215, 209, 211, -1, 216, 217, 218, -1, 218, 217, 219, -1, 218, 219, 220, -1, 217, 221, 219, -1, 220, 219, 222, -1, 219, 221, 223, -1, 219, 223, 222, -1, 221, 216, 223, -1, 222, 223, 224, -1, 223, 216, 218, -1, 223, 218, 224, -1, 224, 218, 220, -1, 225, 226, 227, -1, 227, 226, 228, -1, 227, 228, 229, -1, 226, 230, 228, -1, 229, 228, 231, -1, 228, 230, 232, -1, 228, 232, 231, -1, 230, 225, 232, -1, 231, 232, 233, -1, 232, 225, 227, -1, 232, 227, 233, -1, 233, 227, 229, -1, 234, 235, 236, -1, 236, 235, 237, -1, 236, 237, 238, -1, 235, 239, 237, -1, 238, 237, 240, -1, 237, 239, 241, -1, 237, 241, 240, -1, 239, 234, 241, -1, 240, 241, 242, -1, 241, 234, 236, -1, 241, 236, 242, -1, 242, 236, 238, -1, 243, 244, 245, -1, 245, 244, 246, -1, 245, 246, 247, -1, 244, 248, 246, -1, 247, 246, 249, -1, 246, 248, 250, -1, 246, 250, 249, -1, 248, 243, 250, -1, 249, 250, 251, -1, 250, 243, 245, -1, 250, 245, 251, -1, 251, 245, 247, -1, 252, 253, 254, -1, 254, 253, 255, -1, 254, 255, 256, -1, 253, 257, 255, -1, 256, 255, 258, -1, 255, 257, 259, -1, 255, 259, 258, -1, 257, 252, 259, -1, 258, 259, 260, -1, 259, 252, 254, -1, 259, 254, 260, -1, 260, 254, 256, -1, 261, 262, 263, -1, 263, 262, 264, -1, 263, 264, 265, -1, 262, 266, 264, -1, 265, 264, 267, -1, 264, 266, 268, -1, 264, 268, 267, -1, 266, 261, 268, -1, 267, 268, 269, -1, 268, 261, 263, -1, 268, 263, 269, -1, 269, 263, 265, -1, 270, 271, 272, -1, 272, 271, 273, -1, 272, 273, 274, -1, 271, 275, 273, -1, 274, 273, 276, -1, 273, 275, 277, -1, 273, 277, 276, -1, 275, 270, 277, -1, 276, 277, 274, -1, 277, 270, 272, -1, 277, 272, 274, -1, 278, 279, 280, -1, 280, 279, 281, -1, 280, 281, 282, -1, 279, 283, 281, -1, 282, 281, 284, -1, 281, 283, 285, -1, 281, 285, 284, -1, 283, 278, 285, -1, 284, 285, 286, -1, 285, 278, 280, -1, 285, 280, 286, -1, 286, 280, 282, -1, 287, 288, 289, -1, 289, 288, 290, -1, 289, 290, 291, -1, 288, 292, 290, -1, 291, 290, 293, -1, 290, 292, 294, -1, 290, 294, 293, -1, 292, 287, 294, -1, 293, 294, 295, -1, 294, 287, 289, -1, 294, 289, 295, -1, 295, 289, 291, -1, 296, 297, 298, -1, 298, 297, 299, -1, 298, 299, 300, -1, 297, 301, 299, -1, 300, 299, 302, -1, 299, 301, 303, -1, 299, 303, 302, -1, 301, 296, 303, -1, 302, 303, 304, -1, 303, 296, 298, -1, 303, 298, 304, -1, 304, 298, 300, -1, 305, 306, 307, -1, 307, 306, 308, -1, 307, 308, 309, -1, 306, 310, 308, -1, 309, 308, 311, -1, 308, 310, 312, -1, 308, 312, 311, -1, 310, 305, 312, -1, 311, 312, 313, -1, 312, 305, 307, -1, 312, 307, 313, -1, 313, 307, 309, -1, 314, 315, 316, -1, 316, 315, 317, -1, 316, 317, 318, -1, 315, 319, 317, -1, 318, 317, 320, -1, 317, 319, 321, -1, 317, 321, 320, -1, 319, 314, 321, -1, 320, 321, 322, -1, 321, 314, 316, -1, 321, 316, 322, -1, 322, 316, 318, -1, 323, 324, 325, -1, 325, 324, 326, -1, 325, 326, 327, -1, 324, 328, 326, -1, 327, 326, 329, -1, 326, 328, 330, -1, 326, 330, 329, -1, 328, 323, 330, -1, 329, 330, 331, -1, 330, 323, 325, -1, 330, 325, 331, -1, 331, 325, 327, -1, 332, 333, 334, -1, 334, 333, 335, -1, 334, 335, 336, -1, 333, 337, 335, -1, 336, 335, 338, -1, 335, 337, 339, -1, 335, 339, 338, -1, 337, 332, 339, -1, 338, 339, 340, -1, 339, 332, 334, -1, 339, 334, 340, -1, 340, 334, 336, -1, 341, 342, 343, -1, 343, 342, 344, -1, 343, 344, 345, -1, 342, 346, 344, -1, 345, 344, 347, -1, 344, 346, 348, -1, 344, 348, 347, -1, 346, 341, 348, -1, 347, 348, 349, -1, 348, 341, 343, -1, 348, 343, 349, -1, 349, 343, 345, -1, 350, 351, 352, -1, 352, 351, 353, -1, 352, 353, 354, -1, 351, 355, 353, -1, 354, 353, 356, -1, 353, 355, 357, -1, 353, 357, 356, -1, 355, 350, 357, -1, 356, 357, 358, -1, 357, 350, 352, -1, 357, 352, 358, -1, 358, 352, 354, -1, 359, 360, 361, -1, 361, 360, 362, -1, 361, 362, 363, -1, 360, 364, 362, -1, 363, 362, 365, -1, 362, 364, 366, -1, 362, 366, 365, -1, 364, 359, 366, -1, 365, 366, 367, -1, 366, 359, 361, -1, 366, 361, 367, -1, 367, 361, 363, -1, 368, 369, 370, -1, 370, 369, 371, -1, 370, 371, 372, -1, 369, 373, 371, -1, 372, 371, 374, -1, 371, 373, 375, -1, 371, 375, 374, -1, 373, 368, 375, -1, 374, 375, 376, -1, 375, 368, 370, -1, 375, 370, 376, -1, 376, 370, 372, -1, 377, 378, 379, -1, 379, 378, 380, -1, 379, 380, 381, -1, 378, 382, 380, -1, 381, 380, 383, -1, 380, 382, 384, -1, 380, 384, 383, -1, 382, 377, 384, -1, 383, 384, 385, -1, 384, 377, 379, -1, 384, 379, 385, -1, 385, 379, 381, -1, 386, 387, 388, -1, 388, 387, 389, -1, 388, 389, 390, -1, 387, 391, 389, -1, 390, 389, 392, -1, 389, 391, 393, -1, 389, 393, 392, -1, 391, 386, 393, -1, 392, 393, 394, -1, 393, 386, 388, -1, 393, 388, 394, -1, 394, 388, 390, -1, 395, 396, 397, -1, 397, 396, 398, -1, 397, 398, 399, -1, 396, 400, 398, -1, 399, 398, 401, -1, 398, 400, 402, -1, 398, 402, 401, -1, 400, 395, 402, -1, 401, 402, 403, -1, 402, 395, 397, -1, 402, 397, 403, -1, 403, 397, 399, -1, 404, 405, 406, -1, 406, 405, 407, -1, 406, 407, 408, -1, 405, 409, 407, -1, 408, 407, 410, -1, 407, 409, 411, -1, 407, 411, 410, -1, 409, 404, 411, -1, 410, 411, 412, -1, 411, 404, 406, -1, 411, 406, 412, -1, 412, 406, 408, -1, 413, 414, 415, -1, 415, 414, 416, -1, 415, 416, 417, -1, 414, 418, 416, -1, 417, 416, 419, -1, 416, 418, 420, -1, 416, 420, 419, -1, 418, 413, 420, -1, 419, 420, 421, -1, 420, 413, 415, -1, 420, 415, 421, -1, 421, 415, 417, -1, 422, 423, 424, -1, 424, 423, 425, -1, 424, 425, 426, -1, 423, 427, 425, -1, 426, 425, 428, -1, 425, 427, 429, -1, 425, 429, 428, -1, 427, 422, 429, -1, 428, 429, 430, -1, 429, 422, 424, -1, 429, 424, 430, -1, 430, 424, 426, -1, 431, 432, 433, -1, 433, 432, 434, -1, 433, 434, 435, -1, 432, 436, 434, -1, 435, 434, 437, -1, 434, 436, 438, -1, 434, 438, 437, -1, 436, 431, 438, -1, 437, 438, 439, -1, 438, 431, 433, -1, 438, 433, 439, -1, 439, 433, 435, -1, 440, 441, 442, -1, 442, 441, 443, -1, 442, 443, 444, -1, 441, 445, 443, -1, 444, 443, 446, -1, 443, 445, 447, -1, 443, 447, 446, -1, 445, 440, 447, -1, 446, 447, 448, -1, 447, 440, 442, -1, 447, 442, 448, -1, 448, 442, 444, -1, 449, 450, 451, -1, 451, 450, 452, -1, 451, 452, 453, -1, 450, 454, 452, -1, 453, 452, 455, -1, 452, 454, 456, -1, 452, 456, 455, -1, 454, 449, 456, -1, 455, 456, 457, -1, 456, 449, 451, -1, 456, 451, 457, -1, 457, 451, 453, -1, 458, 459, 460, -1, 460, 459, 461, -1, 460, 461, 462, -1, 459, 463, 461, -1, 462, 461, 464, -1, 461, 463, 465, -1, 461, 465, 464, -1, 463, 458, 465, -1, 464, 465, 466, -1, 465, 458, 460, -1, 465, 460, 466, -1, 466, 460, 462, -1, 467, 468, 469, -1, 469, 468, 470, -1, 469, 470, 471, -1, 468, 472, 470, -1, 471, 470, 473, -1, 470, 472, 474, -1, 470, 474, 473, -1, 472, 467, 474, -1, 473, 474, 475, -1, 474, 467, 469, -1, 474, 469, 475, -1, 475, 469, 471, -1, 476, 477, 478, -1, 478, 477, 479, -1, 478, 479, 480, -1, 477, 481, 479, -1, 480, 479, 482, -1, 479, 481, 483, -1, 479, 483, 482, -1, 481, 476, 483, -1, 482, 483, 484, -1, 483, 476, 478, -1, 483, 478, 484, -1, 484, 478, 480, -1, 485, 486, 487, -1, 487, 486, 488, -1, 487, 488, 489, -1, 486, 490, 488, -1, 489, 488, 491, -1, 488, 490, 492, -1, 488, 492, 491, -1, 490, 485, 492, -1, 491, 492, 493, -1, 492, 485, 487, -1, 492, 487, 493, -1, 493, 487, 489, -1, 494, 495, 496, -1, 496, 495, 497, -1, 496, 497, 498, -1, 495, 499, 497, -1, 498, 497, 500, -1, 497, 499, 501, -1, 497, 501, 500, -1, 499, 494, 501, -1, 500, 501, 502, -1, 501, 494, 496, -1, 501, 496, 502, -1, 502, 496, 498, -1, 503, 504, 505, -1, 505, 504, 506, -1, 505, 506, 507, -1, 504, 508, 506, -1, 507, 506, 509, -1, 506, 508, 510, -1, 506, 510, 509, -1, 508, 503, 510, -1, 509, 510, 511, -1, 510, 503, 505, -1, 510, 505, 511, -1, 511, 505, 507, -1, 512, 513, 514, -1, 514, 513, 515, -1, 514, 515, 516, -1, 513, 517, 515, -1, 516, 515, 518, -1, 515, 517, 519, -1, 515, 519, 518, -1, 517, 512, 519, -1, 518, 519, 520, -1, 519, 512, 514, -1, 519, 514, 520, -1, 520, 514, 516, -1, 521, 522, 523, -1, 523, 522, 524, -1, 523, 524, 525, -1, 522, 526, 524, -1, 525, 524, 527, -1, 524, 526, 528, -1, 524, 528, 527, -1, 526, 521, 528, -1, 527, 528, 529, -1, 528, 521, 523, -1, 528, 523, 529, -1, 529, 523, 525, -1, 530, 531, 532, -1, 532, 531, 533, -1, 532, 533, 534, -1, 531, 535, 533, -1, 534, 533, 536, -1, 533, 535, 537, -1, 533, 537, 536, -1, 535, 530, 537, -1, 536, 537, 538, -1, 537, 530, 532, -1, 537, 532, 538, -1, 538, 532, 534, -1, 539, 540, 541, -1, 541, 540, 542, -1, 541, 542, 543, -1, 540, 544, 542, -1, 543, 542, 545, -1, 542, 544, 546, -1, 542, 546, 545, -1, 544, 539, 546, -1, 545, 546, 547, -1, 546, 539, 541, -1, 546, 541, 547, -1, 547, 541, 543, -1, 548, 549, 550, -1, 550, 549, 551, -1, 550, 551, 552, -1, 549, 553, 551, -1, 552, 551, 554, -1, 551, 553, 555, -1, 551, 555, 554, -1, 553, 548, 555, -1, 554, 555, 556, -1, 555, 548, 550, -1, 555, 550, 556, -1, 556, 550, 552, -1, 557, 558, 559, -1, 559, 558, 560, -1, 559, 560, 561, -1, 558, 562, 560, -1, 561, 560, 563, -1, 560, 562, 564, -1, 560, 564, 563, -1, 562, 557, 564, -1, 563, 564, 565, -1, 564, 557, 559, -1, 564, 559, 565, -1, 565, 559, 561, -1, 566, 567, 568, -1, 568, 567, 569, -1, 568, 569, 570, -1, 567, 571, 569, -1, 570, 569, 572, -1, 569, 571, 573, -1, 569, 573, 572, -1, 571, 566, 573, -1, 572, 573, 574, -1, 573, 566, 568, -1, 573, 568, 574, -1, 574, 568, 570, -1, 575, 576, 577, -1, 577, 576, 578, -1, 577, 578, 579, -1, 576, 580, 578, -1, 579, 578, 581, -1, 578, 580, 582, -1, 578, 582, 581, -1, 580, 575, 582, -1, 581, 582, 583, -1, 582, 575, 577, -1, 582, 577, 583, -1, 583, 577, 579, -1
          ]
          texCoordIndex [
            0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1, 60, 61, 62, -1, 63, 64, 65, -1, 66, 67, 68, -1, 69, 70, 71, -1, 72, 73, 74, -1, 75, 76, 77, -1, 78, 79, 80, -1, 81, 82, 83, -1, 84, 85, 86, -1, 87, 88, 89, -1, 90, 91, 92, -1, 93, 94, 95, -1, 96, 97, 98, -1, 99, 100, 101, -1, 102, 103, 104, -1, 105, 106, 107, -1, 108, 109, 110, -1, 111, 112, 113, -1, 114, 115, 116, -1, 117, 118, 119, -1, 120, 121, 122, -1, 123, 124, 125, -1, 126, 127, 128, -1, 129, 130, 131, -1, 132, 133, 134, -1, 135, 136, 137, -1, 138, 139, 140, -1, 141, 142, 143, -1, 144, 145, 146, -1, 147, 148, 149, -1, 150, 151, 152, -1, 153, 154, 155, -1, 156, 157, 158, -1, 159, 160, 161, -1, 162, 163, 164, -1, 165, 166, 167, -1, 168, 169, 170, -1, 171, 172, 173, -1, 174, 175, 176, -1, 177, 178, 179, -1, 180, 181, 182, -1, 183, 184, 185, -1, 186, 187, 188, -1, 189, 190, 191, -1, 192, 193, 194, -1, 195, 196, 197, -1, 198, 199, 200, -1, 201, 202, 203, -1, 204, 205, 206, -1, 207, 208, 209, -1, 210, 211, 212, -1, 213, 214, 215, -1, 216, 217, 218, -1, 219, 220, 221, -1, 222, 223, 224, -1, 225, 226, 227, -1, 228, 229, 230, -1, 231, 232, 233, -1, 234, 235, 236, -1, 237, 238, 239, -1, 240, 241, 242, -1, 243, 244, 245, -1, 246, 247, 248, -1, 249, 250, 251, -1, 252, 253, 254, -1, 255, 256, 257, -1, 258, 259, 260, -1, 261, 262, 263, -1, 264, 265, 266, -1, 267, 268, 269, -1, 270, 271, 272, -1, 273, 274, 275, -1, 276, 277, 278, -1, 279, 280, 281, -1, 282, 283, 284, -1, 285, 286, 287, -1, 288, 289, 290, -1, 291, 292, 293, -1, 294, 295, 296, -1, 297, 298, 299, -1, 300, 301, 302, -1, 303, 304, 305, -1, 306, 307, 308, -1, 309, 310, 311, -1, 312, 313, 314, -1, 315, 316, 317, -1, 318, 319, 320, -1, 321, 322, 323, -1, 324, 325, 326, -1, 327, 328, 329, -1, 330, 331, 332, -1, 333, 334, 335, -1, 336, 337, 338, -1, 339, 340, 341, -1, 342, 343, 344, -1, 345, 346, 347, -1, 348, 349, 350, -1, 351, 352, 353, -1, 354, 355, 356, -1, 357, 358, 359, -1, 360, 361, 362, -1, 363, 364, 365, -1, 366, 367, 368, -1, 369, 370, 371, -1, 372, 373, 374, -1, 375, 376, 377, -1, 378, 379, 380, -1, 381, 382, 383, -1, 384, 385, 386, -1, 387, 388, 389, -1, 390, 391, 392, -1, 393, 394, 395, -1, 396, 397, 398, -1, 399, 400, 401, -1, 402, 403, 404, -1, 405, 406, 407, -1, 408, 409, 410, -1, 411, 412, 413, -1, 414, 415, 416, -1, 417, 418, 419, -1, 420, 421, 422, -1, 423, 424, 425, -1, 426, 427, 428, -1, 429, 430, 431, -1, 432, 433, 434, -1, 435, 436, 437, -1, 438, 439, 440, -1, 441, 442, 443, -1, 444, 445, 446, -1, 447, 448, 449, -1, 450, 451, 452, -1, 453, 454, 455, -1, 456, 457, 458, -1, 459, 460, 461, -1, 462, 463, 464, -1, 465, 466, 467, -1, 468, 469, 470, -1, 471, 472, 473, -1, 474, 475, 476, -1, 477, 478, 479, -1, 480, 481, 482, -1, 483, 484, 485, -1, 486, 487, 488, -1, 489, 490, 491, -1, 492, 493, 494, -1, 495, 496, 497, -1, 498, 499, 500, -1, 501, 502, 503, -1, 504, 505, 506, -1, 507, 508, 509, -1, 510, 511, 512, -1, 513, 514, 515, -1, 516, 517, 518, -1, 519, 520, 521, -1, 522, 523, 524, -1, 525, 526, 527, -1, 528, 529, 530, -1, 531, 532, 533, -1, 534, 535, 536, -1, 537, 538, 539, -1, 540, 541, 542, -1, 543, 544, 545, -1, 546, 547, 548, -1, 549, 550, 551, -1, 552, 553, 554, -1, 555, 556, 557, -1, 558, 559, 560, -1, 561, 562, 563, -1, 564, 565, 566, -1, 567, 568, 569, -1, 570, 571, 572, -1, 573, 574, 575, -1, 576, 577, 578, -1, 579, 580, 581, -1, 582, 583, 584, -1, 585, 586, 587, -1, 588, 589, 590, -1, 591, 592, 593, -1, 594, 595, 596, -1, 597, 598, 599, -1, 600, 601, 602, -1, 603, 604, 605, -1, 606, 607, 608, -1, 609, 610, 611, -1, 612, 613, 614, -1, 615, 616, 617, -1, 618, 619, 620, -1, 621, 622, 623, -1, 624, 625, 626, -1, 627, 628, 629, -1, 630, 631, 632, -1, 633, 634, 635, -1, 636, 637, 638, -1, 639, 640, 641, -1, 642, 643, 644, -1, 645, 646, 647, -1, 648, 649, 650, -1, 651, 652, 653, -1, 654, 655, 656, -1, 657, 658, 659, -1, 660, 661, 662, -1, 663, 664, 665, -1, 666, 667, 668, -1, 669, 670, 671, -1, 672, 673, 674, -1, 675, 676, 677, -1, 678, 679, 680, -1, 681, 682, 683, -1, 684, 685, 686, -1, 687, 688, 689, -1, 690, 691, 692, -1, 693, 694, 695, -1, 696, 697, 698, -1, 699, 700, 701, -1, 702, 703, 704, -1, 705, 706, 707, -1, 708, 709, 710, -1, 711, 712, 713, -1, 714, 715, 716, -1, 717, 718, 719, -1, 720, 721, 722, -1, 723, 724, 725, -1, 726, 727, 728, -1, 729, 730, 731, -1, 732, 733, 734, -1, 735, 736, 737, -1, 738, 739, 740, -1, 741, 742, 743, -1, 744, 745, 746, -1, 747, 748, 749, -1, 750, 751, 752, -1, 753, 754, 755, -1, 756, 757, 758, -1, 759, 760, 761, -1, 762, 763, 764, -1, 765, 766, 767, -1, 768, 769, 770, -1, 771, 772, 773, -1, 774, 775, 776, -1, 777, 778, 779, -1, 780, 781, 782, -1, 783, 784, 785, -1, 786, 787, 788, -1, 789, 790, 791, -1, 792, 793, 794, -1, 795, 796, 797, -1, 798, 799, 800, -1, 801, 802, 803, -1, 804, 805, 806, -1, 807, 808, 809, -1, 810, 811, 812, -1, 813, 814, 815, -1, 816, 817, 818, -1, 819, 820, 821, -1, 822, 823, 824, -1, 825, 826, 827, -1, 828, 829, 830, -1, 831, 832, 833, -1, 834, 835, 836, -1, 837, 838, 839, -1, 840, 841, 842, -1, 843, 844, 845, -1, 846, 847, 848, -1, 849, 850, 851, -1, 852, 853, 854, -1, 855, 856, 857, -1, 858, 859, 860, -1, 861, 862, 863, -1, 864, 865, 866, -1, 867, 868, 869, -1, 870, 871, 872, -1, 873, 874, 875, -1, 876, 877, 878, -1, 879, 880, 881, -1, 882, 883, 884, -1, 885, 886, 887, -1, 888, 889, 890, -1, 891, 892, 893, -1, 894, 895, 896, -1, 897, 898, 899, -1, 900, 901, 902, -1, 903, 904, 905, -1, 906, 907, 908, -1, 909, 910, 911, -1, 912, 913, 914, -1, 915, 916, 917, -1, 918, 919, 920, -1, 921, 922, 923, -1, 924, 925, 926, -1, 927, 928, 929, -1, 930, 931, 932, -1, 933, 934, 935, -1, 936, 937, 938, -1, 939, 940, 941, -1, 942, 943, 944, -1, 945, 946, 947, -1, 948, 949, 950, -1, 951, 952, 953, -1, 954, 955, 956, -1, 957, 958, 959, -1, 960, 961, 962, -1, 963, 964, 965, -1, 966, 967, 968, -1, 969, 970, 971, -1, 972, 973, 974, -1, 975, 976, 977, -1, 978, 979, 980, -1, 981, 982, 983, -1, 984, 985, 986, -1, 987, 988, 989, -1, 990, 991, 992, -1, 993, 994, 995, -1, 996, 997, 998, -1, 999, 1000, 1001, -1, 1002, 1003, 1004, -1, 1005, 1006, 1007, -1, 1008, 1009, 1010, -1, 1011, 1012, 1013, -1, 1014, 1015, 1016, -1, 1017, 1018, 1019, -1, 1020, 1021, 1022, -1, 1023, 1024, 1025, -1, 1026, 1027, 1028, -1, 1029, 1030, 1031, -1, 1032, 1033, 1034, -1, 1035, 1036, 1037, -1, 1038, 1039, 1040, -1, 1041, 1042, 1043, -1, 1044, 1045, 1046, -1, 1047, 1048, 1049, -1, 1050, 1051, 1052, -1, 1053, 1054, 1055, -1, 1056, 1057, 1058, -1, 1059, 1060, 1061, -1, 1062, 1063, 1064, -1, 1065, 1066, 1067, -1, 1068, 1069, 1070, -1, 1071, 1072, 1073, -1, 1074, 1075, 1076, -1, 1077, 1078, 1079, -1, 1080, 1081, 1082, -1, 1083, 1084, 1085, -1, 1086, 1087, 1088, -1, 1089, 1090, 1091, -1, 1092, 1093, 1094, -1, 1095, 1096, 1097, -1, 1098, 1099, 1100, -1, 1101, 1102, 1103, -1, 1104, 1105, 1106, -1, 1107, 1108, 1109, -1, 1110, 1111, 1112, -1, 1113, 1114, 1115, -1, 1116, 1117, 1118, -1, 1119, 1120, 1121, -1, 1122, 1123, 1124, -1, 1125, 1126, 1127, -1, 1128, 1129, 1130, -1, 1131, 1132, 1133, -1, 1134, 1135, 1136, -1, 1137, 1138, 1139, -1, 1140, 1141, 1142, -1, 1143, 1144, 1145, -1, 1146, 1147, 1148, -1, 1149, 1150, 1151, -1, 1152, 1153, 1154, -1, 1155, 1156, 1157, -1, 1158, 1159, 1160, -1, 1161, 1162, 1163, -1, 1164, 1165, 1166, -1, 1167, 1168, 1169, -1, 1170, 1171, 1172, -1, 1173, 1174, 1175, -1, 1176, 1177, 1178, -1, 1179, 1180, 1181, -1, 1182, 1183, 1184, -1, 1185, 1186, 1187, -1, 1188, 1189, 1190, -1, 1191, 1192, 1193, -1, 1194, 1195, 1196, -1, 1197, 1198, 1199, -1, 1200, 1201, 1202, -1, 1203, 1204, 1205, -1, 1206, 1207, 1208, -1, 1209, 1210, 1211, -1, 1212, 1213, 1214, -1, 1215, 1216, 1217, -1, 1218, 1219, 1220, -1, 1221, 1222, 1223, -1, 1224, 1225, 1226, -1, 1227, 1228, 1229, -1, 1230, 1231, 1232, -1, 1233, 1234, 1235, -1, 1236, 1237, 1238, -1, 1239, 1240, 1241, -1, 1242, 1243, 1244, -1, 1245, 1246, 1247, -1, 1248, 1249, 1250, -1, 1251, 1252, 1253, -1, 1254, 1255, 1256, -1, 1257, 1258, 1259, -1, 1260, 1261, 1262, -1, 1263, 1264, 1265, -1, 1266, 1267, 1268, -1, 1269, 1270, 1271, -1, 1272, 1273, 1274, -1, 1275, 1276, 1277, -1, 1278, 1279, 1280, -1, 1281, 1282, 1283, -1, 1284, 1285, 1286, -1, 1287, 1288, 1289, -1, 1290, 1291, 1292, -1, 1293, 1294, 1295, -1, 1296, 1297, 1298, -1, 1299, 1300, 1301, -1, 1302, 1303, 1304, -1, 1305, 1306, 1307, -1, 1308, 1309, 1310, -1, 1311, 1312, 1313, -1, 1314, 1315, 1316, -1, 1317, 1318, 1319, -1, 1320, 1321, 1322, -1, 1323, 1324, 1325, -1, 1326, 1327, 1328, -1, 1329, 1330, 1331, -1, 1332, 1333, 1334, -1, 1335, 1336, 1337, -1, 1338, 1339, 1340, -1, 1341, 1342, 1343, -1, 1344, 1345, 1346, -1, 1347, 1348, 1349, -1, 1350, 1351, 1352, -1, 1353, 1354, 1355, -1, 1356, 1357, 1358, -1, 1359, 1360, 1361, -1, 1362, 1363, 1364, -1, 1365, 1366, 1367, -1, 1368, 1369, 1370, -1, 1371, 1372, 1373, -1, 1374, 1375, 1376, -1, 1377, 1378, 1379, -1, 1380, 1381, 1382, -1, 1383, 1384, 1385, -1, 1386, 1387, 1388, -1, 1389, 1390, 1391, -1, 1392, 1393, 1394, -1, 1395, 1396, 1397, -1, 1398, 1399, 1400, -1, 1401, 1402, 1403, -1, 1404, 1405, 1406, -1, 1407, 1408, 1409, -1, 1410, 1411, 1412, -1, 1413, 1414, 1415, -1, 1416, 1417, 1418, -1, 1419, 1420, 1421, -1, 1422, 1423, 1424, -1, 1425, 1426, 1427, -1, 1428, 1429, 1430, -1, 1431, 1432, 1433, -1, 1434, 1435, 1436, -1, 1437, 1438, 1439, -1, 1440, 1441, 1442, -1, 1443, 1444, 1445, -1, 1446, 1447, 1448, -1, 1449, 1450, 1451, -1, 1452, 1453, 1454, -1, 1455, 1456, 1457, -1, 1458, 1459, 1460, -1, 1461, 1462, 1463, -1, 1464, 1465, 1466, -1, 1467, 1468, 1469, -1, 1470, 1471, 1472, -1, 1473, 1474, 1475, -1, 1476, 1477, 1478, -1, 1479, 1480, 1481, -1, 1482, 1483, 1484, -1, 1485, 1486, 1487, -1, 1488, 1489, 1490, -1, 1491, 1492, 1493, -1, 1494, 1495, 1496, -1, 1497, 1498, 1499, -1, 1500, 1501, 1502, -1, 1503, 1504, 1505, -1, 1506, 1507, 1508, -1, 1509, 1510, 1511, -1, 1512, 1513, 1514, -1, 1515, 1516, 1517, -1, 1518, 1519, 1520, -1, 1521, 1522, 1523, -1, 1524, 1525, 1526, -1, 1527, 1528, 1529, -1, 1530, 1531, 1532, -1, 1533, 1534, 1535, -1, 1536, 1537, 1538, -1, 1539, 1540, 1541, -1, 1542, 1543, 1544, -1, 1545, 1546, 1547, -1, 1548, 1549, 1550, -1, 1551, 1552, 1553, -1, 1554, 1555, 1556, -1, 1557, 1558, 1559, -1, 1560, 1561, 1562, -1, 1563, 1564, 1565, -1, 1566, 1567, 1568, -1, 1569, 1570, 1571, -1, 1572, 1573, 1574, -1, 1575, 1576, 1577, -1, 1578, 1579, 1580, -1, 1581, 1582, 1583, -1, 1584, 1585, 1586, -1, 1587, 1588, 1589, -1, 1590, 1591, 1592, -1, 1593, 1594, 1595, -1, 1596, 1597, 1598, -1, 1599, 1600, 1601, -1, 1602, 1603, 1604, -1, 1605, 1606, 1607, -1, 1608, 1609, 1610, -1, 1611, 1612, 1613, -1, 1614, 1615, 1616, -1, 1617, 1618, 1619, -1, 1620, 1621, 1622, -1, 1623, 1624, 1625, -1, 1626, 1627, 1628, -1, 1629, 1630, 1631, -1, 1632, 1633, 1634, -1, 1635, 1636, 1637, -1, 1638, 1639, 1640, -1, 1641, 1642, 1643, -1, 1644, 1645, 1646, -1, 1647, 1648, 1649, -1, 1650, 1651, 1652, -1, 1653, 1654, 1655, -1, 1656, 1657, 1658, -1, 1659, 1660, 1661, -1, 1662, 1663, 1664, -1, 1665, 1666, 1667, -1, 1668, 1669, 1670, -1, 1671, 1672, 1673, -1, 1674, 1675, 1676, -1, 1677, 1678, 1679, -1, 1680, 1681, 1682, -1, 1683, 1684, 1685, -1, 1686, 1687, 1688, -1, 1689, 1690, 1691, -1, 1692, 1693, 1694, -1, 1695, 1696, 1697, -1, 1698, 1699, 1700, -1, 1701, 1702, 1703, -1, 1704, 1705, 1706, -1, 1707, 1708, 1709, -1, 1710, 1711, 1712, -1, 1713, 1714, 1715, -1, 1716, 1717, 1718, -1, 1719, 1720, 1721, -1, 1722, 1723, 1724, -1, 1725, 1726, 1727, -1, 1728, 1729, 1730, -1, 1731, 1732, 1733, -1, 1734, 1735, 1736, -1, 1737, 1738, 1739, -1, 1740, 1741, 1742, -1, 1743, 1744, 1745, -1, 1746, 1747, 1748, -1, 1749, 1750, 1751, -1, 1752, 1753, 1754, -1, 1755, 1756, 1757, -1, 1758, 1759, 1760, -1, 1761, 1762, 1763, -1, 1764, 1765, 1766, -1, 1767, 1768, 1769, -1, 1770, 1771, 1772, -1, 1773, 1774, 1775, -1, 1776, 1777, 1778, -1, 1779, 1780, 1781, -1, 1782, 1783, 1784, -1, 1785, 1786, 1787, -1, 1788, 1789, 1790, -1, 1791, 1792, 1793, -1, 1794, 1795, 1796, -1, 1797, 1798, 1799, -1, 1800, 1801, 1802, -1, 1803, 1804, 1805, -1, 1806, 1807, 1808, -1, 1809, 1810, 1811, -1, 1812, 1813, 1814, -1, 1815, 1816, 1817, -1, 1818, 1819, 1820, -1, 1821, 1822, 1823, -1, 1824, 1825, 1826, -1, 1827, 1828, 1829, -1, 1830, 1831, 1832, -1, 1833, 1834, 1835, -1, 1836, 1837, 1838, -1, 1839, 1840, 1841, -1, 1842, 1843, 1844, -1, 1845, 1846, 1847, -1, 1848, 1849, 1850, -1, 1851, 1852, 1853, -1, 1854, 1855, 1856, -1, 1857, 1858, 1859, -1, 1860, 1861, 1862, -1, 1863, 1864, 1865, -1, 1866, 1867, 1868, -1, 1869, 1870, 1871, -1, 1872, 1873, 1874, -1, 1875, 1876, 1877, -1, 1878, 1879, 1880, -1, 1881, 1882, 1883, -1, 1884, 1885, 1886, -1, 1887, 1888, 1889, -1, 1890, 1891, 1892, -1, 1893, 1894, 1895, -1, 1896, 1897, 1898, -1, 1899, 1900, 1901, -1, 1902, 1903, 1904, -1, 1905, 1906, 1907, -1, 1908, 1909, 1910, -1, 1911, 1912, 1913, -1, 1914, 1915, 1916, -1, 1917, 1918, 1919, -1, 1920, 1921, 1922, -1, 1923, 1924, 1925, -1, 1926, 1927, 1928, -1, 1929, 1930, 1931, -1, 1932, 1933, 1934, -1, 1935, 1936, 1937, -1, 1938, 1939, 1940, -1, 1941, 1942, 1943, -1, 1944, 1945, 1946, -1, 1947, 1948, 1949, -1, 1950, 1951, 1952, -1, 1953, 1954, 1955, -1, 1956, 1957, 1958, -1, 1959, 1960, 1961, -1, 1962, 1963, 1964, -1, 1965, 1966, 1967, -1, 1968, 1969, 1970, -1, 1971, 1972, 1973, -1, 1974, 1975, 1976, -1, 1977, 1978, 1979, -1, 1980, 1981, 1982, -1, 1983, 1984, 1985, -1, 1986, 1987, 1988, -1, 1989, 1990, 1991, -1, 1992, 1993, 1994, -1, 1995, 1996, 1997, -1, 1998, 1999, 2000, -1, 2001, 2002, 2003, -1, 2004, 2005, 2006, -1, 2007, 2008, 2009, -1, 2010, 2011, 2012, -1, 2013, 2014, 2015, -1, 2016, 2017, 2018, -1, 2019, 2020, 2021, -1, 2022, 2023, 2024, -1, 2025, 2026, 2027, -1, 2028, 2029, 2030, -1, 2031, 2032, 2033, -1, 2034, 2035, 2036, -1, 2037, 2038, 2039, -1, 2040, 2041, 2042, -1, 2043, 2044, 2045, -1, 2046, 2047, 2048, -1, 2049, 2050, 2051, -1, 2052, 2053, 2054, -1, 2055, 2056, 2057, -1, 2058, 2059, 2060, -1, 2061, 2062, 2063, -1, 2064, 2065, 2066, -1, 2067, 2068, 2069, -1, 2070, 2071, 2072, -1, 2073, 2074, 2075, -1, 2076, 2077, 2078, -1, 2079, 2080, 2081, -1, 2082, 2083, 2084, -1, 2085, 2086, 2087, -1, 2088, 2089, 2090, -1, 2091, 2092, 2093, -1, 2094, 2095, 2096, -1, 2097, 2098, 2099, -1, 2100, 2101, 2102, -1, 2103, 2104, 2105, -1, 2106, 2107, 2108, -1, 2109, 2110, 2111, -1, 2112, 2113, 2114, -1, 2115, 2116, 2117, -1, 2118, 2119, 2120, -1, 2121, 2122, 2123, -1, 2124, 2125, 2126, -1, 2127, 2128, 2129, -1, 2130, 2131, 2132, -1, 2133, 2134, 2135, -1, 2136, 2137, 2138, -1, 2139, 2140, 2141, -1, 2142, 2143, 2144, -1, 2145, 2146, 2147, -1, 2148, 2149, 2150, -1, 2151, 2152, 2153, -1, 2154, 2155, 2156, -1, 2157, 2158, 2159, -1, 2160, 2161, 2162, -1, 2163, 2164, 2165, -1, 2166, 2167, 2168, -1, 2169, 2170, 2171, -1, 2172, 2173, 2174, -1, 2175, 2176, 2177, -1, 2178, 2179, 2180, -1, 2181, 2182, 2183, -1, 2184, 2185, 2186, -1, 2187, 2188, 2189, -1, 2190, 2191, 2192, -1, 2193, 2194, 2195, -1, 2196, 2197, 2198, -1, 2199, 2200, 2201, -1, 2202, 2203, 2204, -1, 2205, 2206, 2207, -1, 2208, 2209, 2210, -1, 2211, 2212, 2213, -1, 2214, 2215, 2216, -1, 2217, 2218, 2219, -1, 2220, 2221, 2222, -1, 2223, 2224, 2225, -1, 2226, 2227, 2228, -1, 2229, 2230, 2231, -1, 2232, 2233, 2234, -1, 2235, 2236, 2237, -1, 2238, 2239, 2240, -1, 2241, 2242, 2243, -1, 2244, 2245, 2246, -1, 2247, 2248, 2249, -1, 2250, 2251, 2252, -1, 2253, 2254, 2255, -1, 2256, 2257, 2258, -1, 2259, 2260, 2261, -1, 2262, 2263, 2264, -1, 2265, 2266, 2267, -1, 2268, 2269, 2270, -1, 2271, 2272, 2273, -1, 2274, 2275, 2276, -1, 2277, 2278, 2279, -1, 2280, 2281, 2282, -1, 2283, 2284, 2285, -1, 2286, 2287, 2288, -1, 2289, 2290, 2291, -1, 2292, 2293, 2294, -1, 2295, 2296, 2297, -1, 2298, 2299, 2300, -1, 2301, 2302, 2303, -1, 2304, 2305, 2306, -1, 2307, 2308, 2309, -1, 2310, 2311, 2312, -1, 2313, 2314, 2315, -1, 2316, 2317, 2318, -1, 2319, 2320, 2321, -1, 2322, 2323, 2324, -1, 2325, 2326, 2327, -1, 2328, 2329, 2330, -1, 2331, 2332, 2333, -1, 2334, 2335, 2336, -1
          ]
        }
      }
      Shape {
        appearance PBRAppearance {
          baseColorMap ImageTexture {
            url [
              "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/palm_tree_trunk.jpg"
            ]
          }
          metalness 0
          roughness 1
        }
        geometry IndexedFaceSet {
          coord Coordinate {
            point [
              0 -0.252763 0 0.218899 0.126382 0 0 -0.201711 1.014882 0.174687 0.100855 1.014882 0 -0.150658 2.029763 -0.218899 0.126382 0 0.130474 0.075329 2.029763 -0.174687 0.100855 1.014882 -0.130474 0.075329 2.029763 0 -0.123672 2.184017 0.107103 0.061836 2.184017 0 0 2.33827 -0.107103 0.061836 2.184017
            ]
          }
          texCoord TextureCoordinate {
            point [
              0 0, 1.333 0, 0 3, 0 3, 1.333 0, 1.333 3, 0 3, 1.333 3, 0 6, 1.333 0, 2.666 0, 1.333 3, 0 6, 1.333 3, 1.333 6, 1.333 3, 2.666 0, 2.666 3, 1.333 3, 2.666 3, 1.333 6, 2.666 0, 4 0, 2.666 3, 1.333 6, 2.666 3, 2.666 6, 2.666 3, 4 0, 4 3, 2.666 3, 4 3, 2.666 6, 2.666 6, 4 3, 4 6, 0 0, 0.3333 0, 0 0.5, 0 0.5, 0.3333 0, 0.3333 0.5, 0 0.5, 0.3333 0.5, 0 1, 0.3333 0, 0.6665 0, 0.3333 0.5, 0.3333 0.5, 0.6665 0, 0.6665 0.5, 0.3333 0.5, 0.6665 0.5, 0.3333 1, 0.6665 0, 1 0, 0.6665 0.5, 0.6665 0.5, 1 0, 1 0.5, 0.6665 0.5, 1 0.5, 0.6665 1
            ]
          }
          coordIndex [
            0, 1, 2, -1, 2, 1, 3, -1, 2, 3, 4, -1, 1, 5, 3, -1, 4, 3, 6, -1, 3, 5, 7, -1, 3, 7, 6, -1, 5, 0, 7, -1, 6, 7, 8, -1, 7, 0, 2, -1, 7, 2, 8, -1, 8, 2, 4, -1, 4, 6, 9, -1, 9, 6, 10, -1, 9, 10, 11, -1, 6, 8, 10, -1, 10, 8, 12, -1, 10, 12, 11, -1, 8, 4, 12, -1, 12, 4, 9, -1, 12, 9, 11, -1
          ]
          texCoordIndex [
            0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1, 60, 61, 62, -1
          ]
        }
      }
      Shape {
        appearance PBRAppearance {
          baseColorMap ImageTexture {
            url [
              "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/palm_tree_leaf.png"
            ]
          }
          metalness 0
          roughness 1
        }
        geometry IndexedFaceSet {
          coord Coordinate {
            point [
              0.783923 -1.339769 2.663305 0.03584 -0.85735 2.908271 0.694916 -1.761669 2.773566 0.019166 -1.571665 3.373799 0.598125 -2.535471 2.882041 0.019465 -2.416827 3.591569 0.03584 -0.85735 2.908271 -0.857499 -1.030712 2.75253 -0.897893 -1.607308 3.268612 -0.903378 -2.421703 3.617885 0.375014 2.008164 3.223929 1.171581 1.333269 3.490303 0.740891 2.419195 2.871395 1.623401 1.818824 2.99055 1.067019 2.791945 2.198868 1.930842 2.150952 2.263907 1.171581 1.333268 3.490303 1.788512 0.6307 2.95478 1.913857 0.851638 2.623201 2.09882 1.143678 1.919708 0.026058 -1.415467 3.787253 -0.679999 -0.64375 3.945758 -0.28887 -1.812902 3.615239 -1.224905 -1.384126 3.840252 -0.617795 -2.400486 3.188956 -1.614166 -2.141305 3.432579 -0.679999 -0.643749 3.945758 -1.377523 -0.154133 3.318938 -1.91339 -0.779079 3.322427 -2.37334 -1.533723 3.015708 -0.92602 0.870171 2.835871 -0.105813 0.579984 3.216327 -0.949893 1.376047 2.986207 -0.180304 1.372095 3.542163 -0.971513 2.168505 2.961699 -0.228848 2.254611 3.547126 -0.105812 0.579984 3.216327 0.786428 0.877384 3.085346 0.740702 1.445851 3.323846 0.700683 2.275619 3.354198 1.285304 0.204993 3.274258 0.724379 -0.540807 3.404924 1.785147 0.011434 3.59735 1.411601 -0.822588 3.828715 2.548923 -0.274173 3.742218 2.223959 -1.135321 3.944057 0.724379 -0.540808 3.404924 0.794576 -1.427517 3.093883 1.242076 -1.590783 3.309332 1.973177 -1.851473 3.385351 -1.075193 -0.883877 2.982603 -1.032357 -0.078249 3.467923 -1.613008 -0.896329 2.770871 -1.940553 -0.144584 3.235011 -2.289182 -0.917694 2.235879 -2.720326 -0.204312 2.673608 -1.032357 -0.078249 3.467923 -1.155474 0.812595 3.189275 -1.902935 0.779535 3.054087 -2.704463 0.731852 2.574861 0.681313 1.012093 3.355029 0.500063 0.100233 3.605932 1.191647 1.091116 3.76557 1.099066 0.223153 4.175507 1.872074 1.197077 4.118817 1.80899 0.366767 4.602161 0.500063 0.100233 3.605932 0.924317 -0.74751 3.436664 1.423319 -0.658893 3.958167 2.089408 -0.532687 4.403754 -1.224725 -0.232649 3.327962 -0.37387 0.213737 3.611554 -1.590163 0.063509 3.771529 -0.806121 0.463527 4.250597 -2.081948 0.441361 4.220475 -1.329179 0.761032 4.798983 -0.37387 0.213737 3.611554 -0.009762 1.143192 3.526768 -0.334377 1.346978 4.213883 -0.788023 1.602094 4.856333 1.048636 -0.240587 3.36306 0.048451 -0.187224 3.424174 1.084333 -0.328257 4.002642 0.082613 -0.32214 4.060926 1.119139 -0.431933 4.645097 0.117685 -0.471985 4.694347 0.048451 -0.187224 3.424174 -0.671451 -0.879465 3.326727 -0.606714 -1.041099 3.939169 -0.539533 -1.21718 4.55402 0.783923 -1.339769 2.663305 0.03584 -0.85735 2.908271 0.694916 -1.761669 2.773566 0.019166 -1.571665 3.373799 0.598125 -2.535471 2.882041 0.019465 -2.416827 3.591569 0.03584 -0.85735 2.908271 -0.857499 -1.030712 2.75253 -0.897893 -1.607308 3.268612 -0.903378 -2.421703 3.617885 0.375014 2.008164 3.223929 1.171581 1.333269 3.490303 0.740891 2.419195 2.871395 1.623401 1.818824 2.99055 1.067019 2.791945 2.198868 1.930842 2.150952 2.263907 1.171581 1.333268 3.490303 1.788512 0.6307 2.95478 1.913857 0.851638 2.623201 2.09882 1.143678 1.919708 0.026058 -1.415467 3.787253 -0.679999 -0.64375 3.945758 -0.28887 -1.812902 3.615239 -1.224905 -1.384126 3.840252 -0.617795 -2.400486 3.188956 -1.614166 -2.141305 3.432579 -0.679999 -0.643749 3.945758 -1.377523 -0.154133 3.318938 -1.91339 -0.779079 3.322427 -2.37334 -1.533723 3.015708 -0.92602 0.870171 2.835871 -0.105813 0.579984 3.216327 -0.949893 1.376047 2.986207 -0.180304 1.372095 3.542163 -0.971513 2.168505 2.961699 -0.228848 2.254611 3.547126 -0.105812 0.579984 3.216327 0.786428 0.877384 3.085346 0.740702 1.445851 3.323846 0.700683 2.275619 3.354198 1.285304 0.204993 3.274258 0.724379 -0.540807 3.404924 1.785147 0.011434 3.59735 1.411601 -0.822588 3.828715 2.548923 -0.274173 3.742218 2.223959 -1.135321 3.944057 0.724379 -0.540808 3.404924 0.794576 -1.427517 3.093883 1.242076 -1.590783 3.309332 1.973177 -1.851473 3.385351 -1.075193 -0.883877 2.982603 -1.032357 -0.078249 3.467923 -1.613008 -0.896329 2.770871 -1.940553 -0.144584 3.235011 -2.289182 -0.917694 2.235879 -2.720326 -0.204312 2.673608 -1.032357 -0.078249 3.467923 -1.155474 0.812595 3.189275 -1.902935 0.779535 3.054087 -2.704463 0.731852 2.574861 0.681313 1.012093 3.355029 0.500063 0.100233 3.605932 1.191647 1.091116 3.76557 1.099066 0.223153 4.175507 1.872074 1.197077 4.118817 1.80899 0.366767 4.602161 0.500063 0.100233 3.605932 0.924317 -0.74751 3.436664 1.423319 -0.658893 3.958167 2.089408 -0.532687 4.403754 -1.224725 -0.232649 3.327962 -0.37387 0.213737 3.611554 -1.590163 0.063509 3.771529 -0.806121 0.463527 4.250597 -2.081948 0.441361 4.220475 -1.329179 0.761032 4.798983 -0.37387 0.213737 3.611554 -0.009762 1.143192 3.526768 -0.334377 1.346978 4.213883 -0.788023 1.602094 4.856333 1.048636 -0.240587 3.36306 0.048451 -0.187224 3.424174 1.084333 -0.328257 4.002642 0.082613 -0.32214 4.060926 1.119139 -0.431933 4.645097 0.117685 -0.471985 4.694347 0.048451 -0.187224 3.424174 -0.671451 -0.879465 3.326727 -0.606714 -1.041099 3.939169 -0.539533 -1.21718 4.55402
            ]
          }
          texCoord TextureCoordinate {
            point [
              0 0, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0.5 0, 1 0, 0.5 0.5, 0.5 0.5, 1 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0.5, 0 1, 0.5 0, 1 0, 0.5 0.5, 0.5 0.5, 1 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0.5, 0 1, 0 1, 0.5 0.5, 0.5 1, 0.5 0, 1 0, 0.5 0.5, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 1, 0.5 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0 0.5, 0.5 0.5, 0 1, 0 1, 0.5 0.5, 0.5 1, 0.5 0, 1 0, 0.5 0.5, 0.5 0.5, 1 0, 1 0.5, 0.5 0.5, 1 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0.5, 0 1, 0 1, 0.5 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0.5 1, 1 0.5, 1 1, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0.5, 0 1, 0.5 1, 1 0.5, 1 1, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0, 1 0, 0.5 0.5, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0.5, 0 1, 0 1, 0.5 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0 0.5, 0.5 0.5, 0 1, 0 1, 0.5 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0.5 0, 1 0, 0.5 0.5, 0 1, 0.5 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0 1, 0.5 0.5, 0.5 1, 0.5 1, 1 0.5, 1 1, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0.5 0, 1 0, 0.5 0.5, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0.5, 0 1, 0.5 1, 1 0.5, 1 1, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0 0.5, 0.5 0.5, 0 1, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0.5, 1 0, 1 0.5, 0.5 0, 1 0, 0.5 0.5, 0 0, 0.5 0, 0 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0 0.5, 0.5 0, 0.5 0.5, 0.5 0.5, 1 0.5, 0.5 1, 0.5 0, 1 0, 0.5 0.5, 0 0, 0 0.5, 0.5 0, 0 1, 0.5 1, 0.5 0.5, 0.5 0.5, 1 0.5, 1 0, 0.5 0, 0.5 0.5, 1 0, 0.5 0.5, 0.5 1, 1 0.5, 0.5 1, 1 1, 1 0.5, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0.5 0, 0.5 0.5, 1 0, 0.5 0.5, 0.5 1, 1 0.5, 0.5 1, 1 1, 1 0.5, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0 1, 0.5 1, 0.5 0.5, 0.5 0, 0.5 0.5, 1 0, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 1, 0.5 1, 0.5 0.5, 0.5 1, 1 1, 1 0.5, 0 0.5, 0 1, 0.5 0.5, 0 1, 0.5 1, 0.5 0.5, 0.5 0, 0.5 0.5, 1 0, 0.5 0.5, 1 0.5, 1 0, 0.5 0.5, 0.5 1, 1 0.5, 0.5 1, 1 1, 1 0.5, 0 0, 0 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0 1, 0.5 1, 0.5 0.5, 0.5 0.5, 1 0.5, 1 0, 0.5 1, 1 1, 1 0.5, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0.5 1, 1 1, 1 0.5, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0, 0.5 0.5, 1 0, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0 1, 0.5 1, 0.5 0.5, 0.5 0.5, 1 0.5, 1 0, 0 0.5, 0 1, 0.5 0.5, 0 1, 0.5 1, 0.5 0.5, 0.5 1, 1 1, 1 0.5, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0.5, 1 0.5, 1 0, 0.5 0, 0.5 0.5, 1 0, 0 1, 0.5 1, 0.5 0.5, 0.5 0.5, 1 0.5, 1 0, 0 1, 0.5 1, 0.5 0.5, 0.5 1, 1 1, 1 0.5, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0.5, 1 0.5, 1 0, 0.5 0, 0.5 0.5, 1 0, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0 1, 0.5 0.5, 0.5 1, 1 1, 1 0.5, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0.5, 1 0.5, 1 0, 0 0.5, 0 1, 0.5 0.5, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0.5, 1 0.5, 1 0, 0.5 0, 0.5 0.5, 1 0, 0 0, 0 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0 0.5, 0.5 0.5, 0.5 0, 0.5 0.5, 0.5 1, 1 0.5, 0.5 0, 0.5 0.5, 1 0
            ]
          }
          coordIndex [
            0, 1, 2, -1, 4, 3, 5, -1, 3, 7, 8, -1, 16, 17, 13, -1, 13, 18, 15, -1, 15, 18, 19, -1, 20, 21, 22, -1, 22, 21, 23, -1, 30, 31, 32, -1, 32, 31, 33, -1, 32, 33, 34, -1, 36, 37, 33, -1, 33, 38, 35, -1, 35, 38, 39, -1, 40, 41, 42, -1, 42, 41, 43, -1, 42, 43, 44, -1, 54, 53, 55, -1, 56, 57, 53, -1, 60, 61, 62, -1, 62, 61, 63, -1, 64, 63, 65, -1, 65, 68, 69, -1, 72, 73, 74, -1, 74, 73, 75, -1, 76, 77, 73, -1, 73, 77, 78, -1, 73, 78, 75, -1, 75, 78, 79, -1, 80, 81, 82, -1, 82, 83, 84, -1, 84, 83, 85, -1, 83, 87, 88, -1, 85, 88, 89, -1, 2, 1, 3, -1, 2, 3, 4, -1, 5, 8, 9, -1, 3, 8, 5, -1, 6, 7, 3, -1, 10, 11, 12, -1, 12, 11, 13, -1, 12, 13, 14, -1, 14, 13, 15, -1, 13, 17, 18, -1, 22, 23, 24, -1, 24, 23, 25, -1, 25, 28, 29, -1, 23, 28, 25, -1, 23, 27, 28, -1, 26, 27, 23, -1, 34, 33, 35, -1, 33, 37, 38, -1, 44, 43, 45, -1, 45, 48, 49, -1, 43, 48, 45, -1, 43, 47, 48, -1, 46, 47, 43, -1, 50, 51, 52, -1, 52, 51, 53, -1, 52, 53, 54, -1, 55, 58, 59, -1, 53, 58, 55, -1, 53, 57, 58, -1, 62, 63, 64, -1, 63, 68, 65, -1, 63, 67, 68, -1, 66, 67, 63, -1, 70, 71, 72, -1, 72, 71, 73, -1, 82, 81, 83, -1, 83, 88, 85, -1, 86, 87, 83, -1, 90, 92, 91, -1, 94, 95, 93, -1, 93, 98, 97, -1, 106, 103, 107, -1, 103, 105, 108, -1, 105, 109, 108, -1, 110, 112, 111, -1, 112, 113, 111, -1, 120, 122, 121, -1, 122, 123, 121, -1, 122, 124, 123, -1, 126, 123, 127, -1, 123, 125, 128, -1, 125, 129, 128, -1, 130, 132, 131, -1, 132, 133, 131, -1, 132, 134, 133, -1, 144, 145, 143, -1, 146, 143, 147, -1, 150, 152, 151, -1, 152, 153, 151, -1, 154, 155, 153, -1, 155, 159, 158, -1, 162, 164, 163, -1, 164, 165, 163, -1, 166, 163, 167, -1, 163, 168, 167, -1, 163, 165, 168, -1, 165, 169, 168, -1, 170, 172, 171, -1, 172, 174, 173, -1, 174, 175, 173, -1, 173, 178, 177, -1, 175, 179, 178, -1, 92, 93, 91, -1, 92, 94, 93, -1, 95, 99, 98, -1, 93, 95, 98, -1, 96, 93, 97, -1, 100, 102, 101, -1, 102, 103, 101, -1, 102, 104, 103, -1, 104, 105, 103, -1, 103, 108, 107, -1, 112, 114, 113, -1, 114, 115, 113, -1, 115, 119, 118, -1, 113, 115, 118, -1, 113, 118, 117, -1, 116, 113, 117, -1, 124, 125, 123, -1, 123, 128, 127, -1, 134, 135, 133, -1, 135, 139, 138, -1, 133, 135, 138, -1, 133, 138, 137, -1, 136, 133, 137, -1, 140, 142, 141, -1, 142, 143, 141, -1, 142, 144, 143, -1, 145, 149, 148, -1, 143, 145, 148, -1, 143, 148, 147, -1, 152, 154, 153, -1, 153, 155, 158, -1, 153, 158, 157, -1, 156, 153, 157, -1, 160, 162, 161, -1, 162, 163, 161, -1, 172, 173, 171, -1, 173, 175, 178, -1, 176, 173, 177, -1
          ]
          texCoordIndex [
            0, 1, 2, -1, 3, 4, 5, -1, 6, 7, 8, -1, 9, 10, 11, -1, 12, 13, 14, -1, 15, 16, 17, -1, 18, 19, 20, -1, 21, 22, 23, -1, 24, 25, 26, -1, 27, 28, 29, -1, 30, 31, 32, -1, 33, 34, 35, -1, 36, 37, 38, -1, 39, 40, 41, -1, 42, 43, 44, -1, 45, 46, 47, -1, 48, 49, 50, -1, 51, 52, 53, -1, 54, 55, 56, -1, 57, 58, 59, -1, 60, 61, 62, -1, 63, 64, 65, -1, 66, 67, 68, -1, 69, 70, 71, -1, 72, 73, 74, -1, 75, 76, 77, -1, 78, 79, 80, -1, 81, 82, 83, -1, 84, 85, 86, -1, 87, 88, 89, -1, 90, 91, 92, -1, 93, 94, 95, -1, 96, 97, 98, -1, 99, 100, 101, -1, 102, 103, 104, -1, 105, 106, 107, -1, 108, 109, 110, -1, 111, 112, 113, -1, 114, 115, 116, -1, 117, 118, 119, -1, 120, 121, 122, -1, 123, 124, 125, -1, 126, 127, 128, -1, 129, 130, 131, -1, 132, 133, 134, -1, 135, 136, 137, -1, 138, 139, 140, -1, 141, 142, 143, -1, 144, 145, 146, -1, 147, 148, 149, -1, 150, 151, 152, -1, 153, 154, 155, -1, 156, 157, 158, -1, 159, 160, 161, -1, 162, 163, 164, -1, 165, 166, 167, -1, 168, 169, 170, -1, 171, 172, 173, -1, 174, 175, 176, -1, 177, 178, 179, -1, 180, 181, 182, -1, 183, 184, 185, -1, 186, 187, 188, -1, 189, 190, 191, -1, 192, 193, 194, -1, 195, 196, 197, -1, 198, 199, 200, -1, 201, 202, 203, -1, 204, 205, 206, -1, 207, 208, 209, -1, 210, 211, 212, -1, 213, 214, 215, -1, 216, 217, 218, -1, 219, 220, 221, -1, 222, 223, 224, -1, 225, 226, 227, -1, 228, 229, 230, -1, 231, 232, 233, -1, 234, 235, 236, -1, 237, 238, 239, -1, 240, 241, 242, -1, 243, 244, 245, -1, 246, 247, 248, -1, 249, 250, 251, -1, 252, 253, 254, -1, 255, 256, 257, -1, 258, 259, 260, -1, 261, 262, 263, -1, 264, 265, 266, -1, 267, 268, 269, -1, 270, 271, 272, -1, 273, 274, 275, -1, 276, 277, 278, -1, 279, 280, 281, -1, 282, 283, 284, -1, 285, 286, 287, -1, 288, 289, 290, -1, 291, 292, 293, -1, 294, 295, 296, -1, 297, 298, 299, -1, 300, 301, 302, -1, 303, 304, 305, -1, 306, 307, 308, -1, 309, 310, 311, -1, 312, 313, 314, -1, 315, 316, 317, -1, 318, 319, 320, -1, 321, 322, 323, -1, 324, 325, 326, -1, 327, 328, 329, -1, 330, 331, 332, -1, 333, 334, 335, -1, 336, 337, 338, -1, 339, 340, 341, -1, 342, 343, 344, -1, 345, 346, 347, -1, 348, 349, 350, -1, 351, 352, 353, -1, 354, 355, 356, -1, 357, 358, 359, -1, 360, 361, 362, -1, 363, 364, 365, -1, 366, 367, 368, -1, 369, 370, 371, -1, 372, 373, 374, -1, 375, 376, 377, -1, 378, 379, 380, -1, 381, 382, 383, -1, 384, 385, 386, -1, 387, 388, 389, -1, 390, 391, 392, -1, 393, 394, 395, -1, 396, 397, 398, -1, 399, 400, 401, -1, 402, 403, 404, -1, 405, 406, 407, -1, 408, 409, 410, -1, 411, 412, 413, -1, 414, 415, 416, -1, 417, 418, 419, -1, 420, 421, 422, -1, 423, 424, 425, -1, 426, 427, 428, -1, 429, 430, 431, -1
          ]
        }
      }
    ]
    %< if (fields.enableBoundingObject.value) { >%
    boundingObject Pose {
      translation 0 0 1.125
      children [
        Cylinder {
          height 2.25
          radius 0.35
        }
      ]
    }
    %< } >%
  }
}
