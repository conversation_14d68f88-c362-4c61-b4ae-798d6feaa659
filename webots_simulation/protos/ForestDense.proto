#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/trees/protos/Forest.proto
# tags: nonDeterministic
# keywords: exterior/tree
# Efficient and customizable forest.
# Tree types are:
#  - 'oak tree'
#  - 'crab apple tree'
#  - 'cherry tree'
#  - 'birch tree'
#  - 'palm tree'
#  - 'spruce'
#  - 'white pine'
#  - 'hackberry tree'
#  - 'hazel tree'
#
# The 'random' type choose randomly a tree type each time the node is regenerated.
# The shape of the forest can either be defined using the 'shape' and 'density' fields or the coordinate of each tree can be defined in external files ( X,Y,Z per tree, one tree per line).
# The path to those files must be defined with respect to the world file.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).
# template language: javascript

PROTO ForestDense [
  field SFVec3f    translation          0 0 0
  field SFRotation rotation             0 0 1 0
  field SFString   name                 "ForestDense"
  field MFString   treesFiles           []
  field MFVec2f    shape                [-20 -10, 20 -10, 0 25]
  field SFFloat    density              0.2
  field SFString{"random", "oak", "palm", "big_sassafras", "sassafras"}
                   type                 "oak"
  field SFInt32    randomSeed           0
  field MFString   groundTexture        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/textures/dry_leaf.jpg"
  field SFBool     withSnow             FALSE
  field SFFloat    maxHeight            6
  field SFFloat    minHeight            2
  field SFFloat    maxRadius            3
  field SFFloat    minRadius            1
]
{
  %<
    import * as wbgeometry from 'wbgeometry.js';
    import * as wbrandom from 'wbrandom.js';

    if (fields.randomSeed.value === 0)
      wbrandom.seed(context.id);
    else if (fields.randomSeed.value < 0)
      wbrandom.seed(Date.now());
    else
      wbrandom.seed(fields.randomSeed.value);

    const type = fields.type.value;
    const shape = fields.shape.value;
    const groundTexture = fields.groundTexture.value;
    const nbShapePoint = shape.length;
    const maxHeight = fields.maxHeight.value;
    const minHeight = fields.minHeight.value;
    const maxRadius = fields.maxRadius.value;
    const minRadius = fields.minRadius.value;

    let density = fields.density.value;
    if (density < 0) {
      density = fields.density.defaultValue;
      console.error('\'density\' must be greater or equal to 0. Value reset to ' + density + '.');
    }

    const treesFiles = fields.treesFiles.value;
    const treesFilesNumber = treesFiles.length;

    let trees = [];
    // generate the trees table using the treesFiles if set and the shape otherwise
    if (treesFilesNumber > 0) {
      for (let i = 0; i < treesFilesNumber; ++i) {
        // coordinate files are assumed to be relative to the world file
        let worldPath = context.world;
        worldPath = worldPath.substring(0, worldPath.lastIndexOf('/') + 1);
        // read content
        let content = wbfile.readTextFile(worldPath + treesFiles[i]);
        let lines = content.split('\n');
        for (let j = 0; j < lines.length; ++j) {
          let coordinates = lines[j].split(',');
          if(coordinates.length === 3) {
            let x = Number(coordinates[0]);
            let y = Number(coordinates[1]);
            let z = Number(coordinates[2]);
            let sizeFactor = wbrandom.real();
            if (typeof x !== 'undefined' && typeof y !== 'undefined' && typeof z !== 'undefined')
              trees.push({x: x, y: -y, z: z, angle: wbrandom.real(2 * Math.PI), height: maxHeight * sizeFactor + minHeight * (1 - sizeFactor), radius: maxRadius * sizeFactor + minRadius * (1 - sizeFactor)});
          }
        }
      }
    } else {
      // compute the maximum tree number
      let xMin = shape[0].x;
      let xMax = shape[0].x;
      let yMin = shape[0].y;
      let yMax = shape[0].y;

      for (let i = 0; i < nbShapePoint; ++i) {
        if (xMax < shape[i].x)
          xMax = shape[i].x;

        if (xMin > shape[i].x)
          xMin = shape[i].x;
        if (yMax < shape[i].y)
          yMax = shape[i].y;
        if (yMin > shape[i].y)
          yMin = shape[i].y;
      }
      let numberOfTree = Math.round((xMax - xMin) * (yMax - yMin) * density);  // we assume a density of 0.01 tree per meter square

      // generate the list of threes
      for (let i = 0; i < numberOfTree; ++i) {
        let x = wbrandom.real(xMin, xMax);
        let y = wbrandom.real(yMin, yMax);
        let sizeFactor = wbrandom.real();
        if (wbgeometry.isPoint2InPolygon({x: x, y: y}, shape))
            trees.push({x: x, y: -y, z: 0, angle: wbrandom.real(2 * Math.PI), height: maxHeight * sizeFactor + minHeight * (1 - sizeFactor), radius: maxRadius * sizeFactor + minRadius * (1 - sizeFactor)});
      }
    }
    let numberOfTree = trees.length;
    let types = [];
    if (type === 'random') {
      const availableTypes = ["oak", "palm", "big_sassafras", "sassafras"];
      for (let i = 0; i < numberOfTree; ++i) {
        let j = wbrandom.integer(availableTypes.length) - 1;
        types.push(availableTypes[j]);
      }
    } else {
      for (let i = 0; i < numberOfTree; ++i)
        types.push(type);
    }
    
    // Generate unique prefix based on node ID to avoid name conflicts
    const uniquePrefix = "forest_" + context.id + "_";
  >%
  Pose {
    translation IS translation
    rotation IS rotation
    children [
      %< for (let j = 0; j < numberOfTree; ++j) { >%
        %< if (types[j] === 'oak') { >%
          Oak {
            translation %<= trees[j].x >% %<= trees[j].y >% %<= trees[j].z >%
            rotation 0 0 1 %<= trees[j].angle >%
            name "%<= uniquePrefix >%%<= types[j] >%_tree_%<= j >%"
            enableBoundingObject TRUE
          }
        %< } else if (types[j] === 'palm') { >%
          PalmTree {
            translation %<= trees[j].x >% %<= trees[j].y >% %<= trees[j].z >%
            rotation 0 0 1 %<= trees[j].angle >%
            name "%<= uniquePrefix >%%<= types[j] >%_tree_%<= j >%"
            enableBoundingObject TRUE
          }
        %< } else if (types[j] === 'big_sassafras') { >%
          BigSassafras {
            translation %<= trees[j].x >% %<= trees[j].y >% %<= trees[j].z >%
            rotation 0 0 1 %<= trees[j].angle >%
            name "%<= uniquePrefix >%%<= types[j] >%_tree_%<= j >%"
            enableBoundingObject TRUE
          }
        %< } else if (types[j] === 'sassafras') { >%
          Sassafras {
            translation %<= trees[j].x >% %<= trees[j].y >% %<= trees[j].z >%
            rotation 0 0 1 %<= trees[j].angle >%
            name "%<= uniquePrefix >%%<= types[j] >%_tree_%<= j >%"
            enableBoundingObject TRUE
          }
        %< } >%
      %< } >%
      %< if (groundTexture.length > 0 && nbShapePoint > 2) { >%
        DEF GROUND Shape {
          castShadows FALSE
          appearance PBRAppearance {
            baseColorMap ImageTexture {
                url IS groundTexture
            }
            metalness 0
            roughness 1
          }
          geometry IndexedFaceSet {
              coord Coordinate {
                point [
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= shape[i].x >% %<= -shape[i].y >% %<= 0 >%
                  %< } >%
                ]
              }
              texCoord TextureCoordinate {
                point [
                  %< for (let i = 0; i < nbShapePoint; ++i) { >%
                    %<= shape[i].x - shape[0].x >% %<= shape[i].y - shape[0].y >%
                  %< } >%
                ]
              }
              coordIndex [
                %< for (let i = 0; i < nbShapePoint; ++i) { >%
                  %<= (nbShapePoint - i) - 1 >%
                %< } >%
                -1
              ]
              texCoordIndex [
                %< for (let i = 0; i < nbShapePoint; ++i) { >%
                  %<= (nbShapePoint - i) - 1 >%
                %< } >%
                -1
              ]
          }
        }
      %< } >%
    ]
  }
}
