#include "wb_lidar.hpp"

#include "pcl/point_types.h"
#include "pcl_conversions/pcl_conversions.h"
#include <cmath>

struct PointXYZIRT {
    PCL_ADD_POINT4D
    PCL_ADD_INTENSITY;
    uint16_t ring;
    double time;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;
POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZIRT, (float, x, x)(float, y, y)(float, z, z)(float, intensity,
                                                                                       intensity)(uint16_t, ring,
                                                                                                  ring)(double, time, time))

DDSWbLidar::DDSWbLidar(webots::Robot *robot, int step, const UParam &param) : DDSWbSensor("lidar", robot, step, param)
{
    super_lidar_ = (webots::Supervisor *)robot;
}

DDSWbLidar::~DDSWbLidar()
{
    if (super_lidar_) delete super_lidar_;
}

bool DDSWbLidar::onInit()
{
    param_.param("wb_lidar_name", wb_lidar_name_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));
    param_.param("filter_by_angle", filter_by_angle_, false);
    param_.param("angle_range", angle_range_, 1.74533);

    lidar_ = (webots::Lidar *)robot_->getDevice(wb_lidar_name_);

    if (lidar_ == nullptr) {
        printf("DDSWbLidar::onInit - ERROR: Could not find lidar device: %s\n", wb_lidar_name_.c_str());
        return false;
    }

    printf("DDSWbLidar::onInit - Successfully found lidar device: %s\n", wb_lidar_name_.c_str());

    lidar_->enable(sensor_step_);
    lidar_->enablePointCloud();
    lidar_->setFrequency(10);

    pc_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::PointCloud2>>(topic_name_);

    copy_lidar_pose_writer_ = std::make_shared<uslam::transport::Writer<nav_msgs::msg::Odometry>>("/lidar/pose_copy");

    printf("DDSWbLidar::init wb_lidar_name:%s step:%d topic_name:%s link_name:%s filter_by_angle:%d angle_range:%f\n",
           wb_lidar_name_.c_str(), sensor_step_, topic_name_.c_str(), link_name_.c_str(), filter_by_angle_, angle_range_);
    return true;
}

void DDSWbLidar::onPublish()
{
    const webots::LidarPoint *points_ptr = lidar_->getPointCloud();
    if (wb_lidar_name_ == "laser_top") {
        laser_node_copy_ = super_lidar_->getFromDef("laser_top");
        lidar_pose_copy_ = getLidarPose();
    }

    pcl::PointCloud<PointXYZIRT>::Ptr point_cloud(new pcl::PointCloud<PointXYZIRT>);
    point_cloud->header.frame_id = link_name_;
    point_cloud->height = 1;
    // point_cloud->header.stamp = static_cast<uint64_t>(points_ptr->time * 1e6);
    int points_cnt = 0;
    double tmp_stamp = points_ptr->time;

    // 计算角度范围
    double half_angle_range = angle_range_ / 2.0;

    while (points_cnt < lidar_->getNumberOfPoints()) {
        PointXYZIRT point;
        if (std::isinf(points_ptr->x) || std::isinf(points_ptr->y) || std::isinf(points_ptr->z)) {
            points_ptr++;
            points_cnt++;
            continue;
        }

        point.x = points_ptr->x;
        point.y = points_ptr->y;
        point.z = points_ptr->z;
        point.intensity = 100.0;
        point.ring = points_ptr->layer_id;
        point.time = points_ptr->time;

        // 角度过滤逻辑
        if (filter_by_angle_) {
            // 计算点相对于雷达坐标系的角度（绕Z轴）
            double angle = std::atan2(points_ptr->y, points_ptr->x);

            // 将角度范围调整到[-π, π]
            if (angle < -M_PI) angle += 2 * M_PI;
            if (angle > M_PI) angle -= 2 * M_PI;

            // 检查是否在指定角度范围内（相对于Y轴±fieldOfView/2）
            // Y轴对应角度π/2，所以范围是[π/2 - half_angle_range, π/2 + half_angle_range]
            double min_angle = M_PI / 2.0 - half_angle_range;
            double max_angle = M_PI / 2.0 + half_angle_range;

            // 处理角度跨越±π的情况
            bool in_range = false;
            if (min_angle < -M_PI) {
                // 范围跨越-π边界
                in_range = (angle >= min_angle + 2 * M_PI) || (angle <= max_angle);
            } else if (max_angle > M_PI) {
                // 范围跨越π边界
                in_range = (angle >= min_angle) || (angle <= max_angle - 2 * M_PI);
            } else {
                // 正常范围
                in_range = (angle >= min_angle) && (angle <= max_angle);
            }

            if (!in_range) {
                points_ptr++;
                points_cnt++;
                continue;
            }
        }

        if (points_ptr->time - 0.0 < 1e-6) {
            points_ptr++;
            points_cnt++;
            continue;
        } else {
            if (points_ptr->time < tmp_stamp) {
                tmp_stamp = points_ptr->time;
            }
            point_cloud->points.push_back(point);
        }
        points_ptr++;
        points_cnt++;
    }

    // 需要用相对时间而不是绝对时间
    for (size_t i = 0; i < point_cloud->points.size(); ++i) {
        point_cloud->points[i].time -= tmp_stamp;
    }
    point_cloud->header.stamp = static_cast<uint64_t>(tmp_stamp * 1e6);
    point_cloud->width = point_cloud->points.size();

    sensor_msgs::msg::PointCloud2 pc_msg;
    pcl::toROSMsg(*point_cloud, pc_msg);
    pc_writer_->Write(pc_msg);
    if (wb_lidar_name_ == "laser_top") {
        uint32_t sec = static_cast<uint32_t>(tmp_stamp);
        uint32_t nanosec = static_cast<uint32_t>((tmp_stamp - sec) * 1e9);
        nav_msgs::msg::Odometry lidar_ground_truth_msg;
        lidar_ground_truth_msg.header.frame_id = "world";
        // lidar_ground_truth_msg.header.stamp = unav::Time().fromSec(tmp_stamp);
        lidar_ground_truth_msg.header.stamp.sec = sec;
        lidar_ground_truth_msg.header.stamp.nanosec = nanosec;
        lidar_ground_truth_msg.pose = lidar_pose_copy_;
        copy_lidar_pose_writer_->Write(lidar_ground_truth_msg);
        // ULOGI("gt time : %f, lidar time : %f",
        //       lidar_ground_truth_msg.header.stamp.sec + lidar_ground_truth_msg.header.stamp.nanosec * 1e-9,
        //       tmp_stamp * 1e6);
    }
}
bool DDSWbLidar::isStepReady()
{
    if (!inited_) return false;

    const webots::LidarPoint *points_ptr = lidar_->getPointCloud();
    if (last_time_ == points_ptr->time) return false;
    last_time_ = points_ptr->time;
    return true;
}

geometry_msgs::msg::PoseWithCovariance DDSWbLidar::getLidarPose()
{
    const double *lidar_pose = laser_node_copy_->getPose();
    Eigen::Matrix4f matrix;
    matrix << lidar_pose[0], lidar_pose[1], lidar_pose[2], lidar_pose[3], lidar_pose[4], lidar_pose[5], lidar_pose[6],
        lidar_pose[7], lidar_pose[8], lidar_pose[9], lidar_pose[10], lidar_pose[11], lidar_pose[12], lidar_pose[13],
        lidar_pose[14], lidar_pose[15];

    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    geometry_msgs::msg::PoseWithCovariance pose_with_cov;
    pose_with_cov.pose.position.x = position[0];
    pose_with_cov.pose.position.y = position[1];
    pose_with_cov.pose.position.z = position[2];
    pose_with_cov.pose.orientation.x = quaternion.x();
    pose_with_cov.pose.orientation.y = quaternion.y();
    pose_with_cov.pose.orientation.z = quaternion.z();
    pose_with_cov.pose.orientation.w = quaternion.w();

    return pose_with_cov;
}
