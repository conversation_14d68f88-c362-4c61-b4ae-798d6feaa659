#include "wb_road_line.hpp"

#include <pcl_conversions/pcl_conversions.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/passthrough.h>
#include <ulog/ulog.h>

using namespace webots;

#include <cmath>

DDSWbRoadLine::DDSWbRoadLine(webots::Robot *robot, int step, const UParam &param) :
    DDSWbSensor("road_line", robot, step, param)
{
    supervisor_ = (webots::Supervisor *)robot;
}

bool DDSWbRoadLine::onInit()
{
    param_.param("topic_name", topic_name_, std::string("/road_lines/pointcloud"));
    param_.param("link_name", link_name_, std::string("world"));
    param_.param("enable_lane_map_save", enable_lane_map_save_, true);
    param_.param("enable_lane_cloud_pub", enable_lane_cloud_pub_, true);
    param_.param("need_barrier", need_barrier_, true);
    param_.param("lane_map_dir", lane_map_dir_, std::string("/home/<USER>/workspace/uautopilot_simulation/lane_map.pcd"));

    pc_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::PointCloud2>>(topic_name_);

    // gather road line nodes once
    if (!supervisor_) {
        printf("DDSWbRoadLine::onInit - ERROR: Could not find supervisor_");
        return false;
    } else {
        if (enable_lane_map_save_) {
            ULOGW("Start extracting lane lines");
            const Node *root = supervisor_->getRoot();
            if (root) findRoadSegments(root);
            pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
            for (const auto *seg : cached_road_segment_nodes_) {
                extractLaneLinesInSegment(seg, cloud, need_barrier_);
            }

            for (const auto *seg : cached_curved_segment_nodes_) {
                extractLaneCurvedInSegment(seg, cloud, need_barrier_);  // 和extractLaneLinesInSegment类似提取弯道车道线
            }
            ULOGW("Extracted %zu points from road segments", cloud->size());
            // 保存cloud点云为pcd
            pcl::io::savePCDFileBinary(lane_map_dir_, *cloud);
            ULOGW("Finished saving PCD file");
        }
    }
    if (enable_lane_cloud_pub_) {
        lane_map_ptr = std::make_shared<pcl::PointCloud<pcl::PointXYZINormal>>();
        if (pcl::io::loadPCDFile<pcl::PointXYZINormal>(lane_map_dir_, *lane_map_ptr) == -1) {
            PCL_ERROR("Failed to read PCD file from %s\n", lane_map_dir_.c_str());
            return false;
        }
        main_robot_node_ = supervisor_->getFromDef("WEBOTS_VEHICLE0");  // 确保这里使用正确的DEF名称

        // 构建道路网络
        // buildRoadNetwork();
    }
    return true;
}

geometry_msgs::msg::Pose DDSWbRoadLine::getRobotPose()
{
    geometry_msgs::msg::Pose pose;
    if (!main_robot_node_) return pose;

    const double *car_pose = main_robot_node_->getPose();
    Eigen::Matrix4f matrix;
    matrix << car_pose[0], car_pose[1], car_pose[2], car_pose[3], car_pose[4], car_pose[5], car_pose[6], car_pose[7],
        car_pose[8], car_pose[9], car_pose[10], car_pose[11], car_pose[12], car_pose[13], car_pose[14], car_pose[15];

    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    pose.position.x = position[0];
    pose.position.y = position[1];
    pose.position.z = position[2];
    pose.orientation.x = quaternion.x();
    pose.orientation.y = quaternion.y();
    pose.orientation.z = quaternion.z();
    pose.orientation.w = quaternion.w();

    return pose;
}

void DDSWbRoadLine::buildRoadNetwork()
{
    ULOGI("Building road network");
    road_segments_.clear();

    // 遍历所有道路段，构建网络
    for (const auto *node : cached_road_segment_nodes_) {
        RoadSegmentInfo segment;
        segment.node = node;
        segment.is_curved = false;

        // 获取段的基本信息
        const Field *lengthField = node->getField("length");
        const Field *transField = node->getField("translation");
        const Field *rotField = node->getField("rotation");

        if (lengthField && transField && rotField) {
            segment.length = lengthField->getSFFloat();
            const double *t = transField->getSFVec3f();
            const double *r = rotField->getSFRotation();

            // 计算起点和终点坐标
            double yaw = r[3];
            if (fabs(r[0]) < 1e-6 && fabs(r[1]) < 1e-6 && fabs(r[2] - 1.0) < 1e-6) {
                yaw = r[3];  // 确实绕 z 轴旋转
            } else {
                // 否则需要把轴角转成欧拉角
                Eigen::AngleAxisd aa(r[3], Eigen::Vector3d(r[0], r[1], r[2]));
                Eigen::Matrix3d R = aa.toRotationMatrix();
                double yaw = atan2(R(1, 0), R(0, 0));
            }
            segment.start_x = t[0];
            segment.start_y = t[1];
            segment.end_x = t[0] + segment.length * cos(yaw);
            segment.end_y = t[1] + segment.length * sin(yaw);
        }
        road_segments_.push_back(segment);
    }

    // 处理弯道段
    for (const auto *node : cached_curved_segment_nodes_) {
        RoadSegmentInfo segment;
        segment.node = node;
        segment.is_curved = true;

        const Field *radiusField = node->getField("curvatureRadius");
        const Field *angleField = node->getField("totalAngle");
        const Field *transField = node->getField("translation");

        if (radiusField && angleField && transField) {
            double radius = radiusField->getSFFloat();
            double angle = angleField->getSFFloat();
            const double *t = transField->getSFVec3f();
            // segment.R_center = radius;
            // segment.totalAngle = angle;

            segment.length = fabs(radius * angle);
            segment.start_x = t[0];
            segment.start_y = t[1];

            // 计算弯道终点坐标
            double end_angle = angle;
            segment.end_x = t[0] + radius * sin(end_angle);
            segment.end_y = t[1] + radius * (1 - cos(end_angle));
        }
        road_segments_.push_back(segment);
    }
    const double CONNECTION_THRESHOLD = 40;  // 阈值
    for (auto &seg1 : road_segments_) {
        for (auto &seg2 : road_segments_) {
            if (&seg1 == &seg2) continue;

            // 检查端点距离
            double d1 = hypot(seg1.end_x - seg2.start_x, seg1.end_y - seg2.start_y);
            double d2 = hypot(seg1.start_x - seg2.end_x, seg1.start_y - seg2.end_y);

            if (d1 < CONNECTION_THRESHOLD || d2 < CONNECTION_THRESHOLD) {
                seg1.connections.push_back(seg2.node);
            }
        }
    }
    ULOGI("Road network built with %zu segments", road_segments_.size());
}

RoadSegmentInfo *DDSWbRoadLine::findCurrentSegment(const geometry_msgs::msg::Pose &pose)
{
    RoadSegmentInfo *current_segment = nullptr;

    for (auto &segment : road_segments_) {
        if (segment.is_curved) {
            // ---- 处理曲线段 ----
            const Field *radiusField = segment.node->getField("curvatureRadius");
            const Field *angleField = segment.node->getField("totalAngle");
            const Field *transField = segment.node->getField("translation");
            const Field *rotField = segment.node->getField("rotation");
            const Field *widthField = segment.node->getField("width");

            if (radiusField && angleField && transField && rotField && widthField) {
                // ULOGI("Curved segment found");
                double radius = radiusField->getSFFloat();
                double angle = angleField->getSFFloat();
                double width = widthField->getSFFloat();
                const double *t = transField->getSFVec3f();
                const double *r = rotField->getSFRotation();

                // 圆心坐标
                double cx = t[0];
                double cy = t[1];

                // 世界坐标转局部
                double theta = r[2] * r[3];
                ;  // Z 轴旋转角
                double dx = pose.position.x - cx;
                double dy = pose.position.y - cy;

                double local_x = dx * cos(-theta) - dy * sin(-theta);
                double local_y = dx * sin(-theta) + dy * cos(-theta);

                double dist_to_center = hypot(local_x, local_y);
                double point_angle = atan2(local_y, local_x);
                if (point_angle < 0) point_angle += 2 * M_PI;

                // 判断是否落在弧段范围
                if (point_angle >= 0 && point_angle <= angle && dist_to_center >= (radius - width / 2.0) &&
                    dist_to_center <= (radius + width / 2.0)) {
                    current_segment = &segment;
                    // ULOGI("Current segment curved found , t [%f, %f, %f]", t[0], t[1], t[2]);
                }
            }

        } else {
            // continue;
            // ---- 处理直线段 ----
            double dx = segment.end_x - segment.start_x;
            double dy = segment.end_y - segment.start_y;
            double length = hypot(dx, dy);
            const Field *widthField = segment.node->getField("width");
            double width = widthField->getSFFloat();
            if (length > 0) {
                // 单位方向向量
                double ux = dx / length;
                double uy = dy / length;

                // 起点到车的向量
                double px = pose.position.x - segment.start_x;
                double py = pose.position.y - segment.start_y;

                // 投影长度
                double proj_len = px * ux + py * uy;

                if (proj_len >= 0 && proj_len <= length) {
                    // 计算垂直距离
                    double perp_dist = fabs(-uy * px + ux * py);
                    if (perp_dist <= width / 2.0) {
                        current_segment = &segment;
                    }
                }
            }
        }
    }

    return current_segment;
}

std::vector<RoadSegmentInfo *> DDSWbRoadLine::getConnectedSegments(const RoadSegmentInfo *segment)
{
    std::vector<RoadSegmentInfo *> connected;
    if (!segment) return connected;

    for (auto &other_segment : road_segments_) {
        // 检查是否在连接列表中
        auto it = std::find(segment->connections.begin(), segment->connections.end(), other_segment.node);
        if (it != segment->connections.end()) {
            connected.push_back(&other_segment);
        }
    }
    return connected;
}

double DDSWbRoadLine::calculateDistanceAlongRoad(const RoadSegmentInfo *segment, const geometry_msgs::msg::Point &point,
                                                 bool from_start)
{
    if (!segment) return 0.0;

    if (segment->is_curved) {
        // 对于弯道
        const Field *transField = segment->node->getField("translation");
        const Field *radiusField = segment->node->getField("curvatureRadius");
        const Field *rotField = segment->node->getField("rotation");
        const Field *angleField = segment->node->getField("totalAngle");

        if (!transField || !radiusField || !rotField || !angleField) return 0.0;

        const double *t = transField->getSFVec3f();
        const double radius = radiusField->getSFFloat();
        const double *r = rotField->getSFRotation();
        double cx = t[0];
        double cy = t[1];
        double start_angle = atan2(segment->start_y - cy, segment->start_x - cx);
        double total_angle = angleField->getSFFloat();
        double local_x = point.x - t[0];
        double local_y = point.y - t[1];

        // 点的相对角度
        double point_angle = atan2(local_y, local_x) - start_angle;
        point_angle = fmod(point_angle + 2 * M_PI, 2 * M_PI);  // 归一化

        // 判断是否在范围内
        // if (point_angle >= 0 && point_angle <= total_angle) {
        //     // 弧长 = 半径 * 角度
        //     double dist = radius * point_angle;
        // }

        // 计算弧长
        ULOGI("point angle from start [%f],total angle [%f], dis: [%f]", point_angle, total_angle,
              std::abs(radius * point_angle));
        return from_start ? std::abs(radius * point_angle) : std::abs(radius * (total_angle - point_angle));
    } else {
        // 对于直道
        double dx = segment->end_x - segment->start_x;
        double dy = segment->end_y - segment->start_y;
        double length = hypot(dx, dy);

        if (length > 0) {
            // 计算点在线段上的投影位置
            double t = ((point.x - segment->start_x) * dx + (point.y - segment->start_y) * dy) / (length * length);
            t = std::clamp(t, 0.0, 1.0);

            return from_start ? t * length : (1.0 - t) * length;
        }
    }
    return 0.0;
}

void DDSWbRoadLine::extractSegmentPoints(const RoadSegmentInfo *segment, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud,
                                         double start_dist, double end_dist)
{
    if (!segment || !cloud) return;

    // 确保起始距离和结束距离在有效范围内
    start_dist = std::max(0.0, start_dist);
    end_dist = std::min(segment->length, end_dist);

    if (start_dist >= end_dist) return;

    if (segment->is_curved) {
        const Field *radiusField = segment->node->getField("curvatureRadius");
        const Field *angleField = segment->node->getField("totalAngle");

        if (!radiusField || !angleField) return;

        double radius = radiusField->getSFFloat();
        double total_angle = angleField->getSFFloat();

        // 将距离转换为角度
        double start_angle = start_dist / radius;
        double end_angle = end_dist / radius;

        // 限制角度范围
        start_angle = std::max(0.0, std::min(total_angle, start_angle));
        end_angle = std::max(0.0, std::min(total_angle, end_angle));

        extractLaneCurvedInSegment(segment->node, cloud, true);
    } else {
        extractLaneLinesInSegment(segment->node, cloud, true);
    }
}

void DDSWbRoadLine::transformPointCloudToBaselink(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &input_cloud,
                                                  pcl::PointCloud<pcl::PointXYZINormal>::Ptr &output_cloud,
                                                  const geometry_msgs::msg::Pose &robot_pose)
{
    output_cloud->clear();

    Eigen::Affine3f transform = Eigen::Affine3f::Identity();

    transform.translation() << robot_pose.position.x, robot_pose.position.y, robot_pose.position.z;
    Eigen::Quaternionf q(robot_pose.orientation.w, robot_pose.orientation.x, robot_pose.orientation.y,
                         robot_pose.orientation.z);
    transform.rotate(q);

    Eigen::Affine3f inverse_transform = transform.inverse();

    for (const auto &point : input_cloud->points) {
        Eigen::Vector3f pt_world(point.x, point.y, point.z);
        Eigen::Vector3f pt_base = inverse_transform * pt_world;

        pcl::PointXYZINormal transformed_point;
        transformed_point.x = pt_base.x();
        transformed_point.y = pt_base.y();
        transformed_point.z = pt_base.z();
        transformed_point.intensity = point.intensity;
        transformed_point.normal_x = point.normal_x;
        transformed_point.normal_y = point.normal_y;
        transformed_point.normal_z = point.normal_z;

        output_cloud->points.push_back(transformed_point);
    }

    // output_cloud->width = output_cloud->points.size();
    // output_cloud->height = 1;
    // output_cloud->is_dense = true;
}

pcl::PointCloud<pcl::PointXYZINormal>::Ptr DDSWbRoadLine::transformCloudToBaseLink(
    const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &world_cloud, const geometry_msgs::msg::Pose &robot_pose)
{
    if (!world_cloud || world_cloud->empty()) {
        PCL_ERROR("Input cloud is empty!");
        return nullptr;
    }

    // 机器人在世界坐标系下的位置和姿态
    Eigen::Vector3d pos(robot_pose.position.x, robot_pose.position.y, robot_pose.position.z);
    Eigen::Quaterniond q(robot_pose.orientation.w, robot_pose.orientation.x, robot_pose.orientation.y,
                         robot_pose.orientation.z);
    Eigen::Matrix3d R = q.toRotationMatrix();

    pcl::PointCloud<pcl::PointXYZINormal>::Ptr baselink_cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
    baselink_cloud->reserve(world_cloud->size());

    for (const auto &pt : world_cloud->points) {
        Eigen::Vector3d world_point(pt.x, pt.y, pt.z);
        Eigen::Vector3d world_normal(pt.normal_x, pt.normal_y, pt.normal_z);

        // 将世界坐标系下的点转换到baselink坐标系
        Eigen::Vector3d baselink_point = R.transpose() * (world_point - pos);

        pcl::PointXYZINormal pt_baselink;
        pt_baselink.x = baselink_point.x();
        pt_baselink.y = baselink_point.y();
        pt_baselink.z = baselink_point.z();
        pt_baselink.intensity = pt.normal_z * 10.0;
        pt_baselink.normal_x = pt.normal_x;
        pt_baselink.normal_y = pt.normal_y;
        pt_baselink.normal_z = pt.normal_z;

        baselink_cloud->push_back(pt_baselink);
    }

    baselink_cloud->width = baselink_cloud->points.size();
    baselink_cloud->height = 1;
    baselink_cloud->is_dense = true;

    return baselink_cloud;
}

pcl::PointCloud<pcl::PointXYZINormal>::Ptr DDSWbRoadLine::extractFrontAreaCloud(
    const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &cloud_map, const geometry_msgs::msg::Pose &pose,
    double forward_dist, double backward_dist, double side_range, double voxel_leaf_size)
{
    if (!cloud_map || cloud_map->empty()) {
        PCL_ERROR("Input map is empty!");
        return nullptr;
    }

    // 降采样
    // pcl::PointCloud<pcl::PointXYZINormal>::Ptr filtered_map = cloud_map;
    // if (voxel_leaf_size > 0.0)
    // {
    //     pcl::VoxelGrid<pcl::PointXYZINormal> vg;
    //     vg.setInputCloud(cloud_map);  // 修复：使用cloud_map而不是cloud_out
    //     vg.setLeafSize(voxel_leaf_size, voxel_leaf_size, voxel_leaf_size);
    //     filtered_map.reset(new pcl::PointCloud<pcl::PointXYZINormal>());
    //     vg.filter(*filtered_map);
    // }
    Eigen::Vector3d pos(pose.position.x, pose.position.y, pose.position.z);
    Eigen::Quaterniond q(pose.orientation.w, pose.orientation.x, pose.orientation.y, pose.orientation.z);
    Eigen::Matrix3d R_transpose = q.toRotationMatrix().transpose();

    // 滤波器进行粗筛选
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr rough_filtered(new pcl::PointCloud<pcl::PointXYZINormal>());

    // 计算大致边界框
    double max_dist = std::max(forward_dist, backward_dist) + side_range;

    pcl::PassThrough<pcl::PointXYZINormal> pass_x, pass_y;

    pass_x.setInputCloud(cloud_map);
    pass_x.setFilterFieldName("x");
    pass_x.setFilterLimits(pos.x() - backward_dist - max_dist, pos.x() + forward_dist + max_dist);
    pass_x.filter(*rough_filtered);

    pass_y.setInputCloud(rough_filtered);
    pass_y.setFilterFieldName("y");
    pass_y.setFilterLimits(pos.y() - side_range - max_dist, pos.y() + side_range + max_dist);
    pass_y.filter(*rough_filtered);

    pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud_out(new pcl::PointCloud<pcl::PointXYZINormal>());
    cloud_out->reserve(rough_filtered->size() / 4);

    const auto &points = rough_filtered->points;
    for (size_t i = 0; i < points.size(); ++i) {
        const auto &pt = points[i];
        // ULOGI("point normal_z : %f", pt.normal_z);
        if (!(std::abs(pt.normal_z - 0.0) < 1e-3) && !(std::abs(pt.normal_z - 1.0) < 1e-3)) continue;

        double dx = pt.x - pos.x();
        double dy = pt.y - pos.y();
        double dz = pt.z - pos.z();

        // 转换到局部坐标系（手动矩阵乘法，避免Eigen开销）
        double x_local = R_transpose(0, 0) * dx + R_transpose(0, 1) * dy + R_transpose(0, 2) * dz;
        double y_local = R_transpose(1, 0) * dx + R_transpose(1, 1) * dy + R_transpose(1, 2) * dz;

        if ((x_local >= 0.0 && x_local <= forward_dist) ||  // 前方区域
            (x_local <= 0.0 && x_local >= -backward_dist))  // 后方区域
        {
            if (y_local >= -side_range && y_local <= side_range)  // 左右范围
            {
                cloud_out->push_back(pt);  // 直接拷贝，避免重新赋值
            }
        }
    }

    // cloud_out->width = cloud_out->points.size();
    // cloud_out->height = 1;
    // cloud_out->is_dense = true;

    return cloud_out;
}

// 提取直道段的车道线，中心线为double，两侧为dashed，中心dashed线不生成
void DDSWbRoadLine::extractLaneLinesInSegment(const webots::Node *segment_node,
                                              pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud, bool need_barrier)
{
    if (!segment_node) return;

    // === 读取必要字段 ===
    const Field *transField = segment_node->getField("translation");
    const Field *rotField = segment_node->getField("rotation");
    const Field *lengthField = segment_node->getField("length");
    const Field *widthField = segment_node->getField("width");
    const Field *lanesField = segment_node->getField("numberOfLanes");

    if (!transField || !rotField || !lengthField || !widthField || !lanesField) return;

    const double *t = transField->getSFVec3f();
    const double *r = rotField->getSFRotation();
    double length = lengthField->getSFFloat();
    double width = widthField->getSFFloat();
    int numberOfLanes = lanesField->getSFInt32();

    if (!t || !r || numberOfLanes < 1) return;

    int lanes = std::max(1, numberOfLanes);
    double lane_width = width / lanes;

    const double yaw = r[2] * r[3];
    const double cos_y = std::cos(yaw);
    const double sin_y = std::sin(yaw);

    const double dash_len_m = 2.0;  // 虚线段长度 (m)
    const double gap_len_m = 2.0;   // 虚线间隔 (m)
    const double period_m = dash_len_m + gap_len_m;
    const double sample_ds = 0.05;  // 采样步长 (m)

    int sampleN = std::max(2, static_cast<int>(std::ceil(length / sample_ds)));

    // === 点投影函数 ===
    auto emitPoint = [&](double x_local, double y_local, int type_idx, int lane_idx) {
        pcl::PointXYZINormal pt;
        pt.x = t[0] + cos_y * x_local - sin_y * y_local;
        pt.y = t[1] + sin_y * x_local + cos_y * y_local;
        pt.z = t[2];
        SemanticType semantic_type = semantic_label.find(static_cast<uint16_t>(type_idx)) == semantic_label.end()
                                         ? SemanticType::NONE
                                         : semantic_label.at(static_cast<uint16_t>(type_idx));
        switch (semantic_type) {
            case SemanticType::LANE:
                pt.intensity = 1.0f;
                break;
            case SemanticType::ROAD_MARKER:
                pt.intensity = 2.0f;
                break;
            case SemanticType::ROAD_SIGN:
                pt.intensity = 3.0f;
                break;
            case SemanticType::CROSSING:
                pt.intensity = 4.0f;
                break;
            case SemanticType::STOPLINE:
                pt.intensity = 5.0f;
                break;
            case SemanticType::POLE:
                pt.intensity = 6.0f;
                break;
            case SemanticType::SURF:
                pt.intensity = 7.0f;
                break;
            case SemanticType::GROUND:
                pt.intensity = 8.0f;
                break;
            default:
                ULOGW("Unknown semantic type");
                break;
        }
        pt.normal_y = type_idx;
        pt.normal_z = lane_idx;
        cloud->push_back(pt);
    };

    for (int b = 0; b <= lanes; ++b) {
        if (!need_barrier) {
            if (b == 0 || b == lanes) continue;
        }

        double offset = -width / 2.0 + b * lane_width;  // 以道路中心为 0 偏移
        bool is_center = std::abs(offset) < 1e-6;
        bool is_center_dashed = (lanes % 2 == 0) && (b == lanes / 2);

        if (is_center) {
            // --- 中心双实线 ---
            const double d = 0.20;  // 双实线间距 (m)
            const double half_d = d * 0.5;

            for (int side = -1; side <= 1; side += 2) {
                double offset_line = offset + side * half_d;
                for (int i = 0; i <= sampleN; ++i) {
                    double s = (static_cast<double>(i) / sampleN) * length;
                    emitPoint(s, offset_line, 126, lanes - b);
                }
            }
        } else {
            if (is_center_dashed) continue;  // 中心虚线不绘制

            // --- 虚线 ---
            for (int i = 0; i <= sampleN; ++i) {
                double s = (static_cast<double>(i) / sampleN) * length;

                double phase = std::fmod(s, period_m);
                if (phase < dash_len_m) {
                    emitPoint(s, offset, 125, lanes - b);
                }
            }
        }
    }
}

// 弯道段车道线：中心线为 double，两侧为 dashed；中心 dashed 不生成
void DDSWbRoadLine::extractLaneCurvedInSegment(const webots::Node *segment_node,
                                               pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud, bool need_barrier)
{
    if (!segment_node) return;

    // 读取字段
    const Field *transField = segment_node->getField("translation");
    const Field *rotField = segment_node->getField("rotation");
    const Field *widthField = segment_node->getField("width");
    const Field *lanesField = segment_node->getField("numberOfLanes");
    const Field *radiusField = segment_node->getField("curvatureRadius");
    const Field *angleField = segment_node->getField("totalAngle");
    const Field *subdivField = segment_node->getField("subdivision");
    if (!transField || !rotField || !widthField || !lanesField || !radiusField || !angleField) return;

    const double *t = transField->getSFVec3f();
    const double *r = rotField->getSFRotation();
    if (!t || !r) return;

    const double width = widthField->getSFFloat();
    const int lanes_cnt = std::max(1, lanesField->getSFInt32());
    const double R_center = radiusField->getSFFloat();   // > 0
    const double totalAngle = angleField->getSFFloat();  // 可正可负
    int subdivision = subdivField ? subdivField->getSFInt32() : 16;
    if (subdivision < 1) subdivision = 16;

    const double lane_width = width / lanes_cnt;

    const double yaw = r[2] * r[3];
    const double cos_y = std::cos(yaw);
    const double sin_y = std::sin(yaw);

    // dashed 参数（单位：米）
    const double dash_len_m = 2.0;
    const double gap_len_m = 2.0;
    const double period_m = dash_len_m + gap_len_m;

    // 采样密度（按外侧弧长决定）
    const double sample_ds = 0.05;  // 每 5 cm 一点
    int N = std::max(subdivision, (int)std::ceil(std::abs(totalAngle) * (R_center + 0.5 * width) / sample_ds));
    N = std::clamp(N, 32, 4096);

    // 在本地圆弧坐标（圆心在(0,0)）上发点；s_offset 为“左为正”的横向偏移（米）
    auto emitPoint = [&](double s_offset, double theta, int type_idx, int lane_idx) {
        const double R_line = R_center + s_offset;  // 关键：平行圆 => 只改半径
        if (R_line <= 1e-6) return;                 // 守卫

        // 与 Webots CurvedRoadSegment 保持一致的参数化：
        // wayPoint = (R' * sinθ, R' * cosθ, 0)
        const double x_local = R_line * std::sin(theta);  // 沿 +X 切向
        const double y_local = R_line * std::cos(theta);  // 指向圆心的 +Y 方向起点在 +Y

        // 旋转+平移到世界系（只绕Z）
        pcl::PointXYZINormal pt;
        pt.x = t[0] + cos_y * x_local - sin_y * y_local;
        pt.y = t[1] + sin_y * x_local + cos_y * y_local;
        pt.z = t[2];
        SemanticType semantic_type = semantic_label.find(static_cast<uint16_t>(type_idx)) == semantic_label.end()
                                         ? SemanticType::NONE
                                         : semantic_label.at(static_cast<uint16_t>(type_idx));
        switch (semantic_type) {
            case SemanticType::LANE:
                pt.intensity = 1.0f;
                break;
            case SemanticType::ROAD_MARKER:
                pt.intensity = 2.0f;
                break;
            case SemanticType::ROAD_SIGN:
                pt.intensity = 3.0f;
                break;
            case SemanticType::CROSSING:
                pt.intensity = 4.0f;
                break;
            case SemanticType::STOPLINE:
                pt.intensity = 5.0f;
                break;
            case SemanticType::POLE:
                pt.intensity = 6.0f;
                break;
            case SemanticType::SURF:
                pt.intensity = 7.0f;
                break;
            case SemanticType::GROUND:
                pt.intensity = 8.0f;
                break;
            default:
                ULOGW("Unknown semantic type");
                break;
        }
        pt.normal_y = type_idx;
        pt.normal_z = lane_idx;
        ULOGI("normal_z: %f, lane: %d", pt.normal_z, lane_idx);
        cloud->push_back(pt);
    };

    // 遍历 lanes_cnt+1 条“车道分隔线”（跳过最外侧两条边缘线）
    for (int b = 0; b <= lanes_cnt; ++b) {
        if (!need_barrier) {
            if (b == 0 || b == lanes_cnt) continue;
        }
        const double s = -width * 0.5 + b * lane_width;  // 左为正
        const bool is_center_boundary = (std::abs(s) < 1e-9);
        const bool is_center_dashed = (lanes_cnt % 2 == 0) && (b == lanes_cnt / 2);

        if (is_center_boundary) {
            // 中心双实线（与中心线平行，半径 R±d/2）
            const double d = 0.20;  // 两条实线之间的间距（米）
            for (int side = -1; side <= 1; side += 2) {
                const double s_line = s + side * (0.5 * d);  // ±d/2
                for (int i = 0; i <= N; ++i) {
                    const double theta = (static_cast<double>(i) / N) * totalAngle;
                    emitPoint(s_line, theta, 126, b);
                }
            }
        } else {
            // 中心虚线不生成
            if (is_center_dashed) continue;

            // 其它均为虚线：dash/gap 按弧长（米）判断，相位与半径绑定
            const double R_line = R_center + s;
            if (R_line <= 1e-6) continue;

            for (int i = 0; i <= N; ++i) {
                const double theta = (static_cast<double>(i) / N) * totalAngle;
                const double arc_len = std::abs(theta) * R_line;  // 起点到当前的弧长
                const double phase = std::fmod(arc_len, period_m);
                if (phase < dash_len_m) emitPoint(s, theta, 125, b);
            }
        }
    }
}

void DDSWbRoadLine::findRoadSegments(const webots::Node *node)
{
    if (!node) return;
    std::string type = node->getTypeName();
    if (type == "StraightRoadSegment" || type.find("StraightRoadSegment") != std::string::npos || type == "Road") {
        cached_road_segment_nodes_.push_back(node);
    }
    if (type == "CurvedRoadSegment" || type.find("CurvedRoadSegment") != std::string::npos) {
        cached_curved_segment_nodes_.push_back(node);
    }
    // children
    const Field *childrenField = node->getField("children");
    if (childrenField) {
        int childCount = childrenField->getCount();
        for (int i = 0; i < childCount; ++i) {
            const Node *child = childrenField->getMFNode(i);
            if (child) findRoadSegments(child);
        }
    }
    // proto children
    if (node->isProto()) {
        const Field *protoChildren = node->getProtoField("children");
        if (protoChildren) {
            int protoCount = protoChildren->getCount();
            if (protoCount > 0) {
                for (int i = 0; i < protoCount; ++i) {
                    const Node *child = protoChildren->getMFNode(i);
                    if (child) findRoadSegments(child);
                }
            } else {
                const Node *child = protoChildren->getSFNode();
                if (child) findRoadSegments(child);
            }
        }
    }
}

void DDSWbRoadLine::onPublish()
{
    if (!supervisor_) return;
    static int publish_counter = 0;

    publish_counter++;
    if (publish_counter % 2 != 0) return;

    // 获取车辆当前位置
    if (enable_lane_cloud_pub_) {
        auto robot_pose = getRobotPose();
        // ULOGI("cur pose: [%f, %f, %f]", robot_pose.position.x, robot_pose.position.y, robot_pose.position.z);

        // // 找到当前所在道路段
        // auto current_segment = findCurrentSegment(robot_pose);
        // if (!current_segment){
        //     ULOGW("Current segment not found");
        //     return;
        // }

        // // 创建临时点云
        // pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZINormal>);

        // // 提取当前段的点云
        // double dist_from_start = calculateDistanceAlongRoad(current_segment, robot_pose.position);
        // extractSegmentPoints(current_segment, cloud, dist_from_start - 50.0, dist_from_start + 50.0);

        // // 获取相邻段并提取点云
        // auto connected_segments = getConnectedSegments(current_segment);
        // for (auto* segment : connected_segments) {
        //     if (segment->is_curved) {
        //         extractSegmentPoints(segment, cloud, 0, segment->length);
        //     } else {
        //         extractSegmentPoints(segment, cloud, 0, segment->length);
        //     }
        // }
        pcl::PointCloud<pcl::PointXYZINormal>::Ptr transformed_cloud(new pcl::PointCloud<pcl::PointXYZINormal>);

        auto extracted_cloud = extractFrontAreaCloud(lane_map_ptr, robot_pose, 60.0, 5.0, 15.0, 0.05);

        if (extracted_cloud && !extracted_cloud->empty()) {
            // pcl::io::savePCDFileBinary("extracted_front_area.pcd", *extracted_cloud);
            // std::cout << "Extracted cloud size: " << extracted_cloud->size() << " points.\n";
            // 转换到baselink坐标系
            // ULOGI("robot pose: [%f, %f, %f]", robot_pose.position.x, robot_pose.position.y, robot_pose.position.z);
            transformed_cloud = transformCloudToBaseLink(extracted_cloud, robot_pose);
            // 发布点云消息
            sensor_msgs::msg::PointCloud2 pc_msg;
            pcl::toROSMsg(*transformed_cloud, pc_msg);
            pc_msg.header.frame_id = "base_link";
            unav::Time timestamp;
            timestamp.fromSec(robot_->getTime());
            pc_msg.header.stamp = timestamp;
            pc_writer_->Write(pc_msg);
        } else {
            ULOGW("Extracted front area cloud is empty or failed.");
        }
    }

    // double t = supervisor_->getTime();
    // if (t == last_time_) return;
    // last_time_ = t;

    // const Node *root = supervisor_->getRoot();
    // if (root) findRoadSegments(root);
    // // 示例：遍历直道段，提取车道线
    // pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
    // ULOGW("Start extracting lane lines");
    // for (const auto *seg : cached_road_segment_nodes_) {
    //     extractLaneLinesInSegment(seg, cloud);
    // }
    // // TODO: 可扩展到curved_segment_nodes_
    // for (const auto *seg : cached_curved_segment_nodes_) {
    //     extractLaneCurvedInSegment(seg, cloud);  // 和extractLaneLinesInSegment类似提取弯道车道线
    // }
    // ULOGW("Extracted %zu points from road segments", cloud->size());
    // // 保存cloud点云为pcd
    // pcl::io::savePCDFileBinary("/home/<USER>/workspace/uautopilot_simulation/road_lines_long.pcd", *cloud);
    // ULOGW("Finished saving PCD file");
    // 发布cloud
    // if (!cloud->empty()) {
    //     sensor_msgs::msg::PointCloud2 pc_msg;
    //     pcl::toROSMsg(*cloud, pc_msg);
    //     pc_msg.header.frame_id = link_name_;
    //     unav::Time timestamp;
    //     timestamp.fromSec(robot_->getTime());
    //     pc_msg.header.stamp = timestamp;
    //     pc_writer_->Write(pc_msg);
    // }
    // 读取本地路径xyzi格式点云并发布话题
    // pcl::PointCloud<pcl::PointXYZINormal>::Ptr local_cloud(new pcl::PointCloud<pcl::PointXYZINormal>());
    // std::string local_pcd_path = "/home/<USER>/workspace/uautopilot_simulation/road_lines.pcd";
    // if (pcl::io::loadPCDFile<pcl::PointXYZINormal>(local_pcd_path, *local_cloud) == -1) {
    //     ULOGW("Failed to load local point cloud from %s", local_pcd_path.c_str());
    // } else if (!local_cloud->empty()) {
    //     sensor_msgs::msg::PointCloud2 pc_msg;
    //     pcl::toROSMsg(*local_cloud, pc_msg);

    //     pc_msg.header.frame_id = link_name_;
    //     unav::Time timestamp;
    //     timestamp.fromSec(robot_->getTime());
    //     pc_msg.header.stamp = timestamp;
    //     pc_writer_->Write(pc_msg);
    //     ULOGI("Published local point cloud with %lu points.", local_cloud->size());
    // } else {
    //     ULOGW("Local point cloud is empty, nothing to publish.");
    // }

    // if (cloud->empty()) return;
}

bool DDSWbRoadLine::isStepReady()
{
    if (!inited_) return false;
    double t = supervisor_ ? supervisor_->getTime() : 0.0;
    return t != last_time_;
}