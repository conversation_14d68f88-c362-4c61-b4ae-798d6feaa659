#include "webots_module.h"

#include "opencv2/opencv.hpp"
#include "pcl_conversions/pcl_conversions.h"
#include "uparam/uparam.h"

namespace uslam {
namespace module {
using namespace webots;
WebotsModule::WebotsModule()
{
    FLAGS_alsologtostderr = false;
    FLAGS_logtostderr = true;
    Ulog::getInstance().init("WebotsModule");

    unav::Time::init(true);

    uslam::transport::Transport::Instance()->init(uslam::transport::DDS, "WebotsModule");
}

WebotsModule::~WebotsModule()
{
    delete robot_;
    delete car_driver_;
}

bool WebotsModule::init(const std::string &config)
{
    // robot_ = new Robot();
    car_driver_ = new Car();
    timestep_ = (int)car_driver_->getBasicTimeStep();
    wb_name_ = car_driver_->getName();

    UParam param(config);
    std::vector<std::string> sensor_list_name;
    param.param("sensor_list", sensor_list_name, std::vector<std::string>());
    for (auto sensor_name : sensor_list_name) {
        auto sub_param = param.getSubParam(sensor_name);
        std::string type;
        sub_param.param("type", type, std::string(""));
        if (type == "Car") {
            auto car = std::make_unique<DDSWbCar>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(car));
        } else if (type == "Lidar") {
            auto lidar = std::make_unique<DDSWbLidar>(car_driver_, timestep_ * 10, sub_param);
            sensor_list_.push_back(std::move(lidar));
        } else if (type == "IMU") {
            auto imu = std::make_unique<DDSWbImu>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(imu));
        } else if (type == "GT") {
            auto gt = std::make_unique<DDSWbGt>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(gt));
        } else if (type == "TrafficLight") {
            auto tl = std::make_unique<DDSWbTrafficLight>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(tl));
        } else if (type == "CAMERA") {
            auto camera = std::make_unique<DDSWbCamera>(car_driver_, timestep_ * 10, sub_param);
            sensor_list_.push_back(std::move(camera));
        } else if (type == "RoadLine") {
            // RoadLine is part of the world (StraightRoadSegment lines). Use supervisor to inspect world nodes.
            // ULOGI("Creating DDSWbRoadLine__________________");
            auto roadline = std::make_unique<DDSWbRoadLine>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(roadline));
        } else if (type == "UltrasonicSensor") {
            auto ultrasonic = std::make_unique<DDSWbUltrasonic>(car_driver_, timestep_, sub_param);
            sensor_list_.push_back(std::move(ultrasonic));
        } else {
            printf("Not support sensor %s\n", type.c_str());
        }
    }

    clock_writer_ = std::make_shared<uslam::transport::Writer<rosgraph_msgs::msg::Clock>>("clock");
    for (auto &sensor : sensor_list_) sensor->init();

    return true;
}

int WebotsModule::step()
{
    return car_driver_->step();
}

void WebotsModule::sendClock(double time)
{
    unav::Time time_now;
    time_now.fromSec(time);
    unav::Time::setNow(time_now);

    rosgraph_msgs::msg::Clock clock_msg;
    clock_msg.clock.sec = time_now.sec;
    clock_msg.clock.nanosec = time_now.nsec;
    clock_writer_->Write(clock_msg);
}

void WebotsModule::update()
{
    sendClock(car_driver_->getTime());
    for (auto &sensor : sensor_list_) sensor->publishValue();
}

}  // namespace module
}  // namespace uslam
