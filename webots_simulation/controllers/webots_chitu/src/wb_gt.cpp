#include "wb_gt.hpp"

#include "utf/tf2/utils.h"
#include <dirent.h>
#include <pcl/features/moment_of_inertia_estimation.h>
#include <opencv2/core/types.hpp>
#include <opencv2/opencv.hpp>
#include <pcl/impl/point_types.hpp>
#include "json/reader.h"
#include "utf/transform_broadcaster.h"
#include "utf/utf.h"

DDSWbGt::DDSWbGt(webots::Robot *robot, int step, const UParam &param) : DDSWbSensor("gt", robot, step, param)
{
    super_robot_ = (webots::Supervisor *)robot;
}

DDSWbGt::~DDSWbGt()
{
    if (super_robot_) delete super_robot_;
}

bool DDSWbGt::onInit()
{
    std::string car_ground_truth;

    param_.param("base_robot_def", base_robot_def_, std::string(""));
    param_.param("target_def_prefix_list", target_def_prefix_list_, std::vector<std::string>());
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));
    param_.param("x_min", x_min_, -50.0);
    param_.param("x_max", x_max_, 50.0);
    param_.param("y_min", y_min_, -50.0);
    param_.param("y_max", y_max_, 50.0);
    param_.param("skip_cnt", skip_cnt_, 0);
    param_.param("car_ground_truth", car_ground_truth, std::string(""));
    param_.param<bool>("use_ground_truth_tf", use_ground_truth_tf_, false);
    param_.param<bool>("use_lidar_ground_truth", use_lidar_ground_truth_, false);

    printf(
        "DDSWbGt::onInit topic_name:%s, base_robot_def_:%s, car_ground_truth:%s, use_ground_truth_tf:%d, x_min: %f, "
        "x_max: %f, y_min: %f, "
        "y_max: %f\n",
        topic_name_.c_str(), base_robot_def_.c_str(), car_ground_truth.c_str(), use_ground_truth_tf_, x_min_, x_max_,
        y_min_, y_max_);
    car_ground_truth_writer_ =
        std::make_shared<uslam::transport::Writer<geometry_msgs::msg::PoseStamped>>(car_ground_truth);
    if (car_ground_truth_writer_) {
        ground_truth_localization_pose_writer_ =
            std::make_shared<uslam::transport::Writer<nav_msgs::msg::Odometry>>("/localization_pose_base");
    }
    if (use_ground_truth_tf_) {
        init_pose_reader_ = std::make_shared<uslam::transport::Reader<geometry_msgs::msg::PoseWithCovarianceStamped>>(
            "/uslam/hmi/initial_pose", std::bind(&DDSWbGt::initPoseCallback, this, std::placeholders::_1));
    }
    if (use_lidar_ground_truth_) {
        ground_truth_lidar_pose_writer_ =
            std::make_shared<uslam::transport::Writer<nav_msgs::msg::Odometry>>("/lidar/ground_truth");
    }

    main_robot_node_ = super_robot_->getFromDef(base_robot_def_);
    // Get the lidar node from the scene graph to access its pose
    laser_top_node_ = super_robot_->getFromDef("laser_top");
    if (!main_robot_node_) {
        ULOGW("DDSWbGt::onInit main_robot_node_ is null");
        return false;
    }
    if (!laser_top_node_) {
        ULOGW("DDSWbGt::onInit laser_top_node_ is null");
        return false;
    }

    if (super_robot_->getFromDef("OBJECT_PROCESSOR")) {
        find_object_process_ = true;
    }
    return true;
}

void DDSWbGt::pubCarGroundTruth()
{
    auto pose = getRobotPose();

    geometry_msgs::msg::PoseStamped car_ground_truth_msg;
    car_ground_truth_msg.header.frame_id = "world";
    unav::Time current_time;
    current_time.fromSec(super_robot_->getTime());
    car_ground_truth_msg.header.stamp = current_time;
    car_ground_truth_msg.pose = pose;
    car_ground_truth_writer_->Write(car_ground_truth_msg);

    if (use_ground_truth_tf_) {
        utf::TransformBroadcaster tfb("map");
        geometry_msgs::msg::TransformStamped transform;
        transform.header.frame_id = "map";
        transform.header.stamp = car_ground_truth_msg.header.stamp;
        transform.child_frame_id = "base_link";
        transform.transform.translation.x = car_ground_truth_msg.pose.position.x;
        transform.transform.translation.y = car_ground_truth_msg.pose.position.y;
        transform.transform.translation.z = car_ground_truth_msg.pose.position.z;
        transform.transform.rotation.x = car_ground_truth_msg.pose.orientation.x;
        transform.transform.rotation.y = car_ground_truth_msg.pose.orientation.y;
        transform.transform.rotation.z = car_ground_truth_msg.pose.orientation.z;
        transform.transform.rotation.w = car_ground_truth_msg.pose.orientation.w;
        tfb.sendTransform(transform);

        nav_msgs::msg::Odometry localization_psoe_msg;
        localization_psoe_msg.header.frame_id = "map";
        localization_psoe_msg.header.stamp = car_ground_truth_msg.header.stamp;
        localization_psoe_msg.child_frame_id = "base_link";
        localization_psoe_msg.pose.pose = car_ground_truth_msg.pose;
        ground_truth_localization_pose_writer_->Write(localization_psoe_msg);
    }

    if (use_lidar_ground_truth_) {
        auto lidar_pose = getLidarPose();

        nav_msgs::msg::Odometry lidar_ground_truth_msg;
        lidar_ground_truth_msg.header.frame_id = "world";
        unav::Time current_lidar_time;
        current_lidar_time.fromSec(super_robot_->getTime());
        lidar_ground_truth_msg.header.stamp = current_time;
        lidar_ground_truth_msg.pose = lidar_pose;
        ground_truth_lidar_pose_writer_->Write(lidar_ground_truth_msg);
    }
}

void DDSWbGt::onPublish()
{
    // printf("\033[1;33m Publish once update_cnt=%d\033[0m\n", update_cnt);
    pubCarGroundTruth();

    update_cnt++;
    if (update_cnt > 100) {
        if (find_object_process_) checkObjectMemory();
        update_cnt = 0;
    }
}

void DDSWbGt::initPoseCallback(const std::shared_ptr<geometry_msgs::msg::PoseWithCovarianceStamped> &msg)
{
    setRobotPose(msg->pose.pose.position.x, msg->pose.pose.position.y, tf2::getYaw(msg->pose.pose.orientation));
}

geometry_msgs::msg::Pose DDSWbGt::getRobotPose()
{
    const double *car_pose = main_robot_node_->getPose();
    Eigen::Matrix4f matrix;
    matrix << car_pose[0], car_pose[1], car_pose[2], car_pose[3], car_pose[4], car_pose[5], car_pose[6], car_pose[7],
        car_pose[8], car_pose[9], car_pose[10], car_pose[11], car_pose[12], car_pose[13], car_pose[14], car_pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    geometry_msgs::msg::Pose pose;
    pose.position.x = position[0];
    pose.position.y = position[1];
    pose.position.z = position[2];
    pose.orientation.x = quaternion.x();
    pose.orientation.y = quaternion.y();
    pose.orientation.z = quaternion.z();
    pose.orientation.w = quaternion.w();
    return pose;
}

geometry_msgs::msg::PoseWithCovariance DDSWbGt::getLidarPose()
{
    const double *lidar_pose = laser_top_node_->getPose();
    Eigen::Matrix4f matrix;
    matrix << lidar_pose[0], lidar_pose[1], lidar_pose[2], lidar_pose[3], lidar_pose[4], lidar_pose[5], lidar_pose[6],
        lidar_pose[7], lidar_pose[8], lidar_pose[9], lidar_pose[10], lidar_pose[11], lidar_pose[12], lidar_pose[13],
        lidar_pose[14], lidar_pose[15];

    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    geometry_msgs::msg::PoseWithCovariance pose_with_cov;
    pose_with_cov.pose.position.x = position[0];
    pose_with_cov.pose.position.y = position[1];
    pose_with_cov.pose.position.z = position[2];
    pose_with_cov.pose.orientation.x = quaternion.x();
    pose_with_cov.pose.orientation.y = quaternion.y();
    pose_with_cov.pose.orientation.z = quaternion.z();
    pose_with_cov.pose.orientation.w = quaternion.w();

    return pose_with_cov;
}

void DDSWbGt::setRobotPose(double pos_x, double pos_y, double pos_theta)
{
    webots::Field *translation = main_robot_node_->getField("translation");
    double pos_values[3] = {pos_x, pos_y, 0.5};
    translation->setSFVec3f(pos_values);

    webots::Field *rotation = main_robot_node_->getField("rotation");
    double rotation_values[4] = {0.0, 0.0, 1.0, pos_theta};
    rotation->setSFRotation(rotation_values);
}

long getProcessMemoryUsage(const std::string &processName)
{
    DIR *dir = opendir("/proc");
    if (!dir) {
        std::cerr << "Failed to open /proc\n";
        return -1;
    }

    struct dirent *entry;
    while ((entry = readdir(dir))) {
        // 检查目录名是否为纯数字（表示是一个进程）
        std::string dirName = entry->d_name;
        if (isdigit(dirName.c_str()[0])) {
            std::string commPath = "/proc/" + dirName + "/comm";
            std::ifstream commFile(commPath);
            if (commFile.is_open()) {
                std::string name;
                std::getline(commFile, name);
                // 去除可能的换行符
                if (!name.empty() && name.back() == '\n') {
                    name.pop_back();
                }
                if (name == processName) {
                    // 找到匹配的进程，获取内存信息
                    std::string statusPath = "/proc/" + dirName + "/status";
                    std::ifstream statusFile(statusPath);
                    std::string line;
                    while (std::getline(statusFile, line)) {
                        if (line.compare(0, 6, "VmRSS:") == 0) {
                            std::istringstream iss(line);
                            std::string ignore;
                            long mem;
                            std::string unit;
                            iss >> ignore >> mem >> unit;
                            if (unit == "kB") mem *= 1024;  // 转换为字节
                            closedir(dir);
                            return mem;
                        }
                    }
                }
            }
        }
    }

    closedir(dir);
    return -1;  // 没有找到进程或出错
}

void DDSWbGt::checkObjectMemory()
{
    long memory = getProcessMemoryUsage("object_process");
    double memory_mb = memory / 1024.0 / 1024.0;

    const double *car_velocity = main_robot_node_->getVelocity();
    double vehicle_speed = std::sqrt(car_velocity[0] * car_velocity[0] + car_velocity[1] * car_velocity[1]);

    if (memory == -1) {
        process_not_found_cnt_++;
        ULOGW("object_process not found.cnt:%d", process_not_found_cnt_);
        if (process_not_found_cnt_ > 10) {
            const auto object_node = super_robot_->getFromDef("OBJECT_PROCESSOR");
            object_node->restartController();
        }
    } else {
        process_not_found_cnt_ = 0;
    }

    if (memory_mb > 1000 && vehicle_speed < 0.01) {
        ULOGW("object_process memory is too large: %f speed: %f", memory_mb, vehicle_speed);
        const auto object_node = super_robot_->getFromDef("OBJECT_PROCESSOR");
        object_node->restartController();
    }
}