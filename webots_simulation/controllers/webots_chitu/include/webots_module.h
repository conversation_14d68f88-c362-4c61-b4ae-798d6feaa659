/**
 *
 *   @file control_manager_module.h
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2022-11-24
 *
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef WEBOTS_MODULE_H_
#define WEBOTS_MODULE_H_
#include <webots/Camera.hpp>
#include <webots/Lidar.hpp>
#include <webots/Robot.hpp>
#include <webots/vehicle/Car.hpp>
#include <webots/vehicle/Driver.hpp>

#include "geometry_msgs/msg/Twist.h"
#include "rosgraph_msgs/msg/Clock.h"
#include "sensor_msgs/msg/Image.h"
#include "sensor_msgs/msg/PointCloud2.h"
#include "umodule/module_base.h"
#include "utransport/reader.h"
#include "utransport/writer.h"
#include "wb_camera.hpp"
#include "wb_car.hpp"
#include "wb_gt.hpp"
#include "wb_imu.hpp"
#include "wb_lidar.hpp"
#include "wb_traffic_light.hpp"
#include "wb_ultrasonic.hpp"
#include "wb_road_line.hpp"

namespace uslam {
namespace module {

class WebotsModule
{
public:
    WebotsModule();
    ~WebotsModule();

    bool init(const std::string &config);
    int step();
    void update();

private:
    void sendClock(double time);
    void twistCallback(const std::shared_ptr<geometry_msgs::msg::Twist> &msg);

    webots::Robot *robot_;
    webots::Car *car_driver_;

    int timestep_;
    std::string wb_name_;

    uslam::transport::WriterPtr<rosgraph_msgs::msg::Clock> clock_writer_;

    std::vector<std::unique_ptr<DDSWbSensor>> sensor_list_;
};

}  // namespace module
}  // namespace uslam

#endif
