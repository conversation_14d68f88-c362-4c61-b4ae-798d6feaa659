#ifndef WB_LIDAR_H_
#define WB_LIDAR_H_

#include <webots/Lidar.hpp>
#include <webots/Supervisor.hpp>

#include "sensor_msgs/msg/PointCloud2.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"
#include "nav_msgs/msg/Odometry.h"

class DDSWbLidar : public DDSWbSensor
{
public:
    DDSWbLidar(webots::Robot *robot, int step, const UParam &param);
    ~DDSWbLidar();
    bool onInit() override;
    void onPublish() override;
    bool isStepReady() override;

private:
    geometry_msgs::msg::PoseWithCovariance getLidarPose();

    uslam::transport::WriterPtr<sensor_msgs::msg::PointCloud2> pc_writer_;
    uslam::transport::WriterPtr<nav_msgs::msg::Odometry> copy_lidar_pose_writer_;
    webots::Supervisor *super_lidar_;
    webots::Lidar *lidar_;
    webots::Node *laser_node_copy_;

    std::string wb_lidar_name_;
    std::string topic_name_, link_name_;
    bool filter_by_angle_;
    double angle_range_;
    double last_time_;
    geometry_msgs::msg::PoseWithCovariance lidar_pose_copy_;
};
#endif