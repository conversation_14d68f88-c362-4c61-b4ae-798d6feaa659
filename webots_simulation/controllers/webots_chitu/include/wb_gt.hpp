#ifndef WB_GT_H_
#define WB_GT_H_
#include <geometry_msgs/msg/PoseStamped.h>
#include <geometry_msgs/msg/PoseWithCovarianceStamped.h>
#include <std_msgs/msg/String.h>
#include <webots/Supervisor.hpp>
#include <webots/Lidar.hpp>

#include "nav_msgs/msg/Odometry.h"
#include "utransport/writer.h"
#include "utransport/reader.h"
#include "wb_sensor.hpp"

#include <Eigen/Core>
#include <Eigen/Geometry>

class DDSWbGt : public DDSWbSensor
{
public:
    DDSWbGt(webots::Robot *robot, int step, const UParam &param);
    ~DDSWbGt();
    bool onInit() override;
    void onPublish() override;

private:
    webots::Supervisor *super_robot_;
    webots::Node *main_robot_node_;
    webots::Node *laser_top_node_;

    std::string base_robot_def_;
    std::vector<std::string> target_def_prefix_list_;  // 需要检测的障碍物前缀
    std::string topic_name_, link_name_;
    double x_min_, x_max_, y_min_, y_max_;
    int skip_cnt_ = 0;

    int update_cnt = 0;
    // add world to map tf and world base_link  tf
    bool use_ground_truth_tf_;
    bool use_lidar_ground_truth_;

    uslam::transport::ReaderPtr<geometry_msgs::msg::PoseWithCovarianceStamped> init_pose_reader_;
    uslam::transport::WriterPtr<geometry_msgs::msg::PoseStamped> car_ground_truth_writer_;
    uslam::transport::WriterPtr<nav_msgs::msg::Odometry> ground_truth_localization_pose_writer_;
    uslam::transport::WriterPtr<nav_msgs::msg::Odometry> ground_truth_lidar_pose_writer_;

    void pubCarGroundTruth();

    void initPoseCallback(const std::shared_ptr<geometry_msgs::msg::PoseWithCovarianceStamped> &msg);

    geometry_msgs::msg::Pose getRobotPose();
    geometry_msgs::msg::PoseWithCovariance getLidarPose();
    void setRobotPose(double pos_x, double pos_y, double pos_theta);

    void checkObjectMemory();
    int process_not_found_cnt_ = 0;
    bool find_object_process_ = false;
};
#endif
