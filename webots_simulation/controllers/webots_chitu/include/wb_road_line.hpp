#ifndef WB_ROAD_LINE_H_
#define WB_ROAD_LINE_H_

#include <webots/Supervisor.hpp>

#include "sensor_msgs/msg/PointCloud2.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <geometry_msgs/msg/PoseStamped.h>
#include <geometry_msgs/msg/PoseWithCovarianceStamped.h>

struct RoadSegmentInfo {
    const webots::Node *node;
    double length;            // 段长度
    double start_x, start_y;  // 起点坐标
    double end_x, end_y;      // 终点坐标
    // double R_center;
    // double totalAngle;
    bool is_curved;
    std::vector<const webots::Node *> connections;  // 相连的道路段
};

// struct CurvedRoadSegmentInfo {
//     const webots::Node *node;
//     double R_center;
//     double totalAngle;
//     std::vector<const webots::Node *> connections;  // 相连的道路段
// };
class DDSWbRoadLine : public DDSWbSensor
{
public:
    DDSWbRoadLine(webots::Robot *robot, int step, const UParam &param);
    // ~DDSWbRoadLine();
    bool onInit() override;
    void onPublish() override;
    bool isStepReady() override;

private:
    enum class GeometryType {
        NONE = 0,
        POLE = 1,    ///< 边缘点（角点）
        SURF = 2,    ///< 平面
        GROUND = 3,  ///< 地面
    };

    enum class SemanticType {
        NONE = 0,
        LANE,         ///< 车道线
        ROAD_MARKER,  ///< 路面标记
        ROAD_SIGN,    ///< 路牌
        CROSSING,     ///< 斑马线
        STOPLINE,     ///< 停止线
        POLE,         ///< 路杆
        SURF,         ///< 平面
        GROUND,       ///< 地面
    };

    inline static const std::map<int, SemanticType> semantic_label{
        {125, SemanticType::LANE},        {126, SemanticType::LANE},        {127, SemanticType::LANE},
        {128, SemanticType::LANE},        {129, SemanticType::CROSSING},    {130, SemanticType::STOPLINE},
        {131, SemanticType::ROAD_SIGN},   {132, SemanticType::ROAD_SIGN},   {133, SemanticType::POLE},
        {134, SemanticType::ROAD_MARKER}, {135, SemanticType::ROAD_MARKER}, {136, SemanticType::ROAD_MARKER},
        {137, SemanticType::ROAD_MARKER}, {138, SemanticType::ROAD_MARKER}};

private:
    void findRoadSegments(const webots::Node *node);
    void sampleRoadLineInSegment(const webots::Node *segment_node, const webots::Node *road_line_node, int line_index,
                                 int total_lines, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud);
    void sampleCurvedRoadLineInSegment(const webots::Node *segment_node, const webots::Node *road_line_node,
                                       int line_index, int total_lines, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud);
    void extractLaneLinesInSegment(const webots::Node *segment_node, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud,
                                   bool need_barrier = false);
    void extractLaneCurvedInSegment(const webots::Node *segment_node, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud,
                                    bool need_barrier = false);

    enum RoadLineType { SOLID, DASHED, DOUBLE, UNKNOWN };
    RoadLineType parseRoadLineType(const webots::Node *road_line_node);
    geometry_msgs::msg::Pose getRobotPose();
    void transformPointCloudToBaselink(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &input_cloud,
                                       pcl::PointCloud<pcl::PointXYZINormal>::Ptr &output_cloud,
                                       const geometry_msgs::msg::Pose &robot_pose);
    void filterPointsByDistance(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &input_cloud,
                                pcl::PointCloud<pcl::PointXYZINormal>::Ptr &output_cloud,
                                const geometry_msgs::msg::Pose &robot_pose);

    void buildRoadNetwork();
    RoadSegmentInfo *findCurrentSegment(const geometry_msgs::msg::Pose &pose);
    std::vector<RoadSegmentInfo *> getConnectedSegments(const RoadSegmentInfo *segment);
    double calculateDistanceAlongRoad(const RoadSegmentInfo *segment, const geometry_msgs::msg::Point &point,
                                      bool from_start = true);
    void extractSegmentPoints(const RoadSegmentInfo *segment, pcl::PointCloud<pcl::PointXYZINormal>::Ptr cloud,
                              double start_dist, double end_dist);
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr extractFrontAreaCloud(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &cloud_map,
                                                                     const geometry_msgs::msg::Pose &pose,
                                                                     double forward_dist, double backward_dist,
                                                                     double side_range, double voxel_leaf_size);
    pcl::PointCloud<pcl::PointXYZINormal>::Ptr transformCloudToBaseLink(
        const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &world_cloud, const geometry_msgs::msg::Pose &robot_pose);

    uslam::transport::WriterPtr<sensor_msgs::msg::PointCloud2> pc_writer_;
    webots::Supervisor *supervisor_ = nullptr;

    std::string topic_name_;
    std::string link_name_;
    bool enable_lane_map_save_;
    bool enable_lane_cloud_pub_;
    std::string lane_map_dir_;
    bool need_barrier_;
    std::shared_ptr<pcl::PointCloud<pcl::PointXYZINormal>> lane_map_ptr;

    double last_time_ = 0.0;
    std::vector<const webots::Node *> cached_road_segment_nodes_;
    std::vector<const webots::Node *> cached_curved_segment_nodes_;

    std::vector<RoadSegmentInfo> road_segments_;

    pcl::PointCloud<pcl::PointXYZINormal>::Ptr full_lane_cloud_;
    const webots::Node *main_robot_node_;
    double forward_distance_;
};

#endif
