#ifndef WB_CAR_H_
#define WB_CAR_H_
#include <webots/vehicle/Car.hpp>

#include "ackermann_msgs/msg/AckermannDriveOdometry.h"
#include "ackermann_msgs/msg/AckermannDriveStamped.h"
#include "std_msgs/msg/String.h"
#include "geometry_msgs/msg/Twist.h"
#include "nav_msgs/msg/Odometry.h"
#include "utransport/reader.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"
#include "std_msgs/msg/Bool.h"
#include <mutex>

class DDSWbCar : public DDSWbSensor
{
private:
    webots::Car *car_driver_;
    std::string odom_out_topic_;
    std::string ackermann_odom_out_topic_;
    std::string twist_in_topic_;
    std::string ackermann_twist_in_topic_;
    std::string ackermann_odom_mode_topic_;

    uslam::transport::ReaderPtr<geometry_msgs::msg::Twist> twist_reader_;
    uslam::transport::ReaderPtr<ackermann_msgs::msg::AckermannDriveStamped> ackermann_twist_reader_;
    uslam::transport::ReaderPtr<std_msgs::msg::Bool> engage_reader_;
    uslam::transport::WriterPtr<nav_msgs::msg::Odometry> odom_writer_;
    uslam::transport::WriterPtr<ackermann_msgs::msg::AckermannDriveOdometry> ackermann_odom_writer_;
    uslam::transport::WriterPtr<std_msgs::msg::String> ackermann_odom_mode_writer_;

    double odom_x_ = 0.0;
    double odom_y_ = 0.0;
    double odom_theta_ = 0.0;
    double last_odom_time_ = -1;
    std::mutex engage_mutex_;
    bool engage_ = true;

    void twistCallback(const std::shared_ptr<geometry_msgs::msg::Twist> &msg);
    void ackermannTwistCallback(const std::shared_ptr<ackermann_msgs::msg::AckermannDriveStamped> &msg);
    void EngageCallback(const std::shared_ptr<std_msgs::msg::Bool> &msg);

public:
    DDSWbCar(webots::Car *car_driver, int step, const UParam &param);

    bool onInit() override;
    void onPublish() override;
};

#endif