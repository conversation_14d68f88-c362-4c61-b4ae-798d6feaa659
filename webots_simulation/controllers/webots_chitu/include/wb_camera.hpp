#ifndef WB_CAMERA_H_
#define WB_CAMERA_H_

#include <webots/Camera.hpp>

#include "sensor_msgs/msg/Image.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"
#include "opencv2/opencv.hpp"
#include "drivable_space_msgs/msg/DrivableSpace.h"

class DDSWbCamera : public DDSWbSensor
{
public:
    DDSWbCamera(webots::Robot *robot, int step, const UParam &param);
    bool onInit() override;
    void onPublish() override;

private:
    uslam::transport::WriterPtr<sensor_msgs::msg::Image> image_writer_;
    uslam::transport::WriterPtr<sensor_msgs::msg::Image> original_image_writer_;
    uslam::transport::WriterPtr<sensor_msgs::msg::Image> road_line_writer_;
    uslam::transport::WriterPtr<sensor_msgs::msg::Image> drivable_space_image_writer_;
    uslam::transport::WriterPtr<drivable_space_msgs::msg::DrivableSpace> drivable_space_writer_;
    webots::Camera *camera_;
    size_t image_count_;

    cv::Mat GetRoadLineImage(const webots::CameraRecognitionObject *head_object_ptr, int object_num,
                             const cv::Mat &camera_image);
    std::vector<std::vector<cv::Point>> GetRoadLineContours(const webots::CameraRecognitionObject *head_object_ptr,
                                                            int object_num, const cv::Mat &camera_image);
    cv::Mat GetDrivableSpaceImage(const webots::CameraRecognitionObject *head_object_ptr, int object_num,
                                  const cv::Mat &camera_image);
    std::string wb_camera_name_;
    std::string topic_name_, link_name_, original_topic_name_;
    std::string drivable_space_topic_name_, road_line_topic_name_;
    std::string drivable_space_image_topic_name_;

    bool obtain_drivable_space_msg_, obtain_road_line_msg_;
};
#endif