import math
from typing import List, <PERSON><PERSON>
import numpy as np
import matplotlib.pyplot as plt


class LineModel:
    """线段运动模型类"""

    def __init__(
        self,
        points: List[Tuple[float, float]],
        velocity: float = 1.0,
        z: float = 1.0,
        init_pos: List[float] = [0, 0, 0],
        use_spline: bool = True,
        step_time: float = 0.01,
        angular_velocity_predict_time: float = 1.0,
    ):
        """
        初始化线段运动模型

        Args:
            points: 路径点列表,每个点为 (x, y) 坐标
            velocity: 运动速度(米/秒)
            z: z轴高度
            init_pos: 初始位置
            use_spline: 是否使用三次样条插值
            spline_points: 样条插值后的点数量
        """
        self.original_points = points
        self.velocity = velocity
        self.z = z
        self.init_pos = init_pos
        self.use_spline = use_spline
        self.step_time = step_time
        self.angular_velocity_predict_time = angular_velocity_predict_time
        self.cur_distance = 0.0

        if use_spline and len(points) > 2:
            self.points = self.smooth_corners_with_bezier(points, offset_ratio=0.3)
        else:
            self.points = points

        # 计算每个线段的长度
        self.segment_lengths = []
        self.total_length = 0
        for i in range(len(self.points) - 1):
            dx = self.points[i + 1][0] - self.points[i][0]
            dy = self.points[i + 1][1] - self.points[i][1]
            length = math.sqrt(dx * dx + dy * dy)
            self.segment_lengths.append(length)
            self.total_length += length

        # self.visualize_path(save_path='spline_path.png')

    def calculate_position(self, elapsed_time: float) -> List[float]:
        """
        计算给定时间的位置

        Args:
            elapsed_time: 运行时间(秒)

        Returns:
            [x, y, z] 位置坐标
        """
        # 计算在路径上的总距离
        # self.cur_distance += self.cur_distance + self.velocity * self.step_time
        # distance = self.cur_distance % self.total_length
        # print("sel.vel:{}  self cur distance:{}  distance:{}".format(self.velocity, self.cur_distance,distance))
        distance = (self.velocity * elapsed_time) % self.total_length

        # 找到当前所在的线段
        current_distance = 0
        current_segment = 0
        for i, length in enumerate(self.segment_lengths):
            if current_distance + length > distance:
                current_segment = i
                break
            current_distance += length

        # 计算在当前线段上的位置
        segment_progress = (distance - current_distance) / self.segment_lengths[
            current_segment
        ]
        start = self.points[current_segment]
        end = self.points[current_segment + 1]

        x = start[0] + (end[0] - start[0]) * segment_progress + self.init_pos[0]
        y = start[1] + (end[1] - start[1]) * segment_progress + self.init_pos[1]
        z = self.init_pos[2]
        return [x, y, z]

    def calculate_angle(self, elapsed_time):
        """
        计算直线路径运动的旋转角度

        Args:
            elapsed_time (float): 已经过的时间(秒)

        Returns:
            float: 旋转角度(弧度)
        """
        # 获取当前位置和下一个位置
        current_pos = self.calculate_position(elapsed_time)
        next_pos = self.calculate_position(
            elapsed_time + self.step_time
        )  # 稍微往前看一点

        current_distance = (self.velocity * elapsed_time) % self.total_length
        next_distance = (
            self.velocity * (elapsed_time + self.step_time)
        ) % self.total_length
        if next_distance > current_distance:
            # 计算方向向量
            dx = next_pos[0] - current_pos[0]
            dy = next_pos[1] - current_pos[1]

            # 计算朝向角度(与x轴正方向的夹角)
            angle = math.atan2(dy, dx)
        else:
            prev_pos = self.calculate_position(elapsed_time - self.step_time)
            dx = current_pos[0] - prev_pos[0]
            dy = current_pos[1] - prev_pos[1]
            angle = math.atan2(dy, dx)
        return angle

    def calculate_velocity(self, elapsed_time):
        """
        计算直线路径运动的速度

        Args:
            elapsed_time (float): 已经过的时间(秒)

        Returns:
            float: 线速度xyz(米/秒) 角速度（弧度/秒）
        """
        current_pos = self.calculate_position(elapsed_time)
        next_pos = self.calculate_position(elapsed_time + self.step_time)

        current_distance = (self.velocity * elapsed_time) % self.total_length
        next_distance = (
            self.velocity * (elapsed_time + self.step_time)
        ) % self.total_length

        if next_distance > current_distance:
            vx = (next_pos[0] - current_pos[0]) / self.step_time
            vy = (next_pos[1] - current_pos[1]) / self.step_time
            vz = 0
        else:
            back_pos = self.points[-1]
            vx = (back_pos[0] - current_pos[0]) / self.step_time
            vy = (back_pos[1] - current_pos[1]) / self.step_time
            vz = 0

        current_angle = self.calculate_angle(elapsed_time)
        next_angle = self.calculate_angle(
            elapsed_time + self.angular_velocity_predict_time
        )

        next_distance = (
            self.velocity * (elapsed_time + self.angular_velocity_predict_time)
        ) % self.total_length
        if next_distance > current_distance:
            angular_velocity = (
                next_angle - current_angle
            ) / self.angular_velocity_predict_time
        else:
            back_time = self.total_length / self.velocity
            back_angle = self.calculate_angle(back_time)
            angular_velocity = (
                back_angle - current_angle
            ) / self.angular_velocity_predict_time
        return [vx, vy, vz, 0, 0, angular_velocity]

    @staticmethod
    def parse_points(points_str: str) -> List[Tuple[float, float]]:
        """
        解析点坐标字符串

        Args:
            points_str: 格式如 "1.0,2.0;3.0,4.0;5.0,6.0"

        Returns:
            [(x1,y1), (x2,y2), ...] 坐标点列表
        """
        try:
            points = []
            for point_str in points_str.split(";"):
                x, y = map(float, point_str.split(","))
                points.append((x, y))
            return points
        except:
            raise ValueError("点坐标格式错误,应为：x1,y1;x2,y2;x3,y3...")

    def visualize_path(self, show: bool = True, save_path: str = None):
        """
        可视化原始路径点和插值后的路径点

        Args:
            show: 是否显示图像
            save_path: 保存图像的路径，如果为None则不保存
        """
        # 创建新的图形
        plt.figure(figsize=(10, 6))

        # 绘制原始点
        original_points = np.array(self.original_points)
        plt.plot(
            original_points[:, 0],
            original_points[:, 1],
            "ro-",
            label="原始路径点",
            markersize=8,
        )

        # 绘制插值后的点
        if self.use_spline:
            interpolated_points = np.array(self.points)
            plt.plot(
                interpolated_points[:, 0],
                interpolated_points[:, 1],
                "b-",
                label="样条插值路径",
                linewidth=2,
            )

        # 设置图形属性
        plt.title("路径点可视化")
        plt.xlabel("X 坐标")
        plt.ylabel("Y 坐标")
        plt.grid(True)
        plt.legend()

        # 保持纵横比相等
        plt.axis("equal")

        # 保存图像
        if save_path:
            plt.savefig(save_path)

        # 显示图像
        if show:
            plt.show()
        else:
            plt.close()

    def quadratic_bezier(
        self, p0: np.ndarray, p1: np.ndarray, p2: np.ndarray, num_points: int = 20
    ) -> List[np.ndarray]:
        """构造二次贝塞尔曲线"""
        points = []
        for t in np.linspace(0, 1, num_points):
            p = (1 - t) ** 2 * p0 + 2 * (1 - t) * t * p1 + t**2 * p2
            points.append(p)
        return points

    def smooth_corners_with_bezier(
        self, points: List[Tuple[float, float]], offset_ratio: float = 0.3
    ) -> List[Tuple[float, float]]:
        """
        对每个折线拐角点插入贝塞尔曲线进行平滑
        :param points: 原始折线点列表
        :param offset_ratio: 控制曲线弯曲程度（越小越贴近原折线）
        :return: 平滑后的点列表
        """
        smoothed = []
        n = len(points)

        for i in range(n):
            prev_idx = max(i - 1, 0)
            next_idx = min(i + 1, n - 1)
            if i == 0 or i == n - 1:
                # 起点和终点不参与平滑
                smoothed.append(points[i])
                continue

            p_prev = np.array(points[prev_idx])
            p_curr = np.array(points[i])
            p_next = np.array(points[next_idx])

            # 计算前后方向向量
            dir_prev = p_curr - p_prev
            dir_next = p_next - p_curr

            # 单位化并缩放
            len_prev = np.linalg.norm(dir_prev)
            len_next = np.linalg.norm(dir_next)

            control_point = p_curr
            start_point = p_curr - dir_prev / len_prev * offset_ratio * len_prev
            end_point = p_curr + dir_next / len_next * offset_ratio * len_next

            # 使用二次贝塞尔曲线连接 start -> control -> end
            curve_points = self.quadratic_bezier(start_point, control_point, end_point)

            # 添加到结果中（去掉首尾点避免重复）
            smoothed.extend([tuple(p) for p in curve_points[1:-1]])

        return smoothed

    def calculate_position_varyvel(
        self, elapsed_time: float, path_length: float
    ) -> List[float]:
        """
        计算速度可变情况下的位置

        Args:
            elapsed_time: 运行时间(秒)

        Returns:
            [x, y, z] 位置坐标
        """
        # 计算在路径上的总距离
        distance = path_length % self.total_length

        # 找到当前所在的线段
        current_distance = 0
        current_segment = 0
        for i, length in enumerate(self.segment_lengths):
            if current_distance + length > distance:
                current_segment = i
                break
            current_distance += length

        # 计算在当前线段上的位置
        segment_progress = (distance - current_distance) / self.segment_lengths[
            current_segment
        ]
        start = self.points[current_segment]
        end = self.points[current_segment + 1]

        x = start[0] + (end[0] - start[0]) * segment_progress + self.init_pos[0]
        y = start[1] + (end[1] - start[1]) * segment_progress + self.init_pos[1]
        z = self.init_pos[2]
        return [x, y, z]
