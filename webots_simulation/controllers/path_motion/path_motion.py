import time
from controller import Supervisor
import math
import argparse
import sys
from circle_model import CircleModel
from polygon_model import PolygonModel
from line_model import LineModel
from typing import List
import numpy as np
import math
import matplotlib.pyplot as plt
import random


class CircularMotionRobot:
    """控制机器人进行圆周运动的类"""

    def __init__(self):
        """初始化机器人控制器"""
        self.TIME_STEP = 10
        self.supervisor = Supervisor()
        self.args = self._parse_arguments()

        # 获取机器人节点
        self.robot_node = self._init_robot()
        self.trans_field = self.robot_node.getField("translation")
        self.init_position = self._parse_init_position()
        self.simulation_time = self.supervisor.getTime()
        # 初始化位置
        start_position = [0, 0, 0]
        # 碰撞检测相关
        self.is_stopped = False  # 停止状态标志阈值(米)

        self.webots_vehicle_node = None  # WEBOTS_VEHICLE0节点引用
        self.pause_start_time = 0.0  # 开始暂停的仿真时间
        self.distance_sensor = self.supervisor.getDevice("u1")
        self.distance_sensor_2 = self.supervisor.getDevice("u2")
        self.distance_sensor_3 = self.supervisor.getDevice("u3")
        if self.distance_sensor is not None:
            self.distance_sensor.enable(self.TIME_STEP)
        if self.distance_sensor_2 is not None:
            self.distance_sensor_2.enable(self.TIME_STEP)
        if self.distance_sensor_3 is not None:
            self.distance_sensor_3.enable(self.TIME_STEP)

        self.last_yaw = 0.0
        # 绘图用
        self.module4_time = []
        self.module4_x = []
        self.module4_y = []
        self.plot_flag = 0
        self.parent_yaw = []

        # 初始化线性模型累积运行时间
        self.elapse_time_sum = 0.0

        # 根据类型创建相应的运动模型
        if self.args.type == "circle":
            self.motion_model = CircleModel(
                radius=self.args.radius,
                velocity=self.args.velocity,
                init_pos=self.init_position,
            )
        elif self.args.type == "polygon":
            if not self.args.points:
                print("未指定多边形顶点，使用默认的三角形")
                points = [(1.0, 0.0), (-0.5, 0.866), (-0.5, -0.866)]
            else:
                points = PolygonModel.parse_points(self.args.points)
            start_position = [points[0][0], points[0][1], 0]  # 使用第一个点作为初始位置
            self.motion_model = PolygonModel(
                points=points,
                velocity=self.args.velocity,
            )
        elif self.args.type == "line":
            if not self.args.points:
                print("未指定路径点，使用默认的直线")
                points = [(-1.0, 0.0), (1.0, 0.0)]
            else:
                points = LineModel.parse_points(self.args.points)
            start_position = [points[0][0], points[0][1], 0]  # 使用第一个点作为初始位置
            self.motion_model = LineModel(
                points=points,
                velocity=self.args.velocity,
            )
        else:
            print(f"暂不支持 {self.args.type} 类型的运动，使用默认的圆形运动")
            self.motion_model = CircleModel(
                radius=self.args.radius, velocity=self.args.velocity
            )
        # 设置初始位置
        self.trans_field.setSFVec3f(start_position)

    def quaternion_to_rotation_matrix(self, q):  # x, y ,z ,w
        rot_matrix = np.array(
            [
                [
                    1.0 - 2 * (q[1] * q[1] + q[2] * q[2]),
                    2 * (q[0] * q[1] - q[3] * q[2]),
                    2 * (q[3] * q[1] + q[0] * q[2]),
                ],
                [
                    2 * (q[0] * q[1] + q[3] * q[2]),
                    1.0 - 2 * (q[0] * q[0] + q[2] * q[2]),
                    2 * (q[1] * q[2] - q[3] * q[0]),
                ],
                [
                    2 * (q[0] * q[2] - q[3] * q[1]),
                    2 * (q[1] * q[2] + q[3] * q[0]),
                    1.0 - 2 * (q[0] * q[0] + q[1] * q[1]),
                ],
            ]
        )

        return rot_matrix

    def _parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="机器人运动控制器")

        parser.add_argument(
            "--type",
            type=str,
            default="circle",
            choices=["circle", "polygon", "line"],
            help="运动类型，可选：circle/polygon/line，默认circle",
        )
        parser.add_argument(
            "--radius", type=float, default=1.0, help="圆形运动半径(米)，默认1.0"
        )
        parser.add_argument(
            "--velocity", type=float, default=1.0, help="运动速度(米/秒)，默认1.0"
        )
        parser.add_argument(
            "--points", type=str, help="多边形顶点坐标，格式：x1,y1;x2,y2;x3,y3..."
        )
        parser.add_argument("--init_pos", type=str, help="xyz坐标，格式：x,y,z")

        if "--help" in sys.argv or "-h" in sys.argv:
            parser.print_help()
            sys.exit(0)

        args_str = " ".join(sys.argv[1:])
        args_list = args_str.split()

        try:
            return parser.parse_args(args_list)
        except SystemExit:
            print("参数解析错误，使用默认值")
            return parser.parse_args([])

    def _parse_init_position(self):
        """解析初始位置"""
        if self.args.init_pos:
            try:
                # 解析命令行参数中的初始位置
                x, y, z = map(float, self.args.init_pos.split(","))
                print(f"str {self.args.init_pos} init_pos: {x}, {y}, {z}")
                return [x, y, z]
            except (ValueError, AttributeError):
                return [0.0, 0.0, 0.0]

        # 如果没有指定初始位置或解析失败，使用当前位置
        return self.trans_field.getSFVec3f()

    def _init_robot(self):
        """初始化机器人节点"""
        robot_node = self.supervisor.getSelf()
        if robot_node is None:
            sys.stderr.write("No DEF MY_ROBOT node found in the current world file\n")
            sys.exit(1)
        return robot_node

    def _get_current_position(self):
        """获取当前机器人位置"""
        return self.trans_field.getSFVec3f()

    def update_position(self, elapsed_time):
        """更新机器人位置"""
        position = self.motion_model.calculate_position(elapsed_time)
        # position = self.motion_model.calculate_position(elapsed_time)

        # print("parent our position:{}    {}    {}    elasped time:{}".format(position[0],position[1],position[2], elapsed_time))
        # print(position)
        self.trans_field.setSFVec3f(position)
        # print("parent set position:{}    {}    {}    elasped time:{}".format(position[0],position[1],position[2], elapsed_time))
        # 更新机器人角度
        rotation_field = self.robot_node.getField("rotation")
        rotation_data = rotation_field.getSFRotation()
        new_angle = self.motion_model.calculate_angle(elapsed_time)
        if new_angle > -2 * np.pi and new_angle < -np.pi:
            new_angle = new_angle + 2 * np.pi
        elif new_angle > np.pi and new_angle < 2 * np.pi:
            new_angle = new_angle - 2 * np.pi
        rotation_field.setSFRotation([0, 0, 1, new_angle])

        velocity = self.motion_model.calculate_velocity(elapsed_time)

        self.robot_node.setVelocity(velocity)

        if self.robot_node.getField("description"):
            descrip_field = self.robot_node.getField("description")
            description_str = descrip_field.getSFString()
            if description_str == "Trailer":
                # 如果障碍物类型是牵引车，对其子模块进行处理
                ## 父类障碍物当前的位置(世界系)
                position = np.array(self.motion_model.calculate_position(elapsed_time))
                # 如果是牵引车，那么角速度设为0
                self.robot_node.setVelocity(
                    [velocity[0], velocity[1], velocity[2], 0, 0, 0]
                )

                # 当前点与终点的距离
                if self.args.type == "line":
                    dis_with_end = (
                        (position[0] - self.motion_model.points[-1][0]) ** 2
                        + (position[1] - self.motion_model.points[-1][1]) ** 2
                    ) ** 0.5
                    if dis_with_end < self.motion_model.velocity * 0.025:
                        self.elapse_time_sum = elapsed_time
                        # print("run here")

                elapsed_time_subed = elapsed_time - self.elapse_time_sum

                # 牵引车头位置
                position = np.array(self._get_current_position())

                # 牵引车头角度(世界系)
                rotation_data = rotation_field.getSFRotation()
                new_angle = rotation_data[3]
                # new_angle = self.motion_model.calculate_angle(elapsed_time_subed)

                velocity = self.motion_model.calculate_velocity(elapsed_time_subed)

                # 父类障碍物相对于世界的旋转矩阵
                quaternion_parent = np.array(
                    [0, 0, math.sin(new_angle / 2), math.cos(new_angle / 2)]
                )
                rot_matrix_world_b = self.quaternion_to_rotation_matrix(
                    quaternion_parent
                )
                rot_matrix_world_b_T = rot_matrix_world_b.T

                # 车头尺寸
                boundingObject_field = self.robot_node.getField("boundingObject")
                boundingObejct_node = boundingObject_field.getSFNode()
                size_field = boundingObejct_node.getField("size")
                size_data = size_field.getSFVec3f()
                length_x = size_data[0]

                # 获取牵引车车头下的节点
                children_field = self.robot_node.getField("children")

                # 时间前移(子模块的位姿是父类障碍物前若干秒的位姿)
                delay_t1 = 1.9 * length_x / self.motion_model.velocity
                delay_t2 = delay_t1 + 2.3 * length_x / self.motion_model.velocity
                delay_t3 = delay_t2 + 2.3 * length_x / self.motion_model.velocity
                delay_t4 = delay_t3 + 2.3 * length_x / self.motion_model.velocity
                delay_t5 = delay_t4 + 2.3 * length_x / self.motion_model.velocity
                delay_times = [delay_t1, delay_t2, delay_t3, delay_t4, delay_t5]
                # delay_times = [3.8, 8.4, 13.0, 17.6, 22.2]
                elapsed_time1 = elapsed_time_subed - delay_times[0]
                elapsed_time2 = elapsed_time_subed - delay_times[1]
                elapsed_time3 = elapsed_time_subed - delay_times[2]
                elapsed_time4 = elapsed_time_subed - delay_times[3]
                elapsed_time5 = elapsed_time_subed - delay_times[4]

                # 获取子模块节点
                if children_field.getCount() > 4:
                    tractor_sub_node = children_field.getMFNode(3)
                else:
                    return 0

                if elapsed_time1 > 0:
                    ## 父类障碍物之前的位姿
                    position_before = np.array(
                        self.motion_model.calculate_position(elapsed_time1)
                    )
                    new_angle_before = self.motion_model.calculate_angle(elapsed_time1)
                    # velocity_before = np.array(self.motion_model.calculate_velocity(elapsed_time))
                else:
                    position_before = np.array(self.minus_time_position(elapsed_time1))
                    angle_time1 = elapsed_time1 if self.args.type == "circle" else 0.0
                    new_angle_before = self.motion_model.calculate_angle(angle_time1)

                ## 计算两个时刻的姿差别
                d_position = position_before - position
                d_new_angle = new_angle_before - new_angle
                if d_new_angle > -2 * np.pi and d_new_angle < -np.pi:
                    d_new_angle = d_new_angle + 2 * np.pi
                elif d_new_angle > np.pi and d_new_angle < 2 * np.pi:
                    d_new_angle = d_new_angle - 2 * np.pi

                # 姿态
                rotation_field = tractor_sub_node.getField("rotation1")
                rotation_field.setSFRotation([0, 0, 1, d_new_angle])

                # 位置
                d_position = d_position.reshape((3, 1))
                d_position_b = rot_matrix_world_b_T @ d_position
                translation_field = tractor_sub_node.getField("translation1")
                translation_field.setSFVec3f(
                    [d_position_b[0], d_position_b[1], d_position_b[2]]
                )

                # # 速度
                # velocity_before = self.motion_model.calculate_velocity(elapsed_time1)
                # d_velocity = np.array(velocity_before) - np.array(velocity)
                # d_velocity_line = d_velocity[0:3]
                # d_velocity_angle = d_velocity[3:6]
                # d_velocity_line = d_velocity_line.reshape((3,1))
                # d_velocity_line_b = rot_matrix_world_b_T @ d_velocity_line
                # tractor_module1_node.setVelocity([d_velocity_line_b[0],d_velocity_line_b[1],d_velocity_line_b[2],
                #                             d_velocity_angle[0],d_velocity_angle[1],d_velocity_angle[2]])

                if elapsed_time2 > 0:
                    ## 父类障碍物之前的位姿
                    position_before = np.array(
                        self.motion_model.calculate_position(elapsed_time2)
                    )
                    new_angle_before = self.motion_model.calculate_angle(elapsed_time2)
                    # velocity_before = np.array(self.motion_model.calculate_velocity(elapsed_time))
                else:
                    position_before = np.array(self.minus_time_position(elapsed_time2))
                    angle_time2 = elapsed_time2 if self.args.type == "circle" else 0.1
                    new_angle_before = self.motion_model.calculate_angle(angle_time2)

                ## 计算两个时刻的姿差别
                d_position = position_before - position
                d_new_angle = new_angle_before - new_angle
                if d_new_angle > -2 * np.pi and d_new_angle < -np.pi:
                    d_new_angle = d_new_angle + 2 * np.pi
                elif d_new_angle > np.pi and d_new_angle < 2 * np.pi:
                    d_new_angle = d_new_angle - 2 * np.pi

                # 姿态
                rotation_field = tractor_sub_node.getField("rotation2")
                rotation_field.setSFRotation([0, 0, 1, d_new_angle])

                # 位置
                d_position = d_position.reshape((3, 1))
                d_position_b = rot_matrix_world_b_T @ d_position
                translation_field = tractor_sub_node.getField("translation2")
                translation_field.setSFVec3f(
                    [d_position_b[0], d_position_b[1], d_position_b[2]]
                )
                # print("module2 world x:{},  y:{},  yaw:{},   time:{}".format(position_before[0], position_before[1], new_angle_before,elapsed_time2))
                # print("module2  translation x:{},  y:{},  yaw:{},  time:{}".format(d_position_b[0],d_position_b[1],d_new_angle,elapsed_time2))
                # # 速度
                # velocity_before = self.motion_model.calculate_velocity(elapsed_time2)
                # d_velocity = np.array(velocity_before) - np.array(velocity)
                # d_velocity_line = d_velocity[0:3]
                # d_velocity_angle = d_velocity[3:6]
                # d_velocity_line = d_velocity_line.reshape((3,1))
                # d_velocity_line_b = rot_matrix_world_b_T @ d_velocity_line
                # tractor_module1_node.setVelocity([d_velocity_line_b[0],d_velocity_line_b[1],d_velocity_line_b[2],
                #                             d_velocity_angle[0],d_velocity_angle[1],d_velocity_angle[2]])

                if elapsed_time3 > 0:
                    ## 父类障碍物之前的位姿
                    position_before = np.array(
                        self.motion_model.calculate_position(elapsed_time3)
                    )
                    new_angle_before = self.motion_model.calculate_angle(elapsed_time3)
                    # velocity_before = np.array(self.motion_model.calculate_velocity(elapsed_time))
                else:
                    position_before = np.array(self.minus_time_position(elapsed_time3))
                    angle_time3 = elapsed_time3 if self.args.type == "circle" else 0.1
                    new_angle_before = self.motion_model.calculate_angle(angle_time3)

                ## 计算两个时刻的姿差别
                d_position = position_before - position
                d_new_angle = new_angle_before - new_angle
                if d_new_angle > -2 * np.pi and d_new_angle < -np.pi:
                    d_new_angle = d_new_angle + 2 * np.pi
                elif d_new_angle > np.pi and d_new_angle < 2 * np.pi:
                    d_new_angle = d_new_angle - 2 * np.pi

                # 姿态
                rotation_field = tractor_sub_node.getField("rotation3")
                rotation_field.setSFRotation([0, 0, 1, d_new_angle])

                # 位置
                d_position = d_position.reshape((3, 1))
                d_position_b = rot_matrix_world_b_T @ d_position
                translation_field = tractor_sub_node.getField("translation3")
                translation_field.setSFVec3f(
                    [d_position_b[0], d_position_b[1], d_position_b[2]]
                )
                # print("module3 world x:{},  y:{},  yaw:{},   time:{}".format(position_before[0], position_before[1], new_angle_before,elapsed_time3))
                # print("module3  translation x:{},  y:{},  yaw:{},  time:{}".format(d_position_b[0],d_position_b[1],d_new_angle,elapsed_time3))
                # # 速度
                # velocity_before = self.motion_model.calculate_velocity(elapsed_time3)
                # d_velocity = np.array(velocity_before) - np.array(velocity)
                # d_velocity_line = d_velocity[0:3]
                # d_velocity_angle = d_velocity[3:6]
                # d_velocity_line = d_velocity_line.reshape((3,1))
                # d_velocity_line_b = rot_matrix_world_b_T @ d_velocity_line
                # tractor_module1_node.setVelocity([d_velocity_line_b[0],d_velocity_line_b[1],d_velocity_line_b[2],
                #                             d_velocity_angle[0],d_velocity_angle[1],d_velocity_angle[2]])

                if elapsed_time4 > 0:
                    ## 父类障碍物之前的位姿
                    position_before = np.array(
                        self.motion_model.calculate_position(elapsed_time4)
                    )
                    new_angle_before = self.motion_model.calculate_angle(elapsed_time4)
                    # velocity_before = np.array(self.motion_model.calculate_velocity(elapsed_time))
                else:
                    position_before = np.array(self.minus_time_position(elapsed_time4))
                    angle_time4 = elapsed_time4 if self.args.type == "circle" else 0.1
                    new_angle_before = self.motion_model.calculate_angle(angle_time4)

                ## 计算两个时刻的姿差别
                d_position = position_before - position
                d_new_angle = new_angle_before - new_angle
                if d_new_angle > -2 * np.pi and d_new_angle < -np.pi:
                    d_new_angle = d_new_angle + 2 * np.pi
                elif d_new_angle > np.pi and d_new_angle < 2 * np.pi:
                    d_new_angle = d_new_angle - 2 * np.pi

                # 姿态
                rotation_field = tractor_sub_node.getField("rotation4")
                rotation_field.setSFRotation([0, 0, 1, d_new_angle])

                # 位置
                d_position = d_position.reshape((3, 1))
                d_position_b = rot_matrix_world_b_T @ d_position
                translation_field = tractor_sub_node.getField("translation4")
                translation_field.setSFVec3f(
                    [d_position_b[0], d_position_b[1], d_position_b[2]]
                )
                # print("module4 world x:{},  y:{},  yaw:{},   time:{}".format(position_before[0], position_before[1], new_angle_before,elapsed_time4))
                # print("module4  translation x:{},  y:{},  yaw:{},  time:{}".format(d_position_b[0],d_position_b[1],d_new_angle,elapsed_time4))

                self.module4_time.append(elapsed_time4)
                self.module4_x.append(d_position_b[0])
                self.module4_y.append(d_position_b[1])
                # # 速度
                # velocity_before = self.motion_model.calculate_velocity(elapsed_time4)
                # d_velocity = np.array(velocity_before) - np.array(velocity)
                # d_velocity_line = d_velocity[0:3]
                # d_velocity_angle = d_velocity[3:6]
                # d_velocity_line = d_velocity_line.reshape((3,1))
                # d_velocity_line_b = rot_matrix_world_b_T @ d_velocity_line
                # tractor_module1_node.setVelocity([d_velocity_line_b[0],d_velocity_line_b[1],d_velocity_line_b[2],
                #                             d_velocity_angle[0],d_velocity_angle[1],d_velocity_angle[2]])

                #  # 获取子模块节点5
                # tractor_module1_node = children_field.getMFNode(7)
                # if elapsed_time5 > 0:
                #     ## 父类障碍物之前的位姿
                #     position_before = np.array(self.motion_model.calculate_position(elapsed_time5))
                #     new_angle_before = self.motion_model.calculate_angle(elapsed_time5)
                # else:
                #     position_before = np.array(self.minus_time_position(elapsed_time5))
                #     new_angle_before = self.motion_model.calculate_angle(0.1)

                # d_position = position_before - position
                # d_new_angle = new_angle_before - new_angle

                # # 获取Field
                # rotation_field = tractor_module1_node.getField("rotation")
                # # 位置之差转到父类坐标系中
                # d_position = d_position.reshape((3,1))
                # d_position_b = rot_matrix_world_b_T @ d_position
                # translation_field = tractor_module1_node.getField("translation")
                # translation_field.setSFVec3f([d_position_b[0],d_position_b[1],d_position_b[2]])
                # rotation_field.setSFRotation([0, 0, 1, d_new_angle])

    # 时间为负时计算的位置点
    def minus_time_position(self, elapse_time) -> List[float]:
        if self.args.type == "line" or self.args.type == "polygon":
            elapse_time = abs(elapse_time)
            # 取前面两个点拟合直线。当时间为负数时，牵引车的子模块运动在虚拟点上
            first_point = self.motion_model.points[0]
            second_point = self.motion_model.points[1]
            # 虚拟直线斜率
            if (second_point[0] - first_point[0]) == 0:
                print("牵引车初始化无法计算斜率")
                return

            k = (second_point[1] - first_point[1]) / (second_point[0] - first_point[0])
            b = first_point[1] - k * first_point[0]

            # 计算虚拟点
            if second_point[0] > first_point[0]:
                virpoint_x = first_point[0] - 100
            else:
                virpoint_x = first_point[0] + 100

            virpoint_y = k * virpoint_x + b
            # 计算虚拟点和第一个点之间的距离
            vir_first_length = (
                (virpoint_y - first_point[1]) ** 2 + (virpoint_x - first_point[0]) ** 2
            ) ** 0.5

            # 给定负数时间 elapse_time
            delta_length = elapse_time * self.motion_model.velocity
            dx = delta_length * (virpoint_x - first_point[0]) / vir_first_length
            dy = delta_length * (virpoint_y - first_point[1]) / vir_first_length

            # 在给定负数时间elapse_time下的点
            virtime_point_x = first_point[0] + dx
            virtime_point_y = first_point[1] + dy
        if self.args.type == "circle":
            position = self.motion_model.calculate_position(elapse_time)
            virtime_point_x = position[0]
            virtime_point_y = position[1]

        return [virtime_point_x, virtime_point_y, 0]

    def get_device_distance(self):
        if (
            self.distance_sensor is not None
            and self.distance_sensor_2 is not None
            and self.distance_sensor_3 is not None
        ):
            value = self.distance_sensor.getValue()
            value_2 = self.distance_sensor_2.getValue()
            value_3 = self.distance_sensor_3.getValue()
            value = min(value, value_2, value_3)
        else:
            value = math.inf
        return value

    def check_collision(self):
        """检查碰撞"""
        device_distance = self.get_device_distance()

        if (
            device_distance is not math.inf
            and device_distance < 7.2
            and not self.is_stopped
        ):
            # 记录停止时的时间状态
            current_time = self.supervisor.getTime()
            self.pause_start_time = current_time
            self.is_stopped = True
            self.robot_node.setVelocity([0, 0, 0, 0, 0, 0])
            print(
                self.supervisor.getName(),
                "detect collision, pause_start_time:",
                self.pause_start_time,
            )
        elif (
            device_distance is not math.inf
            and device_distance > 5.0
            and self.is_stopped
        ):
            current_time = self.supervisor.getTime()
            paused_duration = current_time - self.pause_start_time
            self.simulation_time = self.simulation_time + paused_duration
            self.is_stopped = False
            print(self.supervisor.getName(), "resume , current_time:", current_time)

    def update_future_position(self, elapsed_time, current_time):

        # 如果当前不是三种动态模型，或者速度为0，则放弃发布预测信息
        if not (
            self.args.type == "line"
            or self.args.type == "circle"
            or self.args.type == "polygon"
        ):
            return

        # 当前速度模长
        velocity_norm = self.motion_model.velocity
        if velocity_norm == 0:
            return

        customFiled = self.robot_node.getField("customData")

        # 获取障碍物尺寸
        bounding_object_field = self.robot_node.getField("boundingObject")
        box_node = bounding_object_field.getSFNode()
        box_size_field = box_node.getField("size")
        box_size = box_size_field.getSFVec3f()
        # print("box",box_size[0])
        # 取对角线长度
        box_diagonal_length = (box_size[0] ** 2 + box_size[1] ** 2) ** 0.5

        # 未来机器人预测时间，未来(s)
        future_time = 5
        # 未来机器人位置预测时间步长和长度
        dt = 0.1
        future_path_length = future_time * velocity_norm + box_diagonal_length
        # 线性类型障碍物需要对预测距离做调整：如果输入的期望距离大于了它到终点的距离，那么就需要把预测距离改为它到终点距离
        line_flag = 0
        if self.args.type == "line":
            # 计算在路径上的总距离
            distance = (velocity_norm * elapsed_time) % self.motion_model.total_length

            resume_length = self.motion_model.total_length - distance
            if future_path_length > resume_length:
                future_path_length = resume_length
                line_flag = 1

        judge = future_path_length / velocity_norm / dt  # 未来的预测步数
        judge = math.ceil(judge)
        future_position_strarr = ""
        future_position = []
        for i in range(judge):
            future_elapsed_time = elapsed_time + i * dt
            # 未来机器人位置
            future_position = self.motion_model.calculate_position(future_elapsed_time)
            # future_position = self.motion_model.calculate_position(future_elapsed_time)
            px, py, pz = future_position
            # 更新未来机器人角度
            future_angle = self.motion_model.calculate_angle(future_elapsed_time)
            segment = f"{px},{py},{pz},{future_angle},{current_time + i * dt};"
            future_position_strarr += segment
            # if line_flag == 1:
            #     print(segment)

        # 对于线性模型，如果触发了截取长度的条件，还需要加上终点
        if self.args.type == "line" and line_flag == 1:
            if self.motion_model.points[-1] and future_position:
                last_point = self.motion_model.points[-1]
                # 预测路径最后一个点到终点的距离
                length_end = (
                    (future_position[0] - last_point[0]) ** 2
                    + (future_position[1] - last_point[1]) ** 2
                ) ** 0.5
                # 预测最后一个点到终点的时间差
                dt_end = length_end / velocity_norm
                # 到达最后一个点时的时间戳
                last_time = current_time + (judge - 1) * dt + dt_end
                # 到达最后一个点的偏航角
                yaw_end = self.motion_model.calculate_angle(last_time)
                segment = f"{last_point[0]},{last_point[1]},0,{yaw_end},{last_time};"
                future_position_strarr += segment
                # print(segment)

        customFiled.setSFString(future_position_strarr)

        # self.robot_node.setVelocity(velocity)

    def run(self):
        """运行机器人控制循环"""
        while self.supervisor.step(self.TIME_STEP) != -1:
            current_time = self.supervisor.getTime()

            if not self.is_stopped:
                elapsed = current_time - self.simulation_time
                self.update_position(elapsed)
                self.update_future_position(elapsed, current_time)

                # # 绘图
                # if self.module4_time[-1] > 40:
                #     if self.plot_flag == 0:
                #         print("run here")
                #         xpoints4 = np.array(self.module4_x)
                #         ypoints4 = np.array(self.module4_y)
                #         parent_yaws = np.array(self.parent_yaw)
                #         times = np.array(self.module4_time)
                #         plt.subplot(3,1,1)
                #         plt.plot(times, xpoints4)
                #         # plt.show()
                #         # plt.savefig("moduletime_x.png")  # 保存为图片文件
                #         plt.subplot(3,1,2)
                #         plt.plot(times, ypoints4)
                #         # plt.show()
                #         # plt.savefig("moduletime_y.png")  # 保存为图片文件
                #         plt.subplot(3,1,3)
                #         plt.plot(times, parent_yaws)
                #         plt.savefig("parent_yaw6.png")

                #         print("run here2")
                #         self.plot_flag = 1


def main():
    """主函数"""
    robot = CircularMotionRobot()
    robot.run()


if __name__ == "__main__":
    main()
