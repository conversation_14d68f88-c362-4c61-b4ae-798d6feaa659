// File:          my_controller.cpp
// Date:
// Description:
// Author:
// Modifications:

// You may need to add webots include files such as
// <webots/DistanceSensor.hpp>, <webots/Motor.hpp>, etc.
// and/or to add some other includes
#include <webots/Robot.hpp>
#include "object_module.h"
#include "object_process.hpp"
#include "webots/Supervisor.hpp"

#include "umodule/module_base.h"

// All the webots classes are defined in the "webots" namespace
using namespace webots;

// This is the main program of your controller.
// It creates an instance of your Robot instance, launches its
// function(s) and destroys it at the end of the execution.
// Note that only one instance of Robot should be created in
// a controller program.
// The arguments of the main function can be specified by the
// "controllerArgs" field of the Robot node
int main(int argc, char **argv)
{
    std::unique_ptr<uslam::module::ObjectModule> object_module = std::make_unique<uslam::module::ObjectModule>();

    while (object_module->step() != -1) {
        object_module->update();
    };

    return 0;
}
