/**
 *
 *   @file control_manager_module.h
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2022-11-24
 *
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef WEBOTS_MODULE_H_
#define WEBOTS_MODULE_H_
#include <uservice/ros_service.h>
#include <webots_objects_msgs/srv/Set.h>
#include <webots_objects_msgs/srv/Get.h>
#include <webots/Supervisor.hpp>
#include <utransport/reader.h>

#include "object_process.hpp"

namespace uslam {
namespace module {

class ObjectModule
{
public:
    ObjectModule();
    ~ObjectModule();

    bool init();
    int step();
    void update();

private:
    webots::Supervisor *super_robot_;
    std::unique_ptr<ObjectProcess> object_process_;
    std::atomic<bool> skip_update_;
    std::atomic<int> update_cnt_;

    uslam::transport::ReaderPtr<webots_objects_msgs::msg::WbObjectList> set_object_reader_;
    uslam::transport::WriterPtr<custom_msgs::msg::PerceptionObjects> object_writer_;
    uslam::transport::WriterPtr<custom_msgs::msg::PerceptionObjects> all_object_writer_;

    // 设置和读取地图所有障碍物的接口
    void setObjectCallback(const std::shared_ptr<webots_objects_msgs::msg::WbObjectList> &msg);
    std::shared_ptr<uslam::RosService<webots_objects_msgs::srv::Set_Request, webots_objects_msgs::srv::Set_Response>> set_service_;
    void setServiceCallback(const webots_objects_msgs::srv::Set_Request::ConstPtr &req,
                            webots_objects_msgs::srv::Set_Response::Ptr &res);
    std::shared_ptr<uslam::RosService<webots_objects_msgs::srv::Get_Request, webots_objects_msgs::srv::Get_Response>> get_service_;
    void getServiceCallback(const webots_objects_msgs::srv::Get_Request::ConstPtr &req,
                            webots_objects_msgs::srv::Get_Response::Ptr &res);

    void setClock();
    void publishObjects(bool publish_all = false);
};

}  // namespace module
}  // namespace uslam

#endif
