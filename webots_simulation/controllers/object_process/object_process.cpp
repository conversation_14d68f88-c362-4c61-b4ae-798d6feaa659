#include "object_process.hpp"
#include <pcl/features/moment_of_inertia_estimation.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <ulog/ulog.h>
#include <utf/tf2/transform_datatypes.h>
#include <opencv2/core/types.hpp>
#include <opencv2/opencv.hpp>
#include <pcl/impl/point_types.hpp>
#include <regex>

using namespace webots;

ObjectProcess::ObjectProcess(webots::Supervisor *supervisor, webots::Node *main_robot_node) :
    super_robot_(supervisor), main_robot_node_(main_robot_node)
{
}

ObjectProcess::~ObjectProcess() {}

void ObjectProcess::findBoxes(const Node *node, std::vector<const Node *> &boxes)
{
    if (node == NULL) return;

    if (node->getTypeName() == "Box") {
        boxes.push_back(node);
    }

    Field *childrenField = node->getField("children");
    if (childrenField == NULL) return;

    int childCount = childrenField->getCount();
    for (int i = 0; i < childCount; ++i) {
        Node *childNode = childrenField->getMFNode(i);
        if (childNode == NULL) continue;
        findBoxes(childNode, boxes);
    }
}

void ObjectProcess::findBoundingObject(const Node *node, std::vector<const Node *> &output)
{
    if (node == NULL) return;

    Field *bounding_object_field = node->getField("boundingObject");
    if (bounding_object_field != NULL) {
        const Node *bounding_object_node = bounding_object_field->getSFNode();
        if (bounding_object_node) {
            output.push_back(bounding_object_node);
        }
    }
    if (node->isProto()) {
        Field *bounding_object_proto_field = node->getProtoField("boundingObject");
        if (bounding_object_proto_field != NULL) {
            const Node *bounding_object_proto_node = bounding_object_proto_field->getSFNode();
            if (bounding_object_proto_node) {
                output.push_back(bounding_object_proto_node);
            }
        }
    }

    if (node->getDef().find("VEHICLE") == std::string::npos) return;

    Field *childrenField = node->getField("children");
    if (childrenField == NULL) return;

    int childCount = childrenField->getCount();
    for (int i = 0; i < childCount; ++i) {
        Node *childNode = childrenField->getMFNode(i);
        if (childNode == NULL) continue;
        findBoundingObject(childNode, output);
    }
}

BoundingBox3d ObjectProcess::extractBoundingBox(std::vector<WbBoundingObject> &objects)
{
    if (objects.empty()) return BoundingBox3d();

    std::vector<Eigen::Vector3f> points;
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>());
    std::vector<cv::Point2f> cv_points;

    double max_z = std::numeric_limits<double>::lowest();
    double min_z = std::numeric_limits<double>::max();
    for (auto &object : objects) {
        std::vector<Eigen::Vector3f> localCorners = {{object.size.x() / 2, object.size.y() / 2, object.size.z() / 2},
                                                     {-object.size.x() / 2, object.size.y() / 2, object.size.z() / 2},
                                                     {object.size.x() / 2, -object.size.y() / 2, object.size.z() / 2},
                                                     {-object.size.x() / 2, -object.size.y() / 2, object.size.z() / 2},
                                                     {object.size.x() / 2, object.size.y() / 2, -object.size.z() / 2},
                                                     {-object.size.x() / 2, object.size.y() / 2, -object.size.z() / 2},
                                                     {object.size.x() / 2, -object.size.y() / 2, -object.size.z() / 2},
                                                     {-object.size.x() / 2, -object.size.y() / 2, -object.size.z() / 2}};

        for (const auto &localCorner : localCorners) {
            Eigen::Vector3f worldCornerVec = object.transform * localCorner;
            points.push_back(worldCornerVec);
            cloud->push_back(pcl::PointXYZ(worldCornerVec.x(), worldCornerVec.y(), 0.0));
            cv_points.push_back(cv::Point2f(worldCornerVec.x(), worldCornerVec.y()));
            if (worldCornerVec.z() > max_z) max_z = worldCornerVec.z();
            if (worldCornerVec.z() < min_z) min_z = worldCornerVec.z();
        }
    }

    // 计算点集的最小外包旋转矩形
    cv::RotatedRect rect = cv::minAreaRect(cv_points);
    cv::Mat vertices;
    cv::boxPoints(rect, vertices);

    BoundingBox3d bounding_box3d;
    bounding_box3d.size.x() = rect.size.height;
    bounding_box3d.size.y() = rect.size.width;
    bounding_box3d.size.z() = max_z - min_z;
    bounding_box3d.translation.x() = rect.center.x;
    bounding_box3d.translation.y() = rect.center.y;
    bounding_box3d.translation.z() = (max_z + min_z) / 2.0;
    bounding_box3d.yaw = 0.0;

    // std::cout << "size:" << bounding_box3d.size.transpose() << std::endl;
    return bounding_box3d;
}
BoundingBox3d ObjectProcess::extractBoundingBoxFromNode(webots::Node *node_ptr)
{
    std::vector<const Node *> bounding_object_node_vec;  // bounding_object 对象的指针
    std::vector<const Node *> box_node_vec;              // 需要检测的box的指针和对应的bounding_object

    findBoundingObject(node_ptr, bounding_object_node_vec);
    for (auto &bounding_object_node : bounding_object_node_vec) {
        if (!bounding_object_node) continue;
        std::vector<const Node *> targets;
        findBoxes(bounding_object_node, targets);
        for (auto &target : targets) {
            box_node_vec.push_back(target);
        }
    }

    // printf("name:%s bounding_object_vec size:%ld box_vec size:%ld\n", it->first.c_str(),
    //        bounding_object_node_vec.size(), box_node_vec.size());

    std::vector<WbBoundingObject> bounding_object_vec;  // 所有box 都转到 目标node坐标系下
    for (auto &obj : box_node_vec) {
        Eigen::Isometry3f transform = Eigen::Isometry3f::Identity();
        auto ptr = obj->getParentNode();
        while (ptr != node_ptr) {
            const Field *translation_field = ptr->getField("translation");
            if (translation_field) {
                const Field *rotation_field = ptr->getField("rotation");
                double ax = rotation_field->getSFRotation()[0];
                double ay = rotation_field->getSFRotation()[1];
                double az = rotation_field->getSFRotation()[2];
                double angle = rotation_field->getSFRotation()[3];
                double qx = ax * sin(angle * 0.5);
                double qy = ay * sin(angle * 0.5);
                double qz = az * sin(angle * 0.5);
                double qw = cos(angle * 0.5);

                transform.rotate(Eigen::Quaternionf(qw, qx, qy, qz));
                transform.pretranslate(Eigen::Vector3f(translation_field->getSFVec3f()[0],
                                                       translation_field->getSFVec3f()[1],
                                                       translation_field->getSFVec3f()[2]));
            }
            ptr = ptr->getParentNode();
        }

        WbBoundingObject bo;
        bo.type = "box";
        const Field *size_field = obj->getField("size");
        bo.size.x() = size_field->getSFVec3f()[0];
        bo.size.y() = size_field->getSFVec3f()[1];
        bo.size.z() = size_field->getSFVec3f()[2];
        bo.transform = transform;
        bounding_object_vec.push_back(bo);
    }
    auto boundingbox = extractBoundingBox(bounding_object_vec);  // 提取最大的包围框
    return boundingbox;
}

void ObjectProcess::updateUnknownNode()
{
    // 获取webots对象列表
    auto name_list = extractDefNameList();
    for (int i = 0; i < name_list.size(); ++i) {
        std::string def_name = name_list[i].first;
        std::string target_def_prefix = name_list[i].second;

        if (node_list_.find(def_name) != node_list_.end()) continue;

        std::string id_str;
        int id;
        try {
            id_str = def_name.substr(target_def_prefix.length());
            id = std::stoi(id_str);
        } catch (std::exception &e) {
            std::cout << "DDSWbGt::updateNode: getDef failed" << std::endl;
            continue;
        }

        struct NodeData node_data;
        node_data.node_ptr = super_robot_->getFromDef(def_name);
        node_data.def_name = def_name;
        node_data.webots_id = node_data.node_ptr->getId();
        std::cout << "DDSWbGt::updateNode: find object name:" << def_name << " id:" << node_data.node_ptr->getId()
                  << std::endl;

        int prefix_id_base = PrefixIdMap.at(target_def_prefix);
        node_data.def_name_id = id;
        node_data.output_id = prefix_id_base + node_data.def_name_id;
        node_data.prefix = target_def_prefix;

        node_data.webots_object_msg.prefix = target_def_prefix;
        node_data.webots_object_msg.id = id;
        const Field *bounding_object_field = node_data.node_ptr->getField("boundingObject");
        if (bounding_object_field->getSFNode()) {
            Field *bounding_object_size = bounding_object_field->getSFNode()->getField("size");
            node_data.webots_object_msg.size.x = bounding_object_size->getSFVec3f()[0];
            node_data.webots_object_msg.size.y = bounding_object_size->getSFVec3f()[1];
            node_data.webots_object_msg.size.z = bounding_object_size->getSFVec3f()[2];
        }

        const Field *translation_field = node_data.node_ptr->getField("translation");
        if (translation_field->getSFVec3f()) {
            node_data.webots_object_msg.pose.position.x = translation_field->getSFVec3f()[0];
            node_data.webots_object_msg.pose.position.y = translation_field->getSFVec3f()[1];
            node_data.webots_object_msg.pose.position.z = translation_field->getSFVec3f()[2];
        }

        Field *controller_args = node_data.node_ptr->getField("controllerArgs");
        if (controller_args) {
            int args_count = controller_args->getCount();
            for (int i = 0; i < args_count; ++i) {
                std::string arg = controller_args->getMFString(i);
                if (arg.find("--type=") == 0) {
                    std::string type = arg.substr(7);  // Extract type
                    if (type == "cycle")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE;
                    else if (type == "line")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES;
                    else if (type == "polygon")
                        node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON;
                } else if (arg.find("--velocity=") == 0) {
                    node_data.webots_object_msg.velocity = std::stod(arg.substr(11));  // Extract velocity
                } else if (arg.find("--radius=") == 0) {
                    node_data.webots_object_msg.radius = std::stod(arg.substr(9));  // Extract radius
                } else if (arg.find("--points=") == 0) {
                    std::string points_str = arg.substr(9);  // Extract points
                    std::istringstream points_stream(points_str);
                    std::string point_pair;
                    while (std::getline(points_stream, point_pair, ';')) {
                        std::istringstream pair_stream(point_pair);
                        std::string x_str, y_str;
                        if (std::getline(pair_stream, x_str, ',') && std::getline(pair_stream, y_str, ',')) {
                            geometry_msgs::msg::Point32 point;
                            point.x = std::stod(x_str);
                            point.y = std::stod(y_str);
                            node_data.webots_object_msg.points.push_back(point);
                        }
                    }
                } else if (arg.find("--init_pos=") == 0) {
                    std::string init_pos_str = arg.substr(11);  // Extract init_pos
                    std::istringstream init_pos_stream(init_pos_str);
                    std::string x_str, y_str;
                    if (std::getline(init_pos_stream, x_str, ',') && std::getline(init_pos_stream, y_str, ',')) {
                        node_data.webots_object_msg.pose.position.x = std::stod(x_str);
                        node_data.webots_object_msg.pose.position.y = std::stod(y_str);
                    }
                }
            }
        } else {
            node_data.webots_object_msg.type = webots_objects_msgs::msg::WbObject_Constants::STATIC;
            node_data.webots_object_msg.prefix = "BOX";

            node_data.webots_object_msg.velocity = 0.0;
            node_data.webots_object_msg.radius = 0.0;
        }

        node_data.bounding_box = extractBoundingBoxFromNode(node_data.node_ptr);
        {
            std::cout << "DDSWbGt::updateNode: add node name:" << def_name << " id:" << node_data.node_ptr->getId()
                      << std::endl;
            std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
            node_list_.insert({def_name, node_data});
        }
    }

    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);

    const std::string prefix = "TrailerModule";  //牵引车子模块前缀
    for (auto it = node_list_.begin(); it != node_list_.end();) {
        if (it->second.webots_id < 35000 && super_robot_->getFromId(it->second.webots_id) == nullptr) {
            std::cout << "DDSWbGt::updateNode: remove node name:" << it->first << std::endl;
            it = node_list_.erase(it);
        } else {
            ++it;
        }
    }

    // 对牵引车子节点做处理
    for (auto it = node_list_.begin(); it != node_list_.end(); ++it) {
        // 找到"TrailerModule"在字符串中的起始位置
        size_t prefix_pos = it->second.def_name.find(prefix);
        if (!(prefix_pos == std::string::npos)) {  //找到前缀，说明是牵引车子节点
            std::string trailer_webot_id_str = it->second.def_name.substr(0, prefix_pos);  // 找到头车节点
            int trailer_webot_id = std::stoi(trailer_webot_id_str);
            if (super_robot_->getFromId(trailer_webot_id) == nullptr) {
                it = node_list_.erase(it);
            } else {
                // 车头节点还在，但是给换成了别的类型的
                auto obstacle_node = super_robot_->getFromId(trailer_webot_id);
                Field *description_field = obstacle_node->getField("description");
                std::string description_str = description_field->getSFString();
                if (description_str != "Trailer") {
                    it = node_list_.erase(it);
                }
            }
        }
    }
}

void ObjectProcess::updateNode(std::string prefix, int id, const webots_objects_msgs::msg::WbObject &webots_object_msg)
{
    if (PrefixIdMap.find(prefix) == PrefixIdMap.end()) return;
    std::string def_name = prefix + std::to_string(id);
    if (node_list_.find(def_name) == node_list_.end()) {
        struct NodeData node_data;
        node_data.node_ptr = super_robot_->getFromDef(def_name);
        if (!node_data.node_ptr) return;

        node_data.def_name = def_name;
        node_data.webots_id = node_data.node_ptr->getId();

        int prefix_id_base = PrefixIdMap.at(prefix);
        node_data.def_name_id = id;
        node_data.output_id = prefix_id_base + node_data.def_name_id;
        node_data.prefix = prefix;
        node_data.webots_object_msg = webots_object_msg;
        node_data.bounding_box = extractBoundingBoxFromNode(node_data.node_ptr);

        Eigen::Matrix4f robot_matrix_world;
        const double *robot_pose_world = main_robot_node_->getPose();
        robot_matrix_world << robot_pose_world[0], robot_pose_world[1], robot_pose_world[2], robot_pose_world[3],
            robot_pose_world[4], robot_pose_world[5], robot_pose_world[6], robot_pose_world[7], robot_pose_world[8],
            robot_pose_world[9], robot_pose_world[10], robot_pose_world[11], robot_pose_world[12], robot_pose_world[13],
            robot_pose_world[14], robot_pose_world[15];
        updateObjectPose(&node_data, robot_matrix_world);

        std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
        node_list_.insert({def_name, node_data});

        std::cout << "DDSWbGt::updateNode manual: object name:" << def_name << " id:" << node_data.node_ptr->getId()
                  << std::endl;
    }
}
void ObjectProcess::addBoxObstacleToWb(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                       double pos_y, double pos_theta, double velocity, std::string specific_type_str)
{
    if (specific_type_str == "Trailer") {
        // 创建Box物体的PROTO字符串
        std::string boxDef =
            "DEF %s Solid {"
            "  description \"%s\""
            "  translation %f %f 0"
            "  rotation 0 0 1 %f"
            "  children ["

            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "      translation1 %f %f %f\n"
            "      translation2 %f %f %f\n"
            "      translation3 %f %f %f\n"
            "      translation4 %f %f %f\n"
            "    }\n"

            "    Shape {"
            "      appearance Appearance {"
            "        material Material {"
            "          diffuseColor 1 0 0"
            "        }"
            "      }"
            "      geometry Box {"
            "        size %f %f %f"
            "      }"
            "    }"
            "  ]"
            "  name \"%s\""
            "  boundingObject Box {"
            "    size %f %f %f"
            "  }"
            "}";
        char box_string[3000];
        sprintf(box_string, boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, pos_theta,
                size_x * 1.8, size_y, size_z, -size_x * 1.9, 0.0, 0.0, -size_x * 4.2, 0.0, 0.0, -size_x * 6.5, 0.0, 0.0,
                -size_x * 8.8, 0.0, 0.0, size_x, size_y, size_z, name.c_str(), size_x, size_y, size_z);

        // 将Box物体添加到场景中
        super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    } else {
        std::string boxDef =
            "DEF %s Solid {"
            "  description \"%s\""
            "  translation %f %f 0"
            "  rotation 0 0 1 %f"
            "  children ["
            "    Shape {"
            "      appearance Appearance {"
            "        material Material {"
            "          diffuseColor 1 0 0"
            "        }"
            "      }"
            "      geometry Box {"
            "        size %f %f %f"
            "      }"
            "    }"
            "  ]"
            "  name \"%s\""
            "  boundingObject Box {"
            "    size %f %f %f"
            "  }"
            "}";
        char box_string[2000];
        sprintf(box_string, boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, pos_theta, size_x,
                size_y, size_z, name.c_str(), size_x, size_y, size_z);

        // 将Box物体添加到场景中
        super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    }

    // Node *obstacle = super_robot_->getFromDef(name);
    // double velocity_array[6] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
    // velocity_array[0] = velocity * cos(pos_theta);
    // velocity_array[1] = velocity * sin(pos_theta);
    // obstacle->setVelocity(velocity_array);
    printf("addObstacle name:%s size:%f %f %f pos:%f %f theta:%f velocity:%f specific_type:%s\n", name.c_str(), size_x,
           size_y, size_z, pos_x, pos_y, pos_theta, velocity, specific_type_str.c_str());
}

void ObjectProcess::addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                          double pos_y, std::string type, double velocity, double radius,
                                          std::string specific_type_str)
{
    if (type != "circle") {
        printf("type %s not support\n", type.c_str());
        return;
    }

    // 根据障碍物具体类型选择proto
    char box_string[2000];
    if (specific_type_str == "Trailer") {
        std::string dynamic_boxDef =
            "DEF %s Robot {\n"
            "  description \"%s\""
            "  translation %f %f 0\n"
            "  children [\n"

            "    DBOXUltrasonicSensor {\n"
            "      name \"u1\"\n"
            "      translation %f 0 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u2\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u3\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"

            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "    }\n"

            "    Shape {\n"
            "      appearance Appearance {\n"
            "        material Material {\n"
            "          diffuseColor 0 0 1\n"
            "        }\n"
            "      }\n"
            "      geometry Box {\n"
            "        size %f %f %f\n"
            "      }\n"
            "    }\n"
            "  ]\n"
            "  name \"%s\"\n"  // 添加了name属性
            "  boundingObject Box {\n"
            "    size %f %f %f\n"
            "  }\n"
            "  controller \"path_motion\"\n"
            "  controllerArgs [\n"
            "    \"--type=%s\"\n"
            "    \"--radius=%f\"\n"
            "    \"--velocity=%f\"\n"
            "    \"--init_pos=%f,%f,0\"\n"
            "  ]\n"
            "  supervisor TRUE\n"
            "}";

        sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, size_x / 2.0,
                size_x / 2.0, size_y / 2.0, size_x / 2.0, -size_y / 2.0, size_x * 1.8, size_y, size_z, size_x, size_y,
                size_z,
                name.c_str(),  // 为name属性添加值
                size_x, size_y, size_z, type.c_str(), radius, velocity, pos_x, pos_y);

    } else {
        std::string dynamic_boxDef =
            "DEF %s Robot {\n"
            "  description \"%s\""
            "  translation %f %f 0\n"

            "  children [\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u1\"\n"
            "      translation %f 0 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u2\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u3\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"

            "    Shape {\n"
            "      appearance Appearance {\n"
            "        material Material {\n"
            "          diffuseColor 0 0 1\n"
            "        }\n"
            "      }\n"
            "      geometry Box {\n"
            "        size %f %f %f\n"
            "      }\n"
            "    }\n"
            "  ]\n"
            "  name \"%s\"\n"  // 添加了name属性
            "  boundingObject Box {\n"
            "    size %f %f %f\n"
            "  }\n"
            "  controller \"path_motion\"\n"
            "  controllerArgs [\n"
            "    \"--type=%s\"\n"
            "    \"--radius=%f\"\n"
            "    \"--velocity=%f\"\n"
            "    \"--init_pos=%f,%f,0\"\n"
            "  ]\n"
            "  supervisor TRUE\n"
            "}";
        sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, size_x / 2.0,
                size_x / 2.0, size_y / 2.0, size_x / 2.0, -size_y / 2.0, size_x, size_y, size_z,
                name.c_str(),  // 为name属性添加值
                size_x, size_y, size_z, type.c_str(), radius, velocity, pos_x, pos_y);
    }

    // 将Box物体添加到场景中
    super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    printf("addDynamicBoxObstacle name:%s size:%f %f %f pos:%f %f type:%s radius:%f velocity:%f specific_type:%s\n",
           name.c_str(), size_x, size_y, size_z, pos_x, pos_y, type.c_str(), radius, velocity, specific_type_str.c_str());
}

void ObjectProcess::addDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z, double pos_x,
                                          double pos_y, std::string type, double velocity,
                                          const std::vector<geometry_msgs::msg::Point32> &points,
                                          std::string specific_type_str)
{
    if (type != "polygon" && type != "line") {
        printf("type %s not support\n", type.c_str());
        return;
    }

    // 将所有路径点转换为字符串
    std::string points_str;
    for (size_t i = 0; i < points.size(); ++i) {
        points_str += std::to_string(points[i].x) + "," + std::to_string(points[i].y);
        if (i < points.size() - 1) {
            points_str += ";";
        }
    }

    char box_string[2000];
    // 根据障碍物类别选择使用不同的proto
    if (specific_type_str == "Trailer") {
        // 牵引车proto
        std::string dynamic_boxDef =
            "DEF %s Robot {\n"
            "  description \"%s\""
            "  translation %f %f 0\n"
            "  children [\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u1\"\n"
            "      translation %f 0 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u2\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u3\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"

            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "    }\n"

            "    Shape {\n"
            "      appearance Appearance {\n"
            "        material Material {\n"
            "          diffuseColor 0 0 1\n"
            "        }\n"
            "      }\n"
            "      geometry Box {\n"
            "        size %f %f %f\n"
            "      }\n"
            "    }\n"
            "  ]\n"
            "  name \"%s\"\n"
            "  boundingObject Box {\n"
            "    size %f %f %f\n"
            "  }\n"
            "  controller \"path_motion\"\n"
            "  controllerArgs [\n"
            "    \"--type=%s\"\n"
            "    \"--velocity=%f\"\n"
            "    \"--points=%s\"\n"
            "  ]\n"
            "  supervisor TRUE\n"
            "}";

        sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, size_x / 2.0,
                size_x / 2.0, size_y / 2.0, size_x / 2.0, -size_y / 2.0, size_x * 1.8, size_y, size_z, size_x, size_y,
                size_z, name.c_str(), size_x, size_y, size_z, type.c_str(), velocity, points_str.c_str());

        // sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, size_x / 2.0,
        //         size_x / 2.0, size_y / 2.0, size_x / 2.0, -size_y / 2.0, size_x, size_y, size_z, name.c_str(), size_x,
        //         size_y, size_z, type.c_str(), velocity, points_str.c_str());

    } else {
        // 其他类型proto
        std::string dynamic_boxDef =
            "DEF %s Robot {\n"
            "  description \"%s\""
            "  translation %f %f 0\n"
            "  children [\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u1\"\n"
            "      translation %f 0 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u2\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"
            "    DBOXUltrasonicSensor {\n"
            "      name \"u3\"\n"
            "      translation %f %f 0.5\n"
            "    }\n"

            "    Shape {\n"
            "      appearance Appearance {\n"
            "        material Material {\n"
            "          diffuseColor 0 0 1\n"
            "        }\n"
            "      }\n"
            "      geometry Box {\n"
            "        size %f %f %f\n"
            "      }\n"
            "    }\n"
            "  ]\n"
            "  name \"%s\"\n"
            "  boundingObject Box {\n"
            "    size %f %f %f\n"
            "  }\n"
            "  controller \"path_motion\"\n"
            "  controllerArgs [\n"
            "    \"--type=%s\"\n"
            "    \"--velocity=%f\"\n"
            "    \"--points=%s\"\n"
            "  ]\n"
            "  supervisor TRUE\n"
            "}";

        sprintf(box_string, dynamic_boxDef.c_str(), name.c_str(), specific_type_str.c_str(), pos_x, pos_y, size_x / 2.0,
                size_x / 2.0, size_y / 2.0, size_x / 2.0, -size_y / 2.0, size_x, size_y, size_z, name.c_str(), size_x,
                size_y, size_z, type.c_str(), velocity, points_str.c_str());
    }

    // 将Box物体添加到场景中
    super_robot_->getRoot()->getField("children")->importMFNodeFromString(-1, box_string);
    printf("addDynamicBoxObstacle name:%s size:%f %f %f pos:%f %f type:%s points:%s velocity:%f specific_type:%s\n",
           name.c_str(), size_x, size_y, size_z, pos_x, pos_y, type.c_str(), points_str.c_str(), velocity,
           specific_type_str.c_str());
}

void ObjectProcess::updateBoxObstacleToWb(std::string obs_name, double size_x, double size_y, double size_z, double pos_x,
                                          double pos_y, double pos_theta, double velocity, std::string specific_type_str)
{
    Node *obstacle = super_robot_->getFromDef(obs_name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", obs_name.c_str());
        return;
    }

    double velocity_array[6] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
    velocity_array[0] = velocity * cos(pos_theta);
    velocity_array[1] = velocity * sin(pos_theta);
    obstacle->setVelocity(velocity_array);

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *rotation = obstacle->getField("rotation");
    double rotation_values[4] = {0.0, 0.0, 1.0, pos_theta};
    rotation->setSFRotation(rotation_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    Field *specific_type_field = obstacle->getField("description");
    specific_type_field->setSFString(specific_type_str);
    Field *children_field = obstacle->getField("children");
    int tractor_module_nums = 0;
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }

        // 寻找是否有牵引车子节点
        if (node->getDef() == "TrailerModule") {
            tractor_module_nums += 1;
        }
    }

    // 如果现在不是牵引车，且子节点不为0，则删除牵引车子节点
    if (specific_type_str != "Trailer" && tractor_module_nums != 0) {
        std::cout << "remove trailer sub module" << std::endl;
        children_field->removeMF(0);
    }

    // 如果现在是牵引车，而牵引车子节点为0,则添加牵引车子节点
    if (specific_type_str == "Trailer" && tractor_module_nums == 0) {
        char tractor_module_string[1000];
        std::string tractor_module_Def =
            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "      translation1 %f %f %f\n"
            "      translation2 %f %f %f\n"
            "      translation3 %f %f %f\n"
            "      translation4 %f %f %f\n"
            "    }\n";
        sprintf(tractor_module_string, tractor_module_Def.c_str(), size_x * 1.8, size_y, size_z, -size_x * 1.9, 0.0,
                0.0, -size_x * 4.2, 0.0, 0.0, -size_x * 6.5, 0.0, 0.0, -size_x * 8.8, 0.0, 0.0);

        children_field->importMFNodeFromString(-1, tractor_module_string);
    }

    printf("updateObstacle name:%s size:%f %f %f pos:%f %f theta:%f velocity:%f specific_type:%s\n", obs_name.c_str(),
           size_x, size_y, size_z, pos_x, pos_y, pos_theta, velocity, specific_type_str.c_str());
}
void ObjectProcess::updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z,
                                             double pos_x, double pos_y, std::string type, double velocity,
                                             double radius, std::string specific_type_str)
{
    Node *obstacle = super_robot_->getFromDef(name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", name.c_str());
        return;
    }
    Field *specific_type_field = obstacle->getField("description");
    specific_type_field->setSFString(specific_type_str);

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    Field *children_field = obstacle->getField("children");
    int tractor_module_nums = 0;
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }

        // 寻找是否有牵引车子节点
        if (node->getDef() == "TrailerModule") {
            tractor_module_nums += 1;
        }
    }
    // 如果现在不是牵引车，且子节点不为0，则删除牵引车子节点
    if (specific_type_str != "Trailer" && tractor_module_nums != 0) {
        std::cout << "remove trailer sub module" << std::endl;
        children_field->removeMF(3);
    }

    // 如果现在是牵引车，而牵引车子节点为0,则添加牵引车子节点
    if (specific_type_str == "Trailer" && tractor_module_nums == 0) {
        char tractor_module_string[1000];
        std::string tractor_module_Def =
            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "    }\n";
        sprintf(tractor_module_string, tractor_module_Def.c_str(), size_x * 1.8, size_y, size_z);
        children_field->importMFNodeFromString(3, tractor_module_string);
    }

    // controllerArgs
    Field *controller_args = obstacle->getField("controllerArgs");
    std::string type_str = "--type=" + type;
    controller_args->setMFString(0, type_str.c_str());
    std::string velocity_str = "--velocity=" + std::to_string(velocity);
    controller_args->setMFString(1, velocity_str.c_str());
    std::string radius_str = "--radius=" + std::to_string(radius);
    controller_args->setMFString(2, radius_str.c_str());

    obstacle->restartController();
    printf("updateObstacle name:%s size:%f %f %f specific_type:%s\n", name.c_str(), size_x, size_y, size_z,
           specific_type_str.c_str());
}

void ObjectProcess::updateDynamicBoxObstacle(std::string name, double size_x, double size_y, double size_z,
                                             double pos_x, double pos_y, std::string type, double velocity,
                                             const std::vector<geometry_msgs::msg::Point32> &points,
                                             std::string specific_type_str)
{
    Node *obstacle = super_robot_->getFromDef(name);
    if (obstacle == nullptr) {
        printf("obstacle %s not found\n", name.c_str());
        return;
    }

    Field *specific_type_field = obstacle->getField("description");
    specific_type_field->setSFString(specific_type_str);

    double pos_values[3] = {pos_x, pos_y, 0.0};
    Field *translation = obstacle->getField("translation");
    translation->setSFVec3f(pos_values);

    Field *bounding_object_size = obstacle->getField("boundingObject")->getSFNode()->getField("size");
    double size_values[3] = {size_x, size_y, size_z};
    bounding_object_size->setSFVec3f(size_values);
    Field *children_field = obstacle->getField("children");

    int tractor_module_nums = 0;
    int tractor_first_index = 90;
    for (int i = 0; i < children_field->getCount(); i++) {
        auto node = children_field->getMFNode(i);
        if (node->getTypeName() == "Shape") {
            Field *geometry_size = node->getField("geometry")->getSFNode()->getField("size");
            geometry_size->setSFVec3f(size_values);
        }
        // 检查是否有牵引车子模块节点
        if (node->getDef() == "TrailerModule") {
            tractor_module_nums += 1;
        }
    }
    // 如果现在不是牵引车，且子节点不为0，则删除牵引车子节点
    if (specific_type_str != "Trailer" && tractor_module_nums != 0) {
        std::cout << "remove trailer sub module" << std::endl;
        children_field->removeMF(3);
    }

    // 如果现在是牵引车，而牵引车子节点为0,则添加牵引车子节点
    if (specific_type_str == "Trailer" && tractor_module_nums == 0) {
        char tractor_module_string[1000];
        std::string tractor_module_Def =
            "    DEF TrailerModule DynamicTrailerModule {\n"
            "      size %f %f %f\n"
            "    }\n";
        sprintf(tractor_module_string, tractor_module_Def.c_str(), size_x * 1.8, size_y, size_z);
        children_field->importMFNodeFromString(3, tractor_module_string);
    }
    specific_type_field = obstacle->getField("description");
    specific_type_field->setSFString(specific_type_str);

    // controllerArgs
    Field *controller_args = obstacle->getField("controllerArgs");
    std::string type_str = "--type=" + type;
    controller_args->setMFString(0, type_str.c_str());
    std::string velocity_str = "--velocity=" + std::to_string(velocity);
    controller_args->setMFString(1, velocity_str.c_str());
    std::string points_str = "--points=";
    for (size_t i = 0; i < points.size(); ++i) {
        points_str += std::to_string(points[i].x) + "," + std::to_string(points[i].y);
        if (i < points.size() - 1) {
            points_str += ";";
        }
    }
    controller_args->setMFString(2, points_str.c_str());

    obstacle->restartController();
    printf("updateObstacle name:%s size:%f %f %f specific_type:%s\n", name.c_str(), size_x, size_y, size_z,
           specific_type_str.c_str());
}

int ObjectProcess::findMaxId(std::string prefix)
{
    // 寻找最大的id
    int id_max = 0;
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    std::for_each(node_list_.begin(), node_list_.end(), [&](const auto &item) {
        int id = item.second.def_name_id;
        if (item.second.prefix == prefix && id > id_max) {
            id_max = id;
        }
    });
    lock.unlock();
    return id_max;
}

void ObjectProcess::setObstacle(const webots_objects_msgs::msg::WbObject &webots_object_msg, bool in_base_link)
{
    if (webots_object_msg.action != webots_objects_msgs::msg::WbObject_Constants::ADD) {
        return;
    }
    std::string obstable_prefix;
    std::string obstable_dynamic_type;
    std::string obstable_specific_type;
    if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::STATIC)
        obstable_prefix = "BOX";
    else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "polygon";
    } else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "circle";
    } else if (webots_object_msg.type == webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES) {
        obstable_prefix = "DBOX";
        obstable_dynamic_type = "line";
    } else {
        printf("object type %d not support\n", webots_object_msg.type);
        return;
    }

    auto msg_cpy = webots_object_msg;
    msg_cpy.prefix = obstable_prefix;
    // 障碍物具体类别
    if (webots_object_msg.specific_type == webots_objects_msgs::msg::WbObject_Constants::PEDESTRIAN) {
        obstable_specific_type = "Pedestrian";
    } else if (webots_object_msg.specific_type == webots_objects_msgs::msg::WbObject_Constants::TRAILER) {
        obstable_specific_type = "Trailer";
    } else {
        obstable_specific_type = "Car";
    }

    // std::cout << " specific type is:" << unsigned(webots_object_msg.specific_type) << std::endl;
    // std::cout << " WbObject_Constants PEDESTRIAN is:" << unsigned(webots_objects_msgs::msg::WbObject_Constants::PEDESTRIAN) << std::endl;

    // 高斯噪声生成器设置
    double quaternion_w_mean = 0.0;
    double position_mean = 0.0;
    double quaternion_w_stddev = 0.03;
    double position_stddev = 0.05;
    std::default_random_engine generator1;
    std::default_random_engine generator2;
    std::normal_distribution<double> dist1(quaternion_w_mean, quaternion_w_stddev);
    std::normal_distribution<double> dist2(position_mean, position_stddev);
    quaternion_w_generator = generator1;
    position_generator = generator2;
    quaternion_w_dist = dist1;
    position_dist = dist2;
    is_noise_flag = 1;

    if (in_base_link) {
        Eigen::Vector3f base_pos(msg_cpy.pose.position.x, msg_cpy.pose.position.y, 0);
        const double *pose = main_robot_node_->getPose();
        const double *rotation = main_robot_node_->getOrientation();
        Eigen::Matrix4f matrix;
        matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
            pose[11], pose[12], pose[13], pose[14], pose[15];
        Eigen::Vector3f webots_world_pos = matrix.block<3, 3>(0, 0) * base_pos + matrix.block<3, 1>(0, 3);
        Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
        tf2::Quaternion tf_quaternion(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w());
        auto base_theta = tf2::getYaw(tf_quaternion);
        float webots_world_theta = base_theta + tf2::getYaw(msg_cpy.pose.orientation);

        msg_cpy.pose.position.x = webots_world_pos.x();
        msg_cpy.pose.position.y = webots_world_pos.y();
        msg_cpy.pose.orientation = tf2::toMsg(tf2::Quaternion(tf2::Vector3(0, 0, 1), webots_world_theta));

        if (msg_cpy.points.size() > 0) {
            for (auto &point : msg_cpy.points) {
                Eigen::Vector3f base_point(point.x, point.y, 0);
                Eigen::Vector3f webots_world_point = matrix.block<3, 3>(0, 0) * base_point + matrix.block<3, 1>(0, 3);
                // std::cout << "base_point: " << base_point.transpose()
                //           << " webots_world_point: " << webots_world_point.transpose() << std::endl;
                point.x = webots_world_point.x();
                point.y = webots_world_point.y();
            }
        }
    }

    if (msg_cpy.id == -1) {
        int box_id = findMaxId(obstable_prefix) + 1;
        msg_cpy.id = box_id;
    }

    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    std::string name = obstable_prefix + std::to_string(msg_cpy.id);
    bool not_exist = node_list_.find(name) == node_list_.end();
    lock.unlock();
    if (not_exist) {
        if (obstable_prefix == "BOX")
            addBoxObstacleToWb(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                               msg_cpy.pose.position.y, tf2::getYaw(msg_cpy.pose.orientation), msg_cpy.velocity,
                               obstable_specific_type);
        else if (obstable_prefix == "DBOX") {
            if (obstable_dynamic_type == "polygon") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points,
                                      obstable_specific_type);
            } else if (obstable_dynamic_type == "circle") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.radius,
                                      obstable_specific_type);
            } else if (obstable_dynamic_type == "line") {
                addDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                      msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity, msg_cpy.points,
                                      obstable_specific_type);
            }
        }
        updateNode(obstable_prefix, msg_cpy.id, msg_cpy);
    } else {
        if (obstable_prefix == "BOX")
            updateBoxObstacleToWb(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                  msg_cpy.pose.position.y, tf2::getYaw(msg_cpy.pose.orientation), msg_cpy.velocity,
                                  obstable_specific_type);
        else if (obstable_prefix == "DBOX") {
            if (obstable_dynamic_type == "polygon") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity,
                                         msg_cpy.points, obstable_specific_type);
            } else if (obstable_dynamic_type == "circle") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity,
                                         msg_cpy.radius, obstable_specific_type);
            } else if (obstable_dynamic_type == "line") {
                updateDynamicBoxObstacle(name, msg_cpy.size.x, msg_cpy.size.y, msg_cpy.size.z, msg_cpy.pose.position.x,
                                         msg_cpy.pose.position.y, obstable_dynamic_type, msg_cpy.velocity,
                                         msg_cpy.points, obstable_specific_type);
            }
        }
    }
}

void ObjectProcess::getObstacle(std::vector<std::string> prefix_list, webots_objects_msgs::msg::WbObjectList &webots_objects)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (std::find(prefix_list.begin(), prefix_list.end(), item.second.prefix) == prefix_list.end()) continue;
        webots_objects.objects.push_back(item.second.webots_object_msg);
    }
}
void ObjectProcess::getObstacle(int type, webots_objects_msgs::msg::WbObjectList &webots_objects)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (type != item.second.webots_object_msg.type) continue;
        webots_objects.objects.push_back(item.second.webots_object_msg);
        printNode(item.second);
    }
    lock.unlock();
}
void ObjectProcess::deleteAllObjects()
{
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        auto node_ptr = super_robot_->getFromId(item.second.webots_id);
        if (node_ptr) {
            node_ptr->remove();
        }
    }
    node_list_.clear();
}

bool ObjectProcess::updateAllObjectsPose()
{
    std::unique_lock<std::shared_mutex> lock(node_list_mutex_);
    const double *robot_pose_world = main_robot_node_->getPose();
    Eigen::Matrix4f robot_matrix_world;
    robot_matrix_world << robot_pose_world[0], robot_pose_world[1], robot_pose_world[2], robot_pose_world[3],
        robot_pose_world[4], robot_pose_world[5], robot_pose_world[6], robot_pose_world[7], robot_pose_world[8],
        robot_pose_world[9], robot_pose_world[10], robot_pose_world[11], robot_pose_world[12], robot_pose_world[13],
        robot_pose_world[14], robot_pose_world[15];
    for (auto it = node_list_.begin(); it != node_list_.end(); it++) {
        updateObjectPose(&it->second, robot_matrix_world);
    }
    return false;
}

bool ObjectProcess::updateObjectPose(NodeData *node_data, Eigen::Matrix4f &robot_matrix_world)
{
    // auto node_ptr = super_robot_->getFromId(node_data->webots_id);
    webots::Node *node_ptr;
    if (node_data->webots_id > 35000) {
        node_ptr = node_data->node_ptr;
    } else {
        node_ptr = super_robot_->getFromId(node_data->webots_id);
    }

    if (!node_ptr) return false;
    bool is_trailer_module = false;
    int trailer_webot_id = 0;

    // 牵引车子节点处理 如果对应的头车节点找不到，就返回
    const std::string prefix = "TrailerModule";
    size_t prefix_pos = node_data->def_name.find(prefix);
    // std::cout << "def_name:" << node_data->def_name << std::endl;

    if (!(prefix_pos == std::string::npos)) {  //找到前缀，说明是牵引车子节点
        is_trailer_module = true;
        std::string trailer_webot_id_str = node_data->def_name.substr(0, prefix_pos);
        // std::cout << "trailer_webots_id_str:" << trailer_webot_id_str << std::endl;
        trailer_webot_id = std::stoi(trailer_webot_id_str);
        if (super_robot_->getFromId(trailer_webot_id) == nullptr) {
            return false;
        }
    }

    // 世界坐标系
    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
        return false;  // 检查牵引车节点存在情况
    const double *pose_world = node_ptr->getPose();
    Eigen::Matrix4f matrix_world;
    matrix_world << pose_world[0], pose_world[1], pose_world[2], pose_world[3], pose_world[4], pose_world[5],
        pose_world[6], pose_world[7], pose_world[8], pose_world[9], pose_world[10], pose_world[11], pose_world[12],
        pose_world[13], pose_world[14], pose_world[15];
    Eigen::Quaternionf quaternion_world(matrix_world.block<3, 3>(0, 0));
    Eigen::Vector3f position_world(matrix_world.block<3, 1>(0, 3));

    node_data->webots_object_msg.pose.position.x = position_world[0];
    node_data->webots_object_msg.pose.position.y = position_world[1];
    node_data->webots_object_msg.pose.position.z = position_world[2];
    node_data->webots_object_msg.pose.orientation.x = quaternion_world.x();
    node_data->webots_object_msg.pose.orientation.y = quaternion_world.y();
    node_data->webots_object_msg.pose.orientation.z = quaternion_world.z();
    node_data->webots_object_msg.pose.orientation.w = quaternion_world.w();

    // 机器人坐标系
    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
        return false;  // 检查牵引车节点存在情况
    const double *pose = node_ptr->getPose(main_robot_node_);
    Eigen::Matrix4f matrix;
    matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
        pose[11], pose[12], pose[13], pose[14], pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    // std::cout << "bound size:" << boundingbox.size.transpose() << std::endl;
    // std::cout << "bound pos:" << boundingbox.translation.transpose() << std::endl;

    custom_msgs::msg::PerceptionObject object_msg;

    object_msg.pose.orientation.x = quaternion.x();
    object_msg.pose.orientation.y = quaternion.y();
    object_msg.pose.orientation.z = quaternion.z();
    object_msg.pose.orientation.w = quaternion.w();

    if (is_noise_flag == 1) {
        Eigen::Quaternionf middle_quaternion = quaternion;
        middle_quaternion.w() = quaternion.w() + quaternion_w_dist(quaternion_w_generator);
        middle_quaternion.normalize();
        object_msg.pose.orientation.x = middle_quaternion.x();
        object_msg.pose.orientation.y = middle_quaternion.y();
        object_msg.pose.orientation.z = middle_quaternion.z();
        object_msg.pose.orientation.w = middle_quaternion.w();
    }

    // std::cout << "qx y z w:" << quaternion.x() << " " <<quaternion.y() << " " << quaternion.z()
    // << " " << quaternion.w() << std::endl;

    double object_yaw = tf2::getYaw(object_msg.pose.orientation);
    double center_x = position[0] + node_data->bounding_box.translation.x() * cos(object_yaw);
    double center_y = position[1] + node_data->bounding_box.translation.x() * sin(object_yaw);

    if (is_noise_flag == 1) {
        center_x = center_x + position_dist(position_generator);
        center_y = center_y + position_dist(position_generator);
    }

    object_msg.pose.position.x = center_x;
    object_msg.pose.position.y = center_y;
    object_msg.pose.position.z = position[2] + node_data->bounding_box.translation.z();

    // 获取世界坐标系下的速度
    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
        return false;  // 检查牵引车节点存在情况
    const double *speed_world = node_ptr->getVelocity();

    // 将世界坐标系下的速度转换到机器人坐标系
    Eigen::Vector3f linear_velocity_world(speed_world[0], speed_world[1], speed_world[2]);
    Eigen::Vector3f angular_velocity_world(speed_world[3], speed_world[4], speed_world[5]);

    // 获取机器人在世界坐标系下的姿态
    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
        return false;  // 检查牵引车节点存在情况

    // const double *robot_pose_world = main_robot_node_->getPose();
    // Eigen::Matrix4f robot_matrix_world;
    // robot_matrix_world << robot_pose_world[0], robot_pose_world[1], robot_pose_world[2], robot_pose_world[3],
    //     robot_pose_world[4], robot_pose_world[5], robot_pose_world[6], robot_pose_world[7], robot_pose_world[8],
    //     robot_pose_world[9], robot_pose_world[10], robot_pose_world[11], robot_pose_world[12], robot_pose_world[13],
    //     robot_pose_world[14], robot_pose_world[15];

    // 计算机器人坐标系到世界坐标系的旋转矩阵
    Eigen::Matrix3f robot_rotation_world = robot_matrix_world.block<3, 3>(0, 0);

    // 将速度从世界坐标系转换到机器人坐标系
    Eigen::Vector3f linear_velocity_robot = robot_rotation_world.transpose() * linear_velocity_world;
    Eigen::Vector3f angular_velocity_robot = robot_rotation_world.transpose() * angular_velocity_world;

    // 设置转换后的速度到消息中
    object_msg.twist.linear.x = linear_velocity_robot.x();
    object_msg.twist.linear.y = linear_velocity_robot.y();
    object_msg.twist.linear.z = linear_velocity_robot.z();
    object_msg.twist.angular.x = angular_velocity_robot.x();
    object_msg.twist.angular.y = angular_velocity_robot.y();
    object_msg.twist.angular.z = angular_velocity_robot.z();

    // 计算机器人坐标系下的线速度和角速度
    double linear_speed = std::sqrt(object_msg.twist.linear.x * object_msg.twist.linear.x +
                                    object_msg.twist.linear.y * object_msg.twist.linear.y);
    double angular_speed = object_msg.twist.angular.z;
    // double dt = 0.5;
    // size_t judge = 20.0 / linear_speed / dt;
    // judge = std::min(judge, size_t(20.0 / dt));

    double cur_yaw = object_yaw;
    unav::Time cur_time = unav::Time::now();
    tf2::Quaternion cur_tf;
    cur_tf.setRPY(0, 0, cur_yaw);
    object_msg.path.emplace_back();
    object_msg.path.back().path.emplace_back();
    object_msg.path.back().path.back().header.stamp = cur_time;
    object_msg.path.back().path.back().pose.position.x = object_msg.pose.position.x;
    object_msg.path.back().path.back().pose.position.y = object_msg.pose.position.y;
    object_msg.path.back().path.back().pose.orientation.w = cur_tf.getW();
    object_msg.path.back().path.back().pose.orientation.x = cur_tf.getX();
    object_msg.path.back().path.back().pose.orientation.y = cur_tf.getY();
    object_msg.path.back().path.back().pose.orientation.z = cur_tf.getZ();

    // 记录发出第一帧数据的时间
    static unav::Time start_time = unav::Time::now();

    // 机器人相对世界坐标系的位置
    Eigen::Vector3f robot_position_world;
    robot_position_world = robot_matrix_world.block<3, 1>(0, 3);

    // 障碍物具体类型
    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
        return false;  // 检查牵引车节点存在情况
    Field *descrition_field = node_ptr->getField("description");
    if (!descrition_field) return false;
    std::string specific_type_str = descrition_field->getSFString();
    // std::cout << "obstacle specific type" << specific_type_str << std::endl;

    if (specific_type_str == "Pedestrian") {
        object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::PEDESTRIAN;
    } else if (specific_type_str == "Trailer") {
        object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::TRAILER;
    } else if (specific_type_str == "Shelves") {
        object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::SHELVES;
    } else {
        object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::CAR;
    }

    // 如果障碍物是牵引车，则额外发布子模块的信息
    if (specific_type_str == "Trailer") {
        // 获取子模块的节点信息
        if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
            return false;  // 检查牵引车节点存在情况
        Field *children_field = node_ptr->getField("children");
        int counts = children_field->getCount();
        int sub_module_index = 0;

        // trailer_node_list_.push_back(*node_data);

        for (int i = 0; i < counts; i++) {
            if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
                return false;  // 检查牵引车节点存在情况
            Node *child_node = children_field->getMFNode(i);
            std::string node_name = child_node->getDef();
            // std::cout << "node_name = " << node_name << "  i=" << i << std::endl;
            if (node_name == "TrailerModule") {
                // 读取牵引车头的webots id；
                int tractor_webot_id = node_ptr->getId();
                // 如果是自定义的牵引车子节点的proto，则获取封装的节点信息
                for (int i = 0; i < 4; i++) {
                    // 获取封装的children信息
                    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
                        return false;  // 检查牵引车节点存在情况
                    Field *tractor_children_field = child_node->getProtoField("children");
                    Node *tractor_module_node = tractor_children_field->getMFNode(i);
                    // Node *tractor_module_node = children_field->getMFNode(i);
                    // sub_module_index = std::stod(node_name.substr(14,1));
                    // std::cout << "node name = " << node_name << std::endl;
                    struct NodeData tractor_module_node_data;
                    tractor_module_node_data.node_ptr = tractor_module_node;

                    tractor_module_node_data.prefix = "Solid";
                    tractor_module_node_data.webots_id =
                        tractor_webot_id + 35000 + i;  // 牵引车子模块的webots id仅仅由车头决定

                    tractor_module_node_data.def_name_id = tractor_webot_id + 35000 + i;
                    tractor_module_node_data.output_id = tractor_webot_id + 35000 + i;

                    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
                        return false;  // 检查牵引车节点存在情况
                    const Field *bounding_object_field = tractor_module_node_data.node_ptr->getField("boundingObject");
                    if (bounding_object_field->getSFNode()) {
                        Field *bounding_object_size = bounding_object_field->getSFNode()->getField("size");
                        tractor_module_node_data.webots_object_msg.size.x = bounding_object_size->getSFVec3f()[0];
                        tractor_module_node_data.webots_object_msg.size.y = bounding_object_size->getSFVec3f()[1];
                        tractor_module_node_data.webots_object_msg.size.z = bounding_object_size->getSFVec3f()[2];
                    }

                    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
                        return false;  // 检查牵引车节点存在情况
                    const Field *translation_field = tractor_module_node_data.node_ptr->getField("translation");
                    if (translation_field->getSFVec3f()) {
                        tractor_module_node_data.webots_object_msg.pose.position.x = translation_field->getSFVec3f()[0];
                        tractor_module_node_data.webots_object_msg.pose.position.y = translation_field->getSFVec3f()[1];
                        tractor_module_node_data.webots_object_msg.pose.position.z = translation_field->getSFVec3f()[2];
                    }

                    tractor_module_node_data.webots_object_msg.type =
                        webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES;
                    tractor_module_node_data.perception_object_msg.oc.classification =
                        custom_msgs::msg::ObjectClassification_Constants::SHELVES;
                    tractor_module_node_data.def_name =
                        std::to_string(node_data->webots_id) + "TrailerModule" + std::to_string(i);
                    if (is_trailer_module && (super_robot_->getFromId(trailer_webot_id) == nullptr))
                        return false;  // 检查牵引车节点存在情况
                    updateObjectPose_Tractor_moudle(&tractor_module_node_data, node_data);
                }
            }
        }
    }

    // 障碍物真实路径
    // 读取 customData 数据
    Field *customdata_field = node_ptr->getField("customData");

    // 没读取到数据就只发图形消息
    if (!customdata_field) {
        object_msg.id = node_data->output_id;
        object_msg.size.x = node_data->bounding_box.size.x();
        object_msg.size.y = node_data->bounding_box.size.y();
        object_msg.size.z = node_data->bounding_box.size.z();
        node_data->perception_object_msg = object_msg;
        return true;
    }
    if (!customdata_field) return false;
    std::string future_position_str = customdata_field->getSFString();
    // 转换成二维数组
    std::vector<std::vector<double>> future_positions;
    future_positions = parseSemicolonGroups(future_position_str);

    std::size_t rows = linear_speed < 1e-3 ? 0 : future_positions.size();  // 发布的未来路径的数量
    Eigen::Vector3f future_position_world;
    Eigen::Vector3f future_position_robot;

    unav::Time future_time;

    // 机器人相对世界的姿态四元数
    Eigen::Quaternionf quaternion2(robot_rotation_world);

    // 机器人相对世界的信息
    custom_msgs::msg::PerceptionObject object_msg2;
    object_msg2.pose.orientation.x = quaternion2.x();
    object_msg2.pose.orientation.y = quaternion2.y();
    object_msg2.pose.orientation.z = quaternion2.z();
    object_msg2.pose.orientation.w = quaternion2.w();
    // 机器人相对于世界系的偏航角
    double robot_yaw = tf2::getYaw(object_msg2.pose.orientation);

    for (int i = 1; i < rows; ++i) {
        // 障碍物相对于世界系的偏航角
        cur_yaw = future_positions[i][3];
        // 直接获取预测点的时间戳
        future_time = unav::Time(future_positions[i][4]);

        cur_tf.setRPY(0, 0, cur_yaw - robot_yaw);
        // 预测的障碍物在世界坐标系下的位置
        future_position_world(0) = future_positions[i][0];
        future_position_world(1) = future_positions[i][1];
        future_position_world(2) = future_positions[i][2];

        // 障碍物相对于机器人坐标原点的位置矢量在世界坐标系的投影
        future_position_world = future_position_world - robot_position_world;
        // 转换到机器人坐标系发布(机器人静止时准确预测)
        future_position_robot = robot_rotation_world.transpose() * future_position_world;
        // future_position_robot = future_position_world;

        object_msg.path.back().path.emplace_back();
        object_msg.path.back().path.back().header.stamp = future_time;
        object_msg.path.back().path.back().pose.position.x =
            future_position_robot[0] + node_data->bounding_box.translation.x() * cos(object_yaw);
        object_msg.path.back().path.back().pose.position.y =
            future_position_robot[1] + node_data->bounding_box.translation.x() * sin(object_yaw);
        object_msg.path.back().path.back().pose.orientation.w = cur_tf.getW();
        object_msg.path.back().path.back().pose.orientation.x = cur_tf.getX();
        object_msg.path.back().path.back().pose.orientation.y = cur_tf.getY();
        object_msg.path.back().path.back().pose.orientation.z = cur_tf.getZ();
    }
    object_msg.id = node_data->output_id;
    object_msg.size.x = node_data->bounding_box.size.x();
    object_msg.size.y = node_data->bounding_box.size.y();
    object_msg.size.z = node_data->bounding_box.size.z();
    // object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::CAR;
    node_data->perception_object_msg = object_msg;
    return true;
}

// 障碍物子更新模块
bool ObjectProcess::updateObjectPose_Tractor_moudle(NodeData *node_data, NodeData *parent_node_data)
{
    // auto node_ptr = super_robot_->getFromId(node_data->webots_id);
    // if (!node_ptr) return false;
    auto node_ptr = node_data->node_ptr;

    std::vector<const Node *> bounding_object_node_vec;  // bounding_object 对象的指针
    std::vector<const Node *> box_node_vec;              // 需要检测的box的指针和对应的bounding_object

    findBoundingObject(node_ptr, bounding_object_node_vec);
    for (auto &bounding_object_node : bounding_object_node_vec) {
        if (!bounding_object_node) continue;
        std::vector<const Node *> targets;
        findBoxes(bounding_object_node, targets);
        for (auto &target : targets) {
            box_node_vec.push_back(target);
        }
    }

    std::vector<WbBoundingObject> bounding_object_vec;  // 所有box 都转到 目标node坐标系下
    for (auto &obj : box_node_vec) {
        Eigen::Isometry3f transform = Eigen::Isometry3f::Identity();
        auto ptr = obj->getParentNode();
        while (ptr != node_ptr) {
            const Field *translation_field = ptr->getField("translation");
            if (translation_field) {
                const Field *rotation_field = ptr->getField("rotation");
                double ax = rotation_field->getSFRotation()[0];
                double ay = rotation_field->getSFRotation()[1];
                double az = rotation_field->getSFRotation()[2];
                double angle = rotation_field->getSFRotation()[3];
                double qx = ax * sin(angle * 0.5);
                double qy = ay * sin(angle * 0.5);
                double qz = az * sin(angle * 0.5);
                double qw = cos(angle * 0.5);

                transform.rotate(Eigen::Quaternionf(qw, qx, qy, qz));
                transform.pretranslate(Eigen::Vector3f(translation_field->getSFVec3f()[0],
                                                       translation_field->getSFVec3f()[1],
                                                       translation_field->getSFVec3f()[2]));
            }
            ptr = ptr->getParentNode();
        }

        WbBoundingObject bo;
        bo.type = "box";
        const Field *size_field = obj->getField("size");
        bo.size.x() = size_field->getSFVec3f()[0];
        bo.size.y() = size_field->getSFVec3f()[1];
        bo.size.z() = size_field->getSFVec3f()[2];
        bo.transform = transform;
        bounding_object_vec.push_back(bo);
    }
    auto boundingbox = extractBoundingBox(bounding_object_vec);  // 提取最大的包围框

    // 世界坐标系
    const double *pose_world = node_ptr->getPose();
    Eigen::Matrix4f matrix_world;
    matrix_world << pose_world[0], pose_world[1], pose_world[2], pose_world[3], pose_world[4], pose_world[5],
        pose_world[6], pose_world[7], pose_world[8], pose_world[9], pose_world[10], pose_world[11], pose_world[12],
        pose_world[13], pose_world[14], pose_world[15];
    Eigen::Quaternionf quaternion_world(matrix_world.block<3, 3>(0, 0));
    Eigen::Vector3f position_world(matrix_world.block<3, 1>(0, 3));

    node_data->webots_object_msg.pose.position.x = position_world[0];
    node_data->webots_object_msg.pose.position.y = position_world[1];
    node_data->webots_object_msg.pose.position.z = position_world[2];
    node_data->webots_object_msg.pose.orientation.x = quaternion_world.x();
    node_data->webots_object_msg.pose.orientation.y = quaternion_world.y();
    node_data->webots_object_msg.pose.orientation.z = quaternion_world.z();
    node_data->webots_object_msg.pose.orientation.w = quaternion_world.w();

    // 机器人坐标系
    const double *pose = node_ptr->getPose(main_robot_node_);
    Eigen::Matrix4f matrix;
    matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
        pose[11], pose[12], pose[13], pose[14], pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    // std::cout << "bound size:" << boundingbox.size.transpose() << std::endl;
    // std::cout << "bound pos:" << boundingbox.translation.transpose() << std::endl;

    custom_msgs::msg::PerceptionObject object_msg;

    object_msg.pose.orientation.x = quaternion.x();
    object_msg.pose.orientation.y = quaternion.y();
    object_msg.pose.orientation.z = quaternion.z();
    object_msg.pose.orientation.w = quaternion.w();

    if (is_noise_flag == 1) {
        Eigen::Quaternionf middle_quaternion = quaternion;
        middle_quaternion.w() = quaternion.w() + quaternion_w_dist(quaternion_w_generator);
        middle_quaternion.normalize();
        object_msg.pose.orientation.x = middle_quaternion.x();
        object_msg.pose.orientation.y = middle_quaternion.y();
        object_msg.pose.orientation.z = middle_quaternion.z();
        object_msg.pose.orientation.w = middle_quaternion.w();
    }

    // std::cout << "qx y z w:" << quaternion.x() << " " <<quaternion.y() << " " << quaternion.z()
    // << " " << quaternion.w() << std::endl;

    // std::cout << "boundingbox x:" << boundingbox.translation.x() << "  y:" << boundingbox.translation.y() << std::endl;

    double object_yaw = tf2::getYaw(object_msg.pose.orientation);
    double center_x = position[0] + boundingbox.translation.x() * cos(object_yaw);
    double center_y = position[1] + boundingbox.translation.x() * sin(object_yaw);

    if (is_noise_flag == 1) {
        center_x = center_x + position_dist(position_generator);
        center_y = center_y + position_dist(position_generator);
    }

    object_msg.pose.position.x = center_x;
    object_msg.pose.position.y = center_y;
    object_msg.pose.position.z = position[2] + boundingbox.translation.z();

    // 获取世界坐标系下的速度
    const double *speed_world = node_ptr->getVelocity();

    // 将世界坐标系下的速度转换到机器人坐标系
    Eigen::Vector3f linear_velocity_world(speed_world[0], speed_world[1], speed_world[2]);
    Eigen::Vector3f angular_velocity_world(speed_world[3], speed_world[4], speed_world[5]);

    // 获取机器人在世界坐标系下的姿态
    const double *robot_pose_world = main_robot_node_->getPose();
    Eigen::Matrix4f robot_matrix_world;
    robot_matrix_world << robot_pose_world[0], robot_pose_world[1], robot_pose_world[2], robot_pose_world[3],
        robot_pose_world[4], robot_pose_world[5], robot_pose_world[6], robot_pose_world[7], robot_pose_world[8],
        robot_pose_world[9], robot_pose_world[10], robot_pose_world[11], robot_pose_world[12], robot_pose_world[13],
        robot_pose_world[14], robot_pose_world[15];

    // 计算机器人坐标系到世界坐标系的旋转矩阵
    Eigen::Matrix3f robot_rotation_world = robot_matrix_world.block<3, 3>(0, 0);

    // 将速度从世界坐标系转换到机器人坐标系
    Eigen::Vector3f linear_velocity_robot = robot_rotation_world.transpose() * linear_velocity_world;
    Eigen::Vector3f angular_velocity_robot = robot_rotation_world.transpose() * angular_velocity_world;

    // 设置转换后的速度到消息中
    object_msg.twist.linear.x = linear_velocity_robot.x();
    object_msg.twist.linear.y = linear_velocity_robot.y();
    object_msg.twist.linear.z = linear_velocity_robot.z();
    object_msg.twist.angular.x = angular_velocity_robot.x();
    object_msg.twist.angular.y = angular_velocity_robot.y();
    object_msg.twist.angular.z = angular_velocity_robot.z();

    // 计算机器人坐标系下的线速度和角速度
    double linear_speed = std::sqrt(object_msg.twist.linear.x * object_msg.twist.linear.x +
                                    object_msg.twist.linear.y * object_msg.twist.linear.y);
    double angular_speed = object_msg.twist.angular.z;

    double cur_yaw = object_yaw;
    unav::Time cur_time = unav::Time::now();
    tf2::Quaternion cur_tf;
    cur_tf.setRPY(0, 0, cur_yaw);
    object_msg.path.emplace_back();
    object_msg.path.back().path.emplace_back();
    object_msg.path.back().path.back().header.stamp = cur_time;
    object_msg.path.back().path.back().pose.position.x = object_msg.pose.position.x;
    object_msg.path.back().path.back().pose.position.y = object_msg.pose.position.y;
    object_msg.path.back().path.back().pose.orientation.w = cur_tf.getW();
    object_msg.path.back().path.back().pose.orientation.x = cur_tf.getX();
    object_msg.path.back().path.back().pose.orientation.y = cur_tf.getY();
    object_msg.path.back().path.back().pose.orientation.z = cur_tf.getZ();

    object_msg.id = node_data->output_id;
    object_msg.size.x = boundingbox.size.x();
    object_msg.size.y = boundingbox.size.y();
    object_msg.size.z = boundingbox.size.z();
    // std::cout << "It run to here" << std::endl;
    object_msg.oc.classification = custom_msgs::msg::ObjectClassification_Constants::SHELVES;
    // std::cout << "obj_oc:" << std::to_string(object_msg.oc.classification) << std::endl;
    node_data->perception_object_msg = object_msg;
    // std::cout << "node_data_oc:" << std::to_string(node_data->perception_object_msg.oc.classification) << std::endl;

    // 拖车模块的名字由 车头名字+模块编号组成
    std::string parent_def_name = parent_node_data->def_name;
    std::string def_name = parent_def_name + node_data->def_name;

    node_list_.insert({def_name, *node_data});
    // trailer_node_list_.push_back(*node_data);
    return true;
}

void ObjectProcess::setRobotPose(double pos_x, double pos_y, double pos_theta)
{
    Field *translation = main_robot_node_->getField("translation");
    double pos_values[3] = {pos_x, pos_y, 0.5};
    translation->setSFVec3f(pos_values);

    Field *rotation = main_robot_node_->getField("rotation");
    double rotation_values[4] = {0.0, 0.0, 1.0, pos_theta};
    rotation->setSFRotation(rotation_values);
}

geometry_msgs::msg::Pose ObjectProcess::getRobotPose()
{
    const double *car_pose = main_robot_node_->getPose();
    Eigen::Matrix4f matrix;
    matrix << car_pose[0], car_pose[1], car_pose[2], car_pose[3], car_pose[4], car_pose[5], car_pose[6], car_pose[7],
        car_pose[8], car_pose[9], car_pose[10], car_pose[11], car_pose[12], car_pose[13], car_pose[14], car_pose[15];
    Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
    Eigen::Vector3f position(matrix.block<3, 1>(0, 3));

    geometry_msgs::msg::Pose pose;
    pose.position.x = position[0];
    pose.position.y = position[1];
    pose.position.z = position[2];
    pose.orientation.x = quaternion.x();
    pose.orientation.y = quaternion.y();
    pose.orientation.z = quaternion.z();
    pose.orientation.w = quaternion.w();
    return pose;
}

void ObjectProcess::getPerceptionObjects(custom_msgs::msg::PerceptionObjects &perception_objects, double pos_x_min,
                                         double pos_x_max, double pos_y_min, double pos_y_max)
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        if (item.second.perception_object_msg.pose.position.x < pos_x_min ||
            item.second.perception_object_msg.pose.position.x > pos_x_max ||
            item.second.perception_object_msg.pose.position.y < pos_y_min ||
            item.second.perception_object_msg.pose.position.y > pos_y_max) {
            continue;
        }
        perception_objects.objects.push_back(item.second.perception_object_msg);
    }
}

void ObjectProcess::printNodeList()
{
    std::shared_lock<std::shared_mutex> lock(node_list_mutex_);
    for (auto &item : node_list_) {
        printf("name:%s webots_id:%d output_id:%d size:%f %f %f pos:%f %f %f type:%d\n", item.first.c_str(),
               item.second.webots_id, item.second.output_id, item.second.webots_object_msg.size.x,
               item.second.webots_object_msg.size.y, item.second.webots_object_msg.size.z,
               item.second.webots_object_msg.pose.position.x, item.second.webots_object_msg.pose.position.y,
               item.second.webots_object_msg.pose.position.z, item.second.webots_object_msg.type);
    }
}

void ObjectProcess::printNode(const NodeData &item)
{
    printf("name:%s webots_id:%d output_id:%d size:%f %f %f pos:%f %f %f type:%d id:%d prefix:%s point_size:%ld\n",
           item.def_name.c_str(), item.webots_id, item.output_id, item.webots_object_msg.size.x,
           item.webots_object_msg.size.y, item.webots_object_msg.size.z, item.webots_object_msg.pose.position.x,
           item.webots_object_msg.pose.position.y, item.webots_object_msg.pose.position.z, item.webots_object_msg.type,
           item.webots_object_msg.id, item.webots_object_msg.prefix.c_str(), item.webots_object_msg.points.size());
}

std::vector<std::pair<std::string, std::string>> ObjectProcess::extractDefNameList()
{
    std::vector<std::pair<std::string, std::string>> defList;
    const webots::Node *root = super_robot_->getRoot();
    std::string content = root->exportString();
    // 正则表达式匹配 DEF 后面的标识符和 Solid/Robot
    std::regex defRegex(R"(DEF\s+(\w+)\s+(\w+))");
    std::smatch match;

    // 搜索所有匹配项
    for (std::sregex_iterator i = std::sregex_iterator(content.begin(), content.end(), defRegex);
         i != std::sregex_iterator(); ++i) {
        match = *i;
        std::string def_name = match[1].str();  // 提取 DEF 后的标识符

        for (auto &prefix_id : PrefixIdMap) {
            auto target_def_prefix = prefix_id.first;
            if (def_name.find(target_def_prefix) == 0) {
                defList.push_back({def_name, target_def_prefix});
                break;
            }
        }
    }

    // getMFNode API 会严重内存泄漏
    // webots::Node *root = super_robot_->getRoot();
    // const webots::Field *childrenField = root->getField("children");
    // int node_count = childrenField->getCount();
    // for (int i = 0; i < node_count; ++i) {
    //     const webots::Node *node = childrenField->getMFNode(i);
    //     try {
    //         if (node->getDef().empty()) continue;
    //     } catch (std::exception &e) {
    //         continue;
    //     }
    //     std::string def_name = node->getDef();
    //     for (auto &prefix_id : PrefixIdMap) {
    //         auto target_def_prefix = prefix_id.first;
    //         if (def_name.find(target_def_prefix) == 0) defList.push_back(def_name);
    //         break;
    //     }
    // }

    return defList;
}

// 解析形如 "0.1,0.5,0.4,0.9,1.5;" 的字符串
std::vector<std::vector<double>> ObjectProcess::parseSemicolonGroups(std::string &data)
{
    std::vector<std::vector<double>> future_positions;
    std::stringstream ss(data);
    std::string future_positions_str;

    // 1. 按分号切出每一组
    while (std::getline(ss, future_positions_str, ';')) {
        if (future_positions_str.empty()) continue;

        std::vector<double> group;
        std::stringstream gs(future_positions_str);
        std::string numStr;

        // 2. 按逗号切出每个数字
        while (std::getline(gs, numStr, ',')) {
            group.push_back(std::stod(numStr));
        }
        future_positions.push_back(std::move(group));
    }
    return future_positions;
}
