cmake_minimum_required(VERSION 3.10.0)
project(object_process)
find_package(uauto REQUIRED)
find_package(uautopilot_msgs REQUIRED)
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)
find_package(jsoncpp REQUIRED)
find_package(webots_objects_msgs REQUIRED)

include_directories(${WEBOTS_INCLUDES} include ${PCL_INCLUDE_DIRS} ${OpenCV_INCLUDE_DIRS})
add_executable(
  object_process
  my_controller.cpp
  object_process.cpp
  object_module.cpp
)

target_link_libraries(object_process ${WEBOTS_LIBRARIES} ${PCL_LIBRARIES} ${OpenCV_LIBS} jsoncpp_lib)

ament_target_dependencies(object_process uautopilot_msgs uauto webots_objects_msgs)

install(TARGETS object_process
        RUNTIME DESTINATION projects/controllers/object_process)

if(INSTALL_CONTROLLER_TO_SOURCE_DIR)
  add_custom_command(
    TARGET object_process
    POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/object_process
            ${CMAKE_CURRENT_SOURCE_DIR})
endif()
